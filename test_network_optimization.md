# 网络优化测试方案

## 当前优化措施

### 1. 激进的超时设置
```java
// HTTP客户端配置
.connectTimeout(10, TimeUnit.SECONDS)     // 连接超时：10秒
.readTimeout(20, TimeUnit.SECONDS)        // 读取超时：20秒  
.writeTimeout(10, TimeUnit.SECONDS)       // 写入超时：10秒
.callTimeout(30, TimeUnit.SECONDS)        // 总请求超时：30秒
.retryOnConnectionFailure(false)          // 禁用自动重试
```

### 2. CompletableFuture强制超时
```java
CompletableFuture.supplyAsync(() -> {
    return performSingleRequestInternal(url);
}).get(25, TimeUnit.SECONDS); // 25秒强制超时
```

### 3. 增加请求间隔
```java
private static final long REQUEST_INTERVAL_MS = 8000; // 8秒间隔
```

### 4. 减少批量处理
```java
// 定时任务每次只处理20个电影
movieInfoService.handleMissingMovieInfo(20);

// 定时任务间隔改为30分钟
@Scheduled(every = "30m")
```

### 5. 减少重试次数
```java
return fetchHtmlContentWithRetry(url, 2); // 最多重试2次
```

## 测试步骤

### 1. 手动测试小批量处理
```bash
# 启动应用
./mvnw quarkus:dev

# 在另一个终端监控日志
tail -f logs/app.log | grep -E "(电影信息处理|Thread blocked|429|timeout)"
```

### 2. 测试单个电影处理
创建一个测试端点来处理单个电影：

```java
@GET
@Path("/test-single-movie/{code}")
public Response testSingleMovie(@PathParam("code") String code) {
    try {
        Movie movie = Movie.find("code", code).firstResult();
        if (movie == null) {
            return Response.status(404).entity("Movie not found").build();
        }
        
        long startTime = System.currentTimeMillis();
        movieInfoExtractionService.extractJAMovieInfoAndSave(movie);
        long endTime = System.currentTimeMillis();
        
        return Response.ok()
            .entity(String.format("处理完成，耗时: %d ms", endTime - startTime))
            .build();
    } catch (Exception e) {
        return Response.status(500)
            .entity("处理失败: " + e.getMessage())
            .build();
    }
}
```

### 3. 监控关键指标

#### 线程阻塞监控
```bash
# 监控线程阻塞警告
tail -f logs/app.log | grep "Thread blocked"
```

#### 网络错误监控  
```bash
# 监控429和超时错误
tail -f logs/app.log | grep -E "(429|timeout|connection)"
```

#### 处理时间监控
```bash
# 监控单个电影处理时间
tail -f logs/app.log | grep "成功处理电影"
```

## 预期结果

### 成功指标
1. **无线程阻塞**: 不再出现 "Thread blocked" 警告
2. **429错误减少**: 通过8秒间隔大幅减少429错误
3. **处理时间可控**: 单个电影处理时间在2分钟内
4. **稳定运行**: 定时任务能够稳定完成，不会卡死

### 失败指标
1. **仍有线程阻塞**: 需要进一步减少超时时间
2. **429错误频繁**: 需要增加请求间隔
3. **处理时间过长**: 需要优化网络请求逻辑

## 故障排除

### 如果仍然出现线程阻塞

1. **进一步减少超时时间**
```properties
movie.info.connect.timeout.seconds=5
movie.info.read.timeout.seconds=10
movie.info.call.timeout.seconds=15
```

2. **增加CompletableFuture超时**
```java
.get(15, TimeUnit.SECONDS); // 减少到15秒
```

3. **增加请求间隔**
```properties
movie.info.request.interval.ms=15000  # 15秒间隔
```

### 如果429错误仍然频繁

1. **大幅增加请求间隔**
```properties
movie.info.request.interval.ms=20000  # 20秒间隔
```

2. **减少批量处理数量**
```java
movieInfoService.handleMissingMovieInfo(10); // 每次只处理10个
```

3. **增加定时任务间隔**
```java
@Scheduled(every = "2h") // 2小时执行一次
```

### 如果网络仍然不稳定

1. **添加更多重试逻辑**
```java
// 在网络错误后等待更长时间
if (isNetworkError(e)) {
    Thread.sleep(10000); // 等待10秒
}
```

2. **考虑使用代理或更换网络**

3. **添加断路器模式**
```java
// 连续失败多次后暂停处理
if (consecutiveFailures > 5) {
    logger.warn("连续失败过多，暂停处理30分钟");
    Thread.sleep(30 * 60 * 1000);
    consecutiveFailures = 0;
}
```

## 配置调优建议

### 保守配置（稳定优先）
```properties
movie.info.request.interval.ms=15000
movie.info.max.retries=1
movie.info.connect.timeout.seconds=5
movie.info.read.timeout.seconds=10
movie.info.call.timeout.seconds=15
```

### 平衡配置（当前使用）
```properties
movie.info.request.interval.ms=8000
movie.info.max.retries=2
movie.info.connect.timeout.seconds=10
movie.info.read.timeout.seconds=20
movie.info.call.timeout.seconds=30
```

### 激进配置（速度优先，风险较高）
```properties
movie.info.request.interval.ms=5000
movie.info.max.retries=3
movie.info.connect.timeout.seconds=15
movie.info.read.timeout.seconds=30
movie.info.call.timeout.seconds=45
```

## 总结

通过以上优化措施，我们从多个层面解决了线程阻塞问题：

1. **HTTP层面**: 激进的超时设置
2. **应用层面**: CompletableFuture强制超时
3. **业务层面**: 减少批量处理，增加间隔
4. **监控层面**: 详细的时间和错误监控

这个方案应该能够有效避免线程长时间阻塞，同时保持合理的处理效率。
