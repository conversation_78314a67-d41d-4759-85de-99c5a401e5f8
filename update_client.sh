#!/bin/bash

# This script updates AzureOpenAITranslationClient.java to use the new configuration system

FILE_PATH="/Users/<USER>/IdeaProjects/code-with-quarkus/src/main/java/org/acme/service/translation/AzureOpenAITranslationClient.java"

# Make a backup of the original file
cp "$FILE_PATH" "${FILE_PATH}.bak"

# Replace all occurrences of endpoint, deploymentName, apiVersion and apiKey with client configuration
sed -i '' '
# Replace URL construction in translateBatch
/String url = String.format("%s\/openai\/deployments\/%s\/chat\/completions?api-version=%s",/{
    N
    s/String url = String.format("%s\/openai\/deployments\/%s\/chat\/completions?api-version=%s",\n                    endpoint, deploymentName, apiVersion);/            \/\/ Select client and build URL for Azure OpenAI\n            ClientConfig client = selectClient();\n            String url = String.format("%s\/openai\/deployments\/%s\/chat\/completions?api-version=%s",\n                    client.getEndpoint(), client.getDeploymentId(), client.getApiVersion());/
}

# Replace API key header in translateBatch
s/                    .addHeader("api-key", apiKey)/                    .addHeader("api-key", client.getApiKey())/

# Replace URL construction in translate (first method)
/String url = String.format("%sopenai\/deployments\/%s\/chat\/completions?api-version=%s",/{
    N
    s/String url = String.format("%sopenai\/deployments\/%s\/chat\/completions?api-version=%s",\n                    endpoint, deploymentName, apiVersion);/            \/\/ Select client and build URL for Azure OpenAI\n            ClientConfig client = selectClient();\n            String url = String.format("%sopenai\/deployments\/%s\/chat\/completions?api-version=%s",\n                    client.getEndpoint(), client.getDeploymentId(), client.getApiVersion());/
}

# Replace API key header in translate (first method)
s/                    .addHeader("api-key", apiKey)/                    .addHeader("api-key", client.getApiKey())/

# Replace URL construction in translateAvText
/String url = String.format("%sopenai\/deployments\/%s\/chat\/completions?api-version=%s",/{
    N
    s/String url = String.format("%sopenai\/deployments\/%s\/chat\/completions?api-version=%s",\n                    endpoint, deploymentName, apiVersion);/            \/\/ Select client and build URL for Azure OpenAI\n            ClientConfig client = selectClient();\n            String url = String.format("%sopenai\/deployments\/%s\/chat\/completions?api-version=%s",\n                    client.getEndpoint(), client.getDeploymentId(), client.getApiVersion());/
}

# Replace API key header in translateAvText
s/                    .addHeader("api-key", apiKey)/                    .addHeader("api-key", client.getApiKey())/

# Replace URL construction in translate (second method)
/String url = String.format("%s\/openai\/deployments\/%s\/chat\/completions?api-version=%s",/{
    N
    s/String url = String.format("%s\/openai\/deployments\/%s\/chat\/completions?api-version=%s",\n                    endpoint, deploymentName, apiVersion);/            \/\/ Select client and build URL for Azure OpenAI\n            ClientConfig client = selectClient();\n            String url = String.format("%s\/openai\/deployments\/%s\/chat\/completions?api-version=%s",\n                    client.getEndpoint(), client.getDeploymentId(), client.getApiVersion());/
}

# Replace API key header in translate (second method)
s/                    .addHeader("api-key", apiKey)/                    .addHeader("api-key", client.getApiKey())/
' "$FILE_PATH"

echo "Updated $FILE_PATH to use the new client configuration system"
