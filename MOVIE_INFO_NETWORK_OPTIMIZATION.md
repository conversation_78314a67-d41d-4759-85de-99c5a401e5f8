# 电影信息网络优化方案

## 问题分析

### 原始问题
1. **线程阻塞**: `Thread blocked for 11927263 ms` - 网络请求超时导致线程长时间阻塞
2. **429限流**: 多线程并发请求导致目标网站返回429 Too Many Requests
3. **事务超时**: 长时间的网络请求导致数据库事务超时

### 错误堆栈分析
```
io.vertx.core.VertxException: Thread blocked
at okhttp3.internal.http2.Http2Stream.waitForIo$okhttp(Http2Stream.kt:714)
at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
```

## 解决方案

### 1. 放弃多线程，采用串行处理
由于目标网站有严格的限流机制，多线程会导致429错误，因此改为串行处理：

```java
// 串行处理，避免429限流
for (String code : missingCodes) {
    try {
        // 处理单个电影
        movieInfoExtractionService.extractJAMovieInfoAndSave(currentMovie);
    } catch (Exception e) {
        // 错误处理和重试逻辑
    }
}
```

### 2. 优化HTTP客户端配置

#### 增加超时时间
```java
private final OkHttpClient httpClient = new OkHttpClient.Builder()
    .connectTimeout(30, TimeUnit.SECONDS)     // 连接超时：30秒
    .readTimeout(60, TimeUnit.SECONDS)        // 读取超时：60秒
    .writeTimeout(30, TimeUnit.SECONDS)       // 写入超时：30秒
    .callTimeout(90, TimeUnit.SECONDS)        // 总请求超时：90秒
    .retryOnConnectionFailure(true)           // 连接失败重试
    .connectionPool(new okhttp3.ConnectionPool(3, 60, TimeUnit.SECONDS))
    .build();
```

#### 关键改进点
- **增加超时时间**: 避免网络慢导致的线程阻塞
- **设置总请求超时**: `callTimeout` 确保单个请求不会无限等待
- **减少连接池大小**: 避免过多并发连接

### 3. 实现智能重试机制

#### 多层重试策略
```java
private String fetchHtmlContentWithRetry(String url, int maxRetries) throws IOException {
    for (int retry = 0; retry <= maxRetries; retry++) {
        try {
            // 请求间隔控制
            waitForRequestInterval();
            
            String result = performSingleRequest(url);
            if (result != null) {
                return result;
            }
            
            // 递增等待时间：3s, 6s, 9s
            if (retry < maxRetries) {
                long waitTime = (retry + 1) * 3000;
                Thread.sleep(waitTime);
            }
            
        } catch (IOException e) {
            // 网络异常重试逻辑
        }
    }
}
```

#### 重试特性
- **最多3次重试**: 平衡成功率和性能
- **递增等待时间**: 3秒 → 6秒 → 9秒
- **429特殊处理**: 检测到429时触发重试
- **网络异常重试**: 超时、连接失败等自动重试

### 4. 请求间隔控制

#### 避免429限流
```java
private volatile long lastRequestTime = 0;
private static final long REQUEST_INTERVAL_MS = 2000; // 2秒间隔

private void waitForRequestInterval() {
    long currentTime = System.currentTimeMillis();
    long timeSinceLastRequest = currentTime - lastRequestTime;
    
    if (timeSinceLastRequest < REQUEST_INTERVAL_MS) {
        long waitTime = REQUEST_INTERVAL_MS - timeSinceLastRequest;
        Thread.sleep(waitTime);
    }
    
    lastRequestTime = System.currentTimeMillis();
}
```

#### 间隔控制特性
- **固定2秒间隔**: 确保请求频率不会过高
- **线程安全**: 使用 `volatile` 确保多线程安全
- **精确控制**: 基于实际时间间隔计算等待时间

### 5. 增强错误处理

#### 服务层错误处理
```java
public void handleMissingMovieInfo(int limit) {
    int successful = 0;
    int failed = 0;
    
    for (String code : missingCodes) {
        try {
            movieInfoExtractionService.extractJAMovieInfoAndSave(currentMovie);
            successful++;
        } catch (Exception e) {
            failed++;
            logger.errorf("处理电影代码 %s 时出错: %s", code, e.getMessage(), e);
            
            // 网络错误特殊处理
            if (isNetworkError(e)) {
                Thread.sleep(5000); // 网络错误等待5秒
            }
        }
    }
}
```

#### 错误处理特性
- **详细错误日志**: 记录每个失败的电影代码和原因
- **网络错误识别**: 识别超时、429、连接错误
- **自适应等待**: 网络错误时增加等待时间
- **进度跟踪**: 每10个电影打印处理进度

### 6. 配置优化

#### application.properties 配置
```properties
# 电影信息提取配置 - 避免429限流和网络超时
movie.info.request.interval.ms=2000
movie.info.max.retries=3
movie.info.connect.timeout.seconds=30
movie.info.read.timeout.seconds=60
movie.info.call.timeout.seconds=90

# 事务超时配置 (300秒)
quarkus.transaction-manager.default-transaction-timeout=300

# HTTP客户端全局配置
quarkus.http.timeout=120s
```

## 性能对比

### 优化前
- **处理方式**: 多线程并发
- **问题**: 429限流、线程阻塞、事务超时
- **稳定性**: 经常失败，需要手动重启

### 优化后
- **处理方式**: 串行处理 + 智能重试
- **优势**: 稳定可靠、自动恢复、详细监控
- **性能**: 虽然单线程，但成功率高，总体效率更好

## 使用建议

### 1. 监控和调优
```bash
# 查看处理日志
tail -f logs/app.log | grep "电影信息处理"

# 监控网络错误
tail -f logs/app.log | grep "429\|timeout\|connection"
```

### 2. 配置调整
根据实际网络情况调整参数：

```properties
# 网络较慢时增加间隔
movie.info.request.interval.ms=3000

# 网络不稳定时增加重试次数
movie.info.max.retries=5

# 网络很慢时增加超时时间
movie.info.read.timeout.seconds=120
```

### 3. 批量处理建议
```java
// 小批量处理，避免长时间运行
movieInfoService.handleMissingMovieInfo(50);  // 每次处理50个

// 定时任务分批处理
@Scheduled(every = "1h")
public void scheduledSync() {
    movieInfoService.handleMissingMovieInfo(100);
}
```

## 故障排除

### 常见问题

1. **仍然出现429错误**
   - 增加请求间隔：`movie.info.request.interval.ms=5000`
   - 减少批量大小：每次处理更少的电影

2. **网络超时**
   - 增加超时时间：`movie.info.read.timeout.seconds=120`
   - 检查网络连接稳定性

3. **事务超时**
   - 增加事务超时：`quarkus.transaction-manager.default-transaction-timeout=600`
   - 减少批量处理大小

### 监控指标
- **成功率**: 成功处理的电影数量 / 总数量
- **平均处理时间**: 单个电影的平均处理时间
- **重试率**: 需要重试的请求比例
- **429错误率**: 收到429响应的比例

## 总结

通过以下优化措施，成功解决了网络阻塞和429限流问题：

1. ✅ **串行处理**: 避免并发导致的429限流
2. ✅ **智能重试**: 3次重试 + 递增等待时间
3. ✅ **请求间隔**: 2秒固定间隔避免限流
4. ✅ **超时优化**: 合理的超时时间避免线程阻塞
5. ✅ **错误处理**: 详细的错误分类和处理策略
6. ✅ **配置化**: 可调整的参数适应不同网络环境

这个方案虽然放弃了多线程的速度优势，但大大提高了稳定性和成功率，更适合有限流限制的网络爬虫场景。
