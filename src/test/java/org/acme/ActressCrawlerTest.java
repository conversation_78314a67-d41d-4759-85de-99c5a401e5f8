package org.acme;

import java.io.IOException;

import org.acme.service.extractor.HeadlessBrowserExtractor;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.jupiter.api.Test;

import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;

@QuarkusTest
public class ActressCrawlerTest {

    @Inject
    HeadlessBrowserExtractor headlessBrowserExtractor;

    private static final String BASE_URL = "https://missav.ai/ja/actresses?page=";

    @Test
    public void main() {
        for (int page = 1; page <= 4; page++) {
            try {
                String html = fetchPage(page);
                if (page >1) {
                    System.out.println(html);
                }
                parseHtml(html);
                Thread.sleep(100); // 降低请求频率
            } catch (IOException | InterruptedException e) {
                System.err.println("页码 "+page+" 抓取失败: " + e.getMessage());
            }
        }
    }

    private String fetchPage(int page) throws IOException {
        return headlessBrowserExtractor.fetchHtmlOutsideTransaction(BASE_URL + page);
    }

    private static void parseHtml(String html) {
        Document doc = Jsoup.parse(html);
        Elements items = doc.select("div.space-y-4"); // 演员条目选择器

        for (Element item : items) {
            String name = item.select("h4.text-nord13").text();
            String works = item.select("p.text-nord10:contains(条影片)").text().replaceAll("\\D", "");
            String debut = item.select("p.text-nord10:contains(出道)").text().replaceAll("\\D", "");
            String avatar = item.select("img").attr("src");

            System.out.printf("姓名: %-20s 作品数: %-4s 出道年份: %-4s 头像: %s%n",
                    name, works, debut, avatar);
        }
    }
}
