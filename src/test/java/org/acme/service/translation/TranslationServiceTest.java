package org.acme.service.translation;

import org.junit.jupiter.api.Test;
import jakarta.inject.Inject;
import io.quarkus.test.junit.QuarkusTest;

@QuarkusTest
public class TranslationServiceTest {
    @Inject
    TranslationService translationService;

    @Test
    void testTranslateDescriptions() {
        translationService.translateDescriptions(10);
    }

    @Test
    void testTranslateTitles() {
        translationService.translateTitles(10);
    }

    @Test
    void testTriggerTranslation() {
        
    }
}
