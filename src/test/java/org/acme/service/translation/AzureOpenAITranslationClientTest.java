package org.acme.service.translation;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.acme.entity.MovieInfo;
import org.acme.enums.AzureAILanguageCode;
import org.acme.util.FileUtils;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jboss.logging.Logger;
import org.junit.jupiter.api.Test;

import dev.langchain4j.internal.Json;
import io.quarkus.panache.common.Page;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import org.acme.config.ProxyConfig;
import org.acme.config.ProxyConfig.ProxySource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@QuarkusTest
public class AzureOpenAITranslationClientTest {
    private static final Logger LOG = Logger.getLogger(AzureOpenAITranslationClientTest.class);
    List<String> allLanguages = Arrays.asList(
        "Russian", "Turkish", "Arabic", "Dutch", "Swedish", "Polish", "Danish", "Norwegian", "Finnish",
        "Traditional Chinese", "Simplified Chinese", "English", "Japanese", "Korean", "Malay", "Thai",
        "German", "French", "Vietnamese", "Indonesian", "Filipino", "Portuguese", "Hindi"
    );

    List<String> missedLanguages = Arrays.asList(
        "Russian", "Turkish", "Arabic", "Dutch", "Swedish", "Polish", "Danish", "Norwegian", "Finnish"
    );

    @Inject
    @ProxyEnabled
    ProxyAwareAzureOpenAIClient translationClient;
    
    @Inject
    ProxyConfig proxyConfig;

    @Test
    public void testTranslateText() {
        // Test simple text translation
        String sourceText = "Hello, how are you?";
        String sourceLanguage = "English";
        String targetLanguage = "Chinese";

        String result = translationClient.translateText(sourceText, sourceLanguage, targetLanguage);

        assertNotNull(result, "Translation result should not be null");
        assertFalse(result.isEmpty(), "Translation result should not be empty");
        assertFalse(result.startsWith("Translation failed"), "Translation should not fail");

        System.out.println("Original: " + sourceText);
        System.out.println("Translated: " + result);
    }

    public static Map<String, String> parseTranslationResponse(String translationResponse) {
        Map<String, String> translations = new LinkedHashMap<>();
        if (translationResponse == null || translationResponse.trim().isEmpty()) {
            return translations;
        }

        // This pattern matches lines that start with a language name, followed by a hyphen and space
        // and captures the language name and the translation text
        Pattern pattern = Pattern.compile("^([^-]+?)\\s*-\\s*(.+?)(?=\\n\\w+\\s*-|\\s*$)",
                Pattern.MULTILINE | Pattern.DOTALL);

        Matcher matcher = pattern.matcher(translationResponse);

        while (matcher.find()) {
            String language = matcher.group(1).trim();
            String translation = matcher.group(2).trim();
            // Only add if we have both language and translation
            if (!language.isEmpty() && !translation.isEmpty()) {
                translations.put(language, translation);
            }
        }

        return translations;
    }

    @Test
    public void testTranslateSubtitle() {
        // Test subtitle translation with formatting
        String subtitleText = "Asahi（18）可愛いスリムなアマチュア。膣内射精と剃毛後、吸うのをやめたとき、私は非常に緊張しました。その後、私は肛門貫通ATMを作り、それが現実になりました。";

        String sourceLanguage = "Japanese";
        ArrayList<String> targetLanguages = new ArrayList<>(Arrays.asList("Chinese", "English", "French", "German", "Italian", "Korean", "Portuguese", "Russian", "Spanish", "Vietnamese"));
        long startTime = System.currentTimeMillis();
        String result = translationClient.translate(subtitleText, sourceLanguage, targetLanguages, false, true);
        System.out.println("Original Subtitle:\n" + subtitleText);
        System.out.println("Translated Subtitle:\n" + result);

        Map<String, String> translations = parseTranslationResponse(result);
        translations.forEach((lang, text) -> System.out.println(lang + ": " + text));
        // 打印处理所用的时间
        long endTime = System.currentTimeMillis();
        System.out.println("Translation time: " + (endTime - startTime) + "ms");
    }

    @Test
    public void testTranslateLargeSubtitle() {
        // 设置要使用的代理模式: PROXY_POOL 或 LOCAL_VPN
        // 根据需要切换代理源
        proxyConfig.setProxySource(ProxySource.LOCAL_VPN); // 使用本地VPN代理
        // 或者使用: proxyConfig.setProxySource(ProxySource.PROXY_POOL); // 使用代理池
        LOG.info("Using proxy source: " + proxyConfig.getProxySource());
        
        List<MovieInfo> movieInfos = MovieInfo.find("language = ?1 and title is not null", "ja")  // Find by language
                                  .page(Page.ofSize(5))   // Get the first page with 5 items
                                  .list();

        List<String> titlesToTranslate = new ArrayList<>();
        for (MovieInfo movieInfo : movieInfos) {
            titlesToTranslate.add(movieInfo.title);
        }
        List<String> batchTitles = titlesToTranslate; // Since mock already gives 5

        LOG.infof("Starting batch translation for %d titles to %d languages.", batchTitles.size(), missedLanguages.size());
        long startTime = System.currentTimeMillis();
        // isSubtitle = false, isAVContext = true (as per your original test implies with movie titles)
        String batchResult = translationClient.translateBatch(batchTitles, "Japanese", missedLanguages, false, true);

        if (batchResult.startsWith("Translation failed:")) {
            LOG.error("Batch translation API call failed: " + batchResult);
            System.out.println("Batch translation API call failed: " + batchResult);
            return;
        }

        LOG.infof("Raw Batch Translation Response:\n%s", batchResult);

        List<Map<String, String>> parsedBatchTranslations = translationClient.parseBatchTranslationResponse(batchResult, batchTitles.size());

        if (parsedBatchTranslations.isEmpty() && !batchResult.trim().isEmpty()) {
            LOG.error("Failed to parse batch translation response, or response was empty despite API success.");
             System.out.println("Failed to parse batch translation response.");
        }
        long endTime = System.currentTimeMillis();
        System.out.println("Batch translation time: " + (endTime - startTime) + "ms");
        LOG.info("Parsed Batch Translations");
        for (int i = 0; i < parsedBatchTranslations.size(); i++) {
            String originalTitle = (i < batchTitles.size()) ? batchTitles.get(i) : "Original Title Missing (index out of bounds)";
            LOG.infof("Translations for Original Title %d: '%s'", (i + 1), originalTitle);
            System.out.printf("\n--- Translations for Original Title %d: '%s' ---\n", (i + 1), originalTitle);

            Map<String, String> translationsForOneTitle = parsedBatchTranslations.get(i);
            if (translationsForOneTitle.isEmpty()) {
                LOG.warnf("No translations found for original title: '{}'. This might indicate a parsing issue for this specific block or the LLM did not provide translations for it.", originalTitle);
                 System.out.println("  No translations parsed for this title.");
            }

        }
        LOG.info("End of Parsed Batch Translations");
    }


    @Test
    public void testTranslateWithRateLimit() throws InterruptedException {
        // 1. 准备数据
        List<MovieInfo> movieInfos = MovieInfo.find("language = ?1 and title is not null", "ja")
                                    .page(Page.ofSize(10))  // 测试10个标题
                                    .list();

        // map: title -> movieInfos
        Map<String, MovieInfo> titleToMovieMap = movieInfos.stream()
            .collect(Collectors.toMap(
                mi -> mi.title,  // 键：电影标题
                mi -> mi,        // 值：MovieInfo 对象
                (existing, replacement) -> existing,  // 如果键重复，保留已存在的
                LinkedHashMap::new  // 使用 LinkedHashMap 保持原始顺序
            ));

        List<String> titlesToTranslate = movieInfos.stream()
                .map(mi -> mi.title)
                .collect(Collectors.toList());

        LOG.infof("Starting rate-limited translation for %d titles to %d languages",
                titlesToTranslate.size(), missedLanguages.size());

        // 2. 创建线程池
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(3); // 控制并发数
        ExecutorService executor = Executors.newCachedThreadPool();
        CountDownLatch latch = new CountDownLatch(titlesToTranslate.size());
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);

        // 3. 记录开始时间
        long startTime = System.currentTimeMillis();
        LOG.info("Translation started at: " + new Date(startTime));

        List<MovieInfo> translatedMovies = new ArrayList<>();

        // 4. 调度任务
        for (int i = 0; i < titlesToTranslate.size(); i++) {
            final String title = titlesToTranslate.get(i);
            final int index = i + 1;

            // 每8秒调度一个任务
            scheduler.schedule(() -> {
                executor.submit(() -> {
                    try {
                        LOG.infof("Processing title %d/%d: %s",
                                index, titlesToTranslate.size(), title);

                        // 5. 执行翻译
                        String result = translationClient.translateBatch(
                                List.of(title),
                                "Japanese",
                                allLanguages,
                                false,
                                true
                        );

                        // 6. 处理结果
                        if (!result.startsWith("Translation failed:")) {
                            List<Map<String, String>> translations =
                                    translationClient.parseBatchTranslationResponse(result, 1);
                            LOG.infof("Translation success for '%s': %s",
                                    title, translations);

                            // 7. 保存翻译结果
                            for (Map<String, String> translation : translations) {
                                translation.forEach((lang, text) -> {
                                    MovieInfo movieInfo = titleToMovieMap.get(title);
                                    if (movieInfo != null && AzureAILanguageCode.fromDisplayName(lang) != null && StringUtils.isNotEmpty(text)) {
                                        // 创建新的 MovieInfo 对象用于保存翻译
                                        MovieInfo translatedMovie = new MovieInfo();
                                        // 复制原始对象的属性,除了 description 属性
                                        try {
                                            BeanUtils.copyProperties(translatedMovie, movieInfo);
                                            // 更新翻译后的标题
                                            translatedMovie.title = text;
                                            // 设置目标语言
                                            translatedMovie.language = AzureAILanguageCode.fromDisplayName(lang).getLanguageCode();
                                            // 保存翻译结果
                                            // translatedMovie.persist();
                                            translatedMovies.add(translatedMovie);
                                        } catch (Exception e) {
                                            LOG.error("Failed to copy properties", e);
                                            return;
                                        }
                                    }
                                });
                            }
                            successCount.incrementAndGet();
                        } else {
                            LOG.errorf("Translation failed for '%s': %s", title, result);
                            failCount.incrementAndGet();
                        }
                    } catch (Exception e) {
                        LOG.error("Error processing title: " + title, e);
                        failCount.incrementAndGet();
                    } finally {
                        latch.countDown();
                    }
                });
            }, i * 8, TimeUnit.SECONDS);  // 每8秒一个任务
        }

        // 7. 等待所有任务完成
        latch.await();
        long endTime = System.currentTimeMillis();
        System.out.println(Json.toJson(translatedMovies));

        // 8. 清理资源
        scheduler.shutdown();
        executor.shutdown();

        // 9. 输出统计信息
        LOG.info("\n=== Translation Summary ===");
        LOG.infof("Total titles: %d", titlesToTranslate.size());
        LOG.infof("Success: %d", successCount.get());
        LOG.infof("Failed: %d", failCount.get());
        LOG.infof("Total time: %d seconds", (endTime - startTime) / 1000);
        LOG.infof("Average time per title: %.2f seconds",
                (endTime - startTime) / 1000.0 / titlesToTranslate.size());

        // 10. 验证结果
        assertEquals(titlesToTranslate.size(), successCount.get() + failCount.get());
        if (failCount.get() > 0) {
            LOG.warn("Some translations failed. Check logs for details.");
        }
    }

    /**
     * 测试代理是否成功隐藏了本地IP地址
     * 依次测试直接连接和使用LOCAL_VPN代理，并比较返回的IP地址是否不同
     */
    @Test
    public void testProxyIPMasking() throws IOException {
        // 1. 先使用直接连接测试
        proxyConfig.setProxySource(ProxySource.PROXY_POOL);
        LOG.info("Using direct connection or proxy pool");
        
        // 创建一个默认的HttpClient (如果使用代理池，会使用代理池中的代理)
        OkHttpClient defaultClient = new OkHttpClient.Builder().build();
        String directIp = getPublicIp(defaultClient);
        LOG.info("IP address with direct connection or proxy pool: " + directIp);
        
        // 2. 再使用LOCAL_VPN代理测试
        proxyConfig.setProxySource(ProxySource.LOCAL_VPN);
        LOG.info("Using LOCAL_VPN proxy");
        
        // 在ProxyEnabledHttpClient中创建一个使用VPN代理的HttpClient
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        Proxy proxy = new Proxy(Proxy.Type.HTTP, 
            new InetSocketAddress("127.0.0.1", 10802)); // 本地VPN代理地址
        builder.proxy(proxy);
        
        String vpnIp = getPublicIp(builder.build());
        LOG.info("IP address with LOCAL_VPN proxy: " + vpnIp);
        
        // 3. 比较两个IP是否不同
        boolean isIpHidden = !directIp.equals(vpnIp);
        LOG.info("IP address successfully masked: " + isIpHidden);
        
        if (isIpHidden) {
            LOG.info("VPN proxy successfully hides your IP address!");
        } else {
            LOG.warn("VPN proxy did not change your IP address! This could indicate the proxy is not working properly.");
        }
        
        // 输出结果，但不强制断言测试结果
        // 因为在某些环境下，可能本来就是通过代理访问的
        System.out.println("============ PROXY TEST RESULT ============");
        System.out.println("Direct/Pool IP: " + directIp);
        System.out.println("VPN Proxy IP: " + vpnIp);
        System.out.println("IP successfully hidden: " + isIpHidden);
    }
    
    /**
     * 获取当前的公网IP地址
     * @param client 要使用的OkHttpClient
     * @return 公网IP地址字符串
     */
    private String getPublicIp(OkHttpClient client) throws IOException {
        // 使用ipify.org API获取公网IP
        Request request = new Request.Builder()
            .url("https://api.ipify.org")
            .get()
            .build();
            
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected response code: " + response);
            }
            
            return response.body().string().trim();
        }
    }
    
    @Test
    public void testTranslateDescriptionsWithRateLimit() throws InterruptedException {
        // 1. 准备数据 - 获取需要翻译的描述
        List<MovieInfo> movieInfos = MovieInfo.find("language = ?1 and description is not null", "ja")
                                    .page(Page.ofSize(10))  // 测试10条描述
                                    .list();

        // 创建 description MD5 -> List<MovieInfo> 的映射，用于后续更新
        Map<String, List<MovieInfo>> descMd5ToMoviesMap = new HashMap<>();
        for (MovieInfo movie : movieInfos) {
            if (movie.description != null) {
                String md5 = FileUtils.getMD5(movie.description);
                descMd5ToMoviesMap.computeIfAbsent(md5, k -> new ArrayList<>()).add(movie);
            }
        }

        // 准备需要翻译的描述列表（去重）
        List<String> uniqueDescriptions = movieInfos.stream()
                .map(mi -> mi.description)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        LOG.infof("Starting rate-limited description translation for %d unique descriptions to %d languages",
                uniqueDescriptions.size(), missedLanguages.size());

        // 2. 创建线程池
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(3);
        ExecutorService executor = Executors.newCachedThreadPool();
        CountDownLatch latch = new CountDownLatch(uniqueDescriptions.size());
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);

        // 记录开始时间
        long startTime = System.currentTimeMillis();
        LOG.info("Description translation started at: " + new Date(startTime));

        List<MovieInfo> translatedMovies = new ArrayList<>();

        // 3. 调度任务
        for (int i = 0; i < uniqueDescriptions.size(); i++) {
            final String description = uniqueDescriptions.get(i);
            final String md5Key = FileUtils.getMD5(description);
            final List<MovieInfo> relatedMovies = descMd5ToMoviesMap.get(md5Key);
            final int index = i + 1;

            // 每8秒调度一个任务
            scheduler.schedule(() -> {
                executor.submit(() -> {
                    try {
                        LOG.infof("Processing description %d/%d (affects %d movies)",
                                index, uniqueDescriptions.size(),
                                relatedMovies != null ? relatedMovies.size() : 0);

                        // 执行翻译
                        String result = translationClient.translateBatch(
                                List.of(description),
                                "Japanese",
                                missedLanguages,
                                false,  // 不是字幕
                                false   // 不是AV上下文
                        );

                        // 处理结果
                        if (!result.startsWith("Translation failed:")) {
                            List<Map<String, String>> translations =
                                    translationClient.parseBatchTranslationResponse(result, 1);
                            LOG.infof("Translation success for description %d", index);

                            // 保存翻译结果
                            for (Map<String, String> translation : translations) {
                                translation.forEach((lang, translatedText) -> {
                                    if (AzureAILanguageCode.fromDisplayName(lang) != null &&
                                        StringUtils.isNotEmpty(translatedText) &&
                                        relatedMovies != null) {

                                        // 为每个相关的 MovieInfo 创建翻译后的副本
                                        for (MovieInfo originalMovie : relatedMovies) {
                                            try {
                                                // 创建新的 MovieInfo 对象用于保存翻译
                                                MovieInfo translatedMovie = new MovieInfo();
                                                // 复制原始对象的属性
                                                BeanUtils.copyProperties(translatedMovie, originalMovie);
                                                // 更新翻译后的描述
                                                translatedMovie.description = translatedText;
                                                // 设置目标语言
                                                translatedMovie.language = AzureAILanguageCode.fromDisplayName(lang).getLanguageCode();
                                                // 添加到结果列表
                                                translatedMovies.add(translatedMovie);
                                            } catch (Exception e) {
                                                LOG.errorf("Failed to process translation for movie %s: %s",
                                                        originalMovie.id, e.getMessage());
                                            }
                                        }
                                    }
                                });
                            }
                            System.out.println(Json.toJson(translatedMovies));

                            successCount.incrementAndGet();
                        } else {
                            LOG.errorf("Translation failed for description %d: %s", index, result);
                            failCount.incrementAndGet();
                        }
                    } catch (Exception e) {
                        LOG.error("Error processing description " + index, e);
                        failCount.incrementAndGet();
                    } finally {
                        latch.countDown();
                    }
                });
            }, i * 8, TimeUnit.SECONDS);  // 每8秒一个任务
        }

        // 4. 等待所有任务完成
        latch.await();
        long endTime = System.currentTimeMillis();

        // 5. 批量保存翻译结果
        if (!translatedMovies.isEmpty()) {
            LOG.infof("Saving %d translated descriptions to database...", translatedMovies.size());
            MovieInfo.persist(translatedMovies);
        }

        // 6. 清理资源
        scheduler.shutdown();
        executor.shutdown();

        // 7. 输出统计信息
        LOG.info("\n=== Translation Summary ===");
        LOG.infof("Total unique descriptions: %d", uniqueDescriptions.size());
        LOG.infof("Total translated movies: %d", translatedMovies.size());
        LOG.infof("Success: %d", successCount.get());
        LOG.infof("Failed: %d", failCount.get());
        LOG.infof("Total time: %d seconds", (endTime - startTime) / 1000);
        LOG.infof("Average time per description: %.2f seconds",
                (endTime - startTime) / 1000.0 / uniqueDescriptions.size());

        // 8. 验证结果
        assertEquals(uniqueDescriptions.size(), successCount.get() + failCount.get());
        if (failCount.get() > 0) {
            LOG.warn("Some translations failed. Check logs for details.");
        }
    }
}
