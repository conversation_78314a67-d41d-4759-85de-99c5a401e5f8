package org.acme.service.translation;

import io.quarkus.test.junit.QuarkusTest;
import org.acme.entity.MovieInfo;
import org.acme.entity.TranslationTask;
import org.acme.util.ThreadPoolUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 改进的翻译服务测试类
 * 测试多线程处理和短事务功能
 */
@QuarkusTest
public class TaskBasedTranslationServiceTest {

    @Inject
    TaskBasedTranslationService translationService;

    @Mock
    private TranslationTaskManager taskManager;

    @Mock
    private AzureOpenAITranslationClient translationClient;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testThreadPoolUtilCreation() {
        // 测试线程池创建功能
        ThreadPoolExecutor pool = ThreadPoolUtil.createThreadPool(
            "test-pool", 2, 4, 10
        );
        
        assertNotNull(pool);
        assertEquals(2, pool.getCorePoolSize());
        assertEquals(4, pool.getMaximumPoolSize());
        
        // 清理资源
        ThreadPoolUtil.shutdownThreadPool(pool, 5);
    }

    @Test
    void testThreadPoolShutdown() throws InterruptedException {
        ThreadPoolExecutor pool = ThreadPoolUtil.createThreadPool(
            "test-shutdown", 1, 2, 5
        );
        
        // 提交一些任务
        for (int i = 0; i < 3; i++) {
            final int taskId = i;
            pool.submit(() -> {
                try {
                    Thread.sleep(100);
                    System.out.println("Task " + taskId + " completed");
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        // 测试安全关闭
        long startTime = System.currentTimeMillis();
        ThreadPoolUtil.shutdownThreadPool(pool, 2);
        long duration = System.currentTimeMillis() - startTime;
        
        assertTrue(pool.isShutdown());
        assertTrue(duration < 3000); // 应该在3秒内完成
    }

    @Test
    void testConcurrentTaskProcessing() {
        // 创建测试数据
        List<TranslationTask> tasks = createTestTasks();
        
        // 测试并发处理不会抛出异常
        assertDoesNotThrow(() -> {
            // 这里我们测试方法调用不会出错
            // 实际的翻译逻辑需要mock外部依赖
            translationService.triggerTranslation(10);
        });
    }

    @Test
    void testThreadPoolResourceManagement() {
        // 测试多个线程池的创建和销毁
        List<ThreadPoolExecutor> pools = new ArrayList<>();
        
        // 创建多个线程池
        for (int i = 0; i < 5; i++) {
            ThreadPoolExecutor pool = ThreadPoolUtil.createThreadPool(
                "test-pool-" + i, 1, 2, 5
            );
            pools.add(pool);
        }
        
        // 验证都创建成功
        assertEquals(5, pools.size());
        pools.forEach(pool -> assertFalse(pool.isShutdown()));
        
        // 批量关闭
        pools.forEach(pool -> ThreadPoolUtil.shutdownThreadPool(pool, 1));
        
        // 验证都已关闭
        pools.forEach(pool -> assertTrue(pool.isShutdown()));
    }

    @Test
    void testThreadNaming() {
        ThreadPoolExecutor pool = ThreadPoolUtil.createThreadPool(
            "named-test", 2, 2, 5
        );
        
        // 提交任务并检查线程名称
        pool.submit(() -> {
            String threadName = Thread.currentThread().getName();
            assertTrue(threadName.startsWith("named-test-"));
        });
        
        ThreadPoolUtil.shutdownThreadPool(pool, 2);
    }

    @Test
    void testErrorHandling() {
        // 测试错误情况下的处理
        assertDoesNotThrow(() -> {
            ThreadPoolUtil.shutdownThreadPool(null, 1); // null pool
        });
        
        ThreadPoolExecutor pool = ThreadPoolUtil.createThreadPool(
            "error-test", 1, 1, 1
        );
        pool.shutdown();
        
        // 对已关闭的线程池再次调用关闭
        assertDoesNotThrow(() -> {
            ThreadPoolUtil.shutdownThreadPool(pool, 1);
        });
    }

    /**
     * 创建测试用的翻译任务
     */
    private List<TranslationTask> createTestTasks() {
        List<TranslationTask> tasks = new ArrayList<>();
        
        for (int i = 0; i < 10; i++) {
            TranslationTask task = new TranslationTask();
            task.id = (long) i;
            task.contentId = "movie-" + (i % 3); // 3个不同的内容
            task.contentType = i % 2 == 0 ? "TITLE" : "DESCRIPTION";
            task.targetLanguage = "en"; // 简化测试，都用英语
            task.status = "PENDING";
            tasks.add(task);
        }
        
        return tasks;
    }

    /**
     * 性能测试 - 比较串行和并行处理的时间差异
     */
    @Test
    void testPerformanceImprovement() {
        int taskCount = 100;
        
        // 模拟串行处理时间
        long serialStart = System.currentTimeMillis();
        for (int i = 0; i < taskCount; i++) {
            simulateTranslationWork(10); // 每个任务10ms
        }
        long serialTime = System.currentTimeMillis() - serialStart;
        
        // 模拟并行处理时间
        ThreadPoolExecutor pool = ThreadPoolUtil.createThreadPool(
            "perf-test", 4, 8, 50
        );
        
        long parallelStart = System.currentTimeMillis();
        List<Runnable> tasks = new ArrayList<>();
        for (int i = 0; i < taskCount; i++) {
            tasks.add(() -> simulateTranslationWork(10));
        }
        
        // 提交所有任务
        tasks.forEach(pool::submit);
        
        // 等待完成
        pool.shutdown();
        try {
            pool.awaitTermination(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        long parallelTime = System.currentTimeMillis() - parallelStart;
        
        System.out.printf("串行处理时间: %dms, 并行处理时间: %dms%n", 
                         serialTime, parallelTime);
        
        // 并行处理应该明显更快（至少快50%）
        assertTrue(parallelTime < serialTime * 0.8, 
                  "并行处理应该比串行处理更快");
    }

    /**
     * 模拟翻译工作
     */
    private void simulateTranslationWork(long milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
