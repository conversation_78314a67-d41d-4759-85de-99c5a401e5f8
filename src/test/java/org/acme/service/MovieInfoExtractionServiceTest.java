package org.acme.service;

import org.acme.entity.Movie;
import org.junit.jupiter.api.Test;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;

@QuarkusTest
public class MovieInfoExtractionServiceTest {
    @Inject
    MovieInfoService movieInfoService;
    
    @Inject
    MovieInfoExtractionService movieInfoExtractionService;
    
    @Test
    void testHandleMissingMovieInfo() {
        movieInfoService.handleMissingMovieInfo(1);
    }

    @Test
    void testExtract123AvInfo() {
        movieInfoExtractionService.extract123AvInfo("PISM-020", "ja");
    }
}
