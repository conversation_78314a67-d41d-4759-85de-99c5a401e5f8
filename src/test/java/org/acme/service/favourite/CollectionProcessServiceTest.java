package org.acme.service.favourite;

import java.io.IOException;
import java.util.List;
import java.util.Set;

import jakarta.inject.Inject;
import okhttp3.Request;
import okhttp3.Response;

import org.acme.entity.Movie;
import org.acme.util.HttpClientUtils;
import org.jboss.logging.Logger;
import org.junit.jupiter.api.Test;
import io.quarkus.test.junit.QuarkusTest;
import org.acme.config.AuthCookieConfig;
import okhttp3.OkHttpClient;
import org.acme.producer.HttpClientProducer;

@QuarkusTest
public class CollectionProcessServiceTest {

    @Inject
    CollectionProcessService collectionProcessService;

    @Inject
    AuthCookieConfig authCookieConfig;

    private OkHttpClient httpClient;
    
    @Inject
    HttpClientProducer httpClientProducer;
    
    // Initialize httpClient in a method annotated with @Test or in a setup method
    private void initHttpClient() {
        if (httpClient == null) {
            httpClient = httpClientProducer.produceOkHttpClient();
        }
    }

    @Test
    void testAddIdsToFavourites() {
        collectionProcessService.removeIdsFromFavourites(Set.of(278558));
    }

    @Test
    void testHandleExistingFavorites() {
        collectionProcessService.handleExistingFavorites(1);
    }

    @Test
    void testExtractMovieFromElement() {
        // Initialize the HTTP client before using it
        initHttpClient();
        
        String url = "https://123av.com/ja/user/feed?page=5000";
        Request request = HttpClientUtils.createRequestBuilderWithReferer(url, authCookieConfig.getAuthCookie(), "https://123av.com/ja/user?sort=recent_update", false)
                        .build();

        Response response;
        try {
            response = httpClient.newCall(request).execute();
            String htmlContent = response.body().string();
            collectionProcessService.extractMovieFromElement(htmlContent);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}