cron.expr=*/5 * * * * ?

quarkus.http.port=8081
quarkus.http.test-port=0

# configure your datasource
quarkus.datasource.db-kind = postgresql
quarkus.datasource.username = dqy
#quarkus.datasource.password = ''
quarkus.datasource.jdbc.url = **********************************************
quarkus.datasource.jdbc.max-size=30
quarkus.datasource.jdbc.connection-timeout=30S

# quarkus.hibernate-orm.log.sql=true
# quarkus.hibernate-orm.format-sql=true
# quarkus.log.level=DEBUG

# drop and create the database at startup (use `update` to only update the schema)
quarkus.hibernate-orm.database.generation = update


quarkus.log.file.enable=true
quarkus.log.file.path=logs/app.log
quarkus.log.file.rotation.max-file-size=10M
quarkus.log.file.rotation.max-backup-index=5

quarkus.langchain4j.openai.api-key=sk-********************************
quarkus.langchain4j.openai.endpoint=https://yut-oai-eu.openai.azure.com/

# XAI configuration
xai.api-key=your-xai-api-key
xai.endpoint=https://api.xai.com/v1
xai.model-id=default-model

# Headless browser crawler configuration
crawler.useHeadlessBrowser=true
crawler.headless.useragent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
crawler.headless.timeout=30000
crawler.headless.slowMo=50

# Transaction timeout configuration (300 seconds for movie info processing)
quarkus.transaction-manager.default-transaction-timeout=300

# 电影信息提取配置 - 避免429限流和网络超时
movie.info.request.interval.ms=2000
movie.info.max.retries=3
movie.info.connect.timeout.seconds=30
movie.info.read.timeout.seconds=60
movie.info.call.timeout.seconds=90

# HTTP客户端全局配置
quarkus.http.timeout=120s
