# 优化配置示例 - 支持多线程翻译处理
# 使用方式: java -jar app.jar -Dquarkus.profile=optimized

# 数据库连接池配置 - 支持并发事务
quarkus.datasource.jdbc.max-size=25
quarkus.datasource.jdbc.min-size=8
quarkus.datasource.jdbc.initial-size=8
quarkus.datasource.jdbc.max-lifetime=PT30M
quarkus.datasource.jdbc.idle-timeout=PT10M
quarkus.datasource.jdbc.leak-detection-threshold=PT2M

# 事务配置 - 优化短事务处理
quarkus.transaction-manager.default-transaction-timeout=PT2M
quarkus.transaction-manager.enable-recovery=true

# HTTP客户端配置 - 翻译API调用优化
quarkus.rest-client.connect-timeout=30000
quarkus.rest-client.read-timeout=60000
quarkus.rest-client.max-pooled-per-route=10
quarkus.rest-client.max-pooled=50

# 日志配置 - 详细的翻译处理日志
quarkus.log.category."org.acme.service.translation".level=INFO
quarkus.log.category."org.acme.util.ThreadPoolUtil".level=INFO
quarkus.log.category."org.acme.scheduler".level=INFO

# 应用性能配置
quarkus.thread-pool.core-threads=8
quarkus.thread-pool.max-threads=30
quarkus.thread-pool.queue-size=1000

# 内存和GC优化建议（JVM参数）
# -Xmx3g -Xms1g
# -XX:+UseG1GC
# -XX:MaxGCPauseMillis=200
# -XX:+UseStringDeduplication
# -XX:+OptimizeStringConcat

# 翻译服务特定配置
translation.batch.size=50
translation.max.concurrent.contents=4
translation.max.concurrent.languages=3
translation.timeout.seconds=120
translation.retry.max.attempts=3

# 调度器配置 - 如果使用定时任务
quarkus.scheduler.enabled=true
quarkus.scheduler.thread-count=4

# 监控配置
quarkus.micrometer.enabled=true
quarkus.micrometer.export.prometheus.enabled=true

# 开发环境特定配置
%dev.quarkus.log.level=DEBUG
%dev.quarkus.datasource.jdbc.max-size=10
%dev.translation.batch.size=10

# 生产环境特定配置
%prod.quarkus.log.level=INFO
%prod.quarkus.datasource.jdbc.max-size=30
%prod.translation.batch.size=100
%prod.translation.max.concurrent.contents=6
%prod.translation.max.concurrent.languages=4
