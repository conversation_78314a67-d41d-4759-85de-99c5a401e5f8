<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XAI Text Analysis</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .card {
            background: #f9f9f9;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        textarea, input, select {
            width: 100%;
            padding: 8px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #e9f7ef;
            border-radius: 4px;
            border-left: 4px solid #4CAF50;
        }
        .loading {
            text-align: center;
            display: none;
        }
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f1f1f1;
            border-radius: 4px 4px 0 0;
        }
        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 14px 16px;
            transition: 0.3s;
            color: #333;
        }
        .tab button:hover {
            background-color: #ddd;
        }
        .tab button.active {
            background-color: #4CAF50;
            color: white;
        }
        .tabcontent {
            display: none;
            padding: 20px;
            border: 1px solid #ccc;
            border-top: none;
            border-radius: 0 0 4px 4px;
            animation: fadeEffect 1s;
        }
        @keyframes fadeEffect {
            from {opacity: 0;}
            to {opacity: 1;}
        }
    </style>
</head>
<body>
    <h1>XAI Text Analysis</h1>
    
    <div class="tab">
        <button class="tablinks active" onclick="openTab(event, 'Sentiment')">Sentiment Analysis</button>
        <button class="tablinks" onclick="openTab(event, 'Extract')">Information Extraction</button>
        <button class="tablinks" onclick="openTab(event, 'Categorize')">Text Categorization</button>
        <button class="tablinks" onclick="openTab(event, 'Keywords')">Keyword Generation</button>
    </div>
    
    <!-- Sentiment Analysis Tab -->
    <div id="Sentiment" class="tabcontent" style="display: block;">
        <div class="card">
            <h2>Sentiment Analysis</h2>
            <p>Analyze the sentiment of a text as positive, negative, or neutral.</p>
            
            <label for="sentiment-text">Text to analyze:</label>
            <textarea id="sentiment-text" rows="5" placeholder="Enter text to analyze sentiment..."></textarea>
            
            <button onclick="analyzeSentiment()">Analyze Sentiment</button>
            
            <div id="sentiment-loading" class="loading">
                <p>Analyzing sentiment...</p>
            </div>
            
            <div id="sentiment-result" class="result" style="display: none;"></div>
        </div>
    </div>
    
    <!-- Information Extraction Tab -->
    <div id="Extract" class="tabcontent">
        <div class="card">
            <h2>Information Extraction</h2>
            <p>Extract specific information from text.</p>
            
            <label for="extract-text">Text to extract from:</label>
            <textarea id="extract-text" rows="5" placeholder="Enter text to extract information from..."></textarea>
            
            <label for="extract-fields">Fields to extract (comma-separated):</label>
            <input type="text" id="extract-fields" placeholder="e.g., name, date, location, price">
            
            <button onclick="extractInformation()">Extract Information</button>
            
            <div id="extract-loading" class="loading">
                <p>Extracting information...</p>
            </div>
            
            <div id="extract-result" class="result" style="display: none;"></div>
        </div>
    </div>
    
    <!-- Text Categorization Tab -->
    <div id="Categorize" class="tabcontent">
        <div class="card">
            <h2>Text Categorization</h2>
            <p>Categorize text into predefined categories.</p>
            
            <label for="categorize-text">Text to categorize:</label>
            <textarea id="categorize-text" rows="5" placeholder="Enter text to categorize..."></textarea>
            
            <label for="categorize-categories">Categories (comma-separated):</label>
            <input type="text" id="categorize-categories" placeholder="e.g., sports, politics, technology, entertainment">
            
            <button onclick="categorizeText()">Categorize Text</button>
            
            <div id="categorize-loading" class="loading">
                <p>Categorizing text...</p>
            </div>
            
            <div id="categorize-result" class="result" style="display: none;"></div>
        </div>
    </div>
    
    <!-- Keyword Generation Tab -->
    <div id="Keywords" class="tabcontent">
        <div class="card">
            <h2>Keyword Generation</h2>
            <p>Generate keywords from text.</p>
            
            <label for="keywords-text">Text to generate keywords from:</label>
            <textarea id="keywords-text" rows="5" placeholder="Enter text to generate keywords from..."></textarea>
            
            <label for="keywords-count">Number of keywords:</label>
            <input type="number" id="keywords-count" min="1" max="20" value="5">
            
            <button onclick="generateKeywords()">Generate Keywords</button>
            
            <div id="keywords-loading" class="loading">
                <p>Generating keywords...</p>
            </div>
            
            <div id="keywords-result" class="result" style="display: none;"></div>
        </div>
    </div>
    
    <script>
        function openTab(evt, tabName) {
            var i, tabcontent, tablinks;
            tabcontent = document.getElementsByClassName("tabcontent");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }
            tablinks = document.getElementsByClassName("tablinks");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.className += " active";
        }
        
        function analyzeSentiment() {
            const text = document.getElementById("sentiment-text").value.trim();
            if (!text) {
                alert("Please enter text to analyze.");
                return;
            }
            
            document.getElementById("sentiment-loading").style.display = "block";
            document.getElementById("sentiment-result").style.display = "none";
            
            fetch('/api/text-analysis/sentiment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ text: text }),
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById("sentiment-loading").style.display = "none";
                document.getElementById("sentiment-result").style.display = "block";
                document.getElementById("sentiment-result").innerHTML = `<strong>Sentiment:</strong> ${data.sentiment}`;
            })
            .catch(error => {
                document.getElementById("sentiment-loading").style.display = "none";
                document.getElementById("sentiment-result").style.display = "block";
                document.getElementById("sentiment-result").innerHTML = `<strong>Error:</strong> ${error.message}`;
            });
        }
        
        function extractInformation() {
            const text = document.getElementById("extract-text").value.trim();
            const fieldsInput = document.getElementById("extract-fields").value.trim();
            
            if (!text || !fieldsInput) {
                alert("Please enter text and fields to extract.");
                return;
            }
            
            const fields = fieldsInput.split(',').map(field => field.trim());
            
            document.getElementById("extract-loading").style.display = "block";
            document.getElementById("extract-result").style.display = "none";
            
            fetch('/api/text-analysis/extract', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ text: text, fields: fields }),
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById("extract-loading").style.display = "none";
                document.getElementById("extract-result").style.display = "block";
                
                let resultHtml = "<strong>Extracted Information:</strong><br>";
                for (const [key, value] of Object.entries(data)) {
                    resultHtml += `<strong>${key}:</strong> ${value || 'Not found'}<br>`;
                }
                
                document.getElementById("extract-result").innerHTML = resultHtml;
            })
            .catch(error => {
                document.getElementById("extract-loading").style.display = "none";
                document.getElementById("extract-result").style.display = "block";
                document.getElementById("extract-result").innerHTML = `<strong>Error:</strong> ${error.message}`;
            });
        }
        
        function categorizeText() {
            const text = document.getElementById("categorize-text").value.trim();
            const categoriesInput = document.getElementById("categorize-categories").value.trim();
            
            if (!text || !categoriesInput) {
                alert("Please enter text and categories.");
                return;
            }
            
            const categories = categoriesInput.split(',').map(category => category.trim());
            
            document.getElementById("categorize-loading").style.display = "block";
            document.getElementById("categorize-result").style.display = "none";
            
            fetch('/api/text-analysis/categorize', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ text: text, categories: categories }),
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById("categorize-loading").style.display = "none";
                document.getElementById("categorize-result").style.display = "block";
                document.getElementById("categorize-result").innerHTML = `<strong>Category:</strong> ${data.category}`;
            })
            .catch(error => {
                document.getElementById("categorize-loading").style.display = "none";
                document.getElementById("categorize-result").style.display = "block";
                document.getElementById("categorize-result").innerHTML = `<strong>Error:</strong> ${error.message}`;
            });
        }
        
        function generateKeywords() {
            const text = document.getElementById("keywords-text").value.trim();
            const count = document.getElementById("keywords-count").value;
            
            if (!text) {
                alert("Please enter text to generate keywords from.");
                return;
            }
            
            document.getElementById("keywords-loading").style.display = "block";
            document.getElementById("keywords-result").style.display = "none";
            
            fetch('/api/text-analysis/keywords', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ text: text, count: parseInt(count) }),
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById("keywords-loading").style.display = "none";
                document.getElementById("keywords-result").style.display = "block";
                
                let resultHtml = "<strong>Keywords:</strong><br>";
                resultHtml += data.keywords.join(", ");
                
                document.getElementById("keywords-result").innerHTML = resultHtml;
            })
            .catch(error => {
                document.getElementById("keywords-loading").style.display = "none";
                document.getElementById("keywords-result").style.display = "block";
                document.getElementById("keywords-result").innerHTML = `<strong>Error:</strong> ${error.message}`;
            });
        }
    </script>
</body>
</html>
