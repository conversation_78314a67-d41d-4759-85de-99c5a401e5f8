package org.acme.util;

import java.io.FileWriter;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.net.ssl.HostnameVerifier;

import org.acme.config.ProxyConfig;
import org.jboss.logging.Logger;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import okhttp3.Credentials;
import okhttp3.Headers;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

@ApplicationScoped
public class MovieInfoExtractorUtil {

    private static final Logger logger = Logger.getLogger(MovieInfoExtractorUtil.class);

    private final ProxyConfig proxyConfig;

    @Inject
    public MovieInfoExtractorUtil(ProxyConfig proxyConfig) {
        this.proxyConfig = proxyConfig;
    }
    // 2. Make the client an instance variable - NO MORE STATIC
    private OkHttpClient client;
    private OkHttpClient clientWithProxy;


    @PostConstruct
    void initializeClients() {
        this.client = createHttpClient(false); // Create a client without proxy
        this.clientWithProxy = createHttpClient(true); // Create a client that uses proxy
    }


    // 随机用户代理列表
    private static final String[] USER_AGENTS = {
        // Chrome on macOS
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
        // Firefox on macOS
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/115.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/117.0",
        // Safari on macOS
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
        // Edge on macOS
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0"
    };

    /**
     * 获取随机用户代理
     * @return 随机选择的用户代理字符串
     */
    private static String getRandomUserAgent() {
        int index = (int) (Math.random() * USER_AGENTS.length);
        return USER_AGENTS[index];
    }


    /**
     * 创建HTTP客户端，可选择是否使用代理
     *
     * @param useProxy 是否使用代理
     * @return 配置好的OkHttpClient实例
     */
    private OkHttpClient createHttpClient(boolean useProxy) {
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS) // Increased for stability
                .readTimeout(10, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .followRedirects(true)
                .followSslRedirects(true);
        // You can add your interceptor here if needed

        // 添加SSL证书验证绕过，解决 PKIX path building failed 错误
        try {
            final TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public void checkServerTrusted(X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[]{};
                    }
                }
            };
            
            // 创建信任所有证书的 SSLContext
            final SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustAllCerts, new SecureRandom());
            
            // 为OkHttpClient创建 SSLSocketFactory
            final SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();
            
            // 设置OkHttpClient使用我们的SSLSocketFactory和TrustManager
            builder.sslSocketFactory(sslSocketFactory, (X509TrustManager) trustAllCerts[0]);
            builder.hostnameVerifier((hostname, session) -> true); // 不验证主机名
        } catch (Exception e) {
            logger.error("配置SSL证书绕过失败: " + e.getMessage(), e);
        }

        // The check is now safe, because proxyConfig is guaranteed to be injected
        // before this method is called via @PostConstruct
        if (useProxy && this.proxyConfig != null) {
            Optional<ProxyConfig.ProxyEntry> proxyOpt = this.proxyConfig.getNextProxy();
            if (proxyOpt.isPresent()) {
                ProxyConfig.ProxyEntry proxy = proxyOpt.get();
                logger.infof("配置代理: %s:%d", proxy.getHost(), proxy.getPort());

                Proxy httpProxy = new Proxy(Proxy.Type.HTTP,
                        new InetSocketAddress(proxy.getHost(), proxy.getPort()));

                builder.proxy(httpProxy);

                if (proxy.hasAuthentication()) {
                    builder.proxyAuthenticator((route, response) -> {
                        String credential = Credentials.basic(proxy.getUsername(), proxy.getPassword());
                        return response.request().newBuilder()
                                .header("Proxy-Authorization", credential)
                                .build();
                    });
                }
            } else {
                logger.warn("请求使用代理，但代理池为空，将使用直接连接");
            }
        }
        return builder.build();
    }

    // Language codes and names based on the HTML snippet
    private static final Map<String, String> LANGUAGES = new HashMap<String, String>() {{
        // Default language (base URL)
        put("", "繁體中文");
        // Other languages
        put("cn", "简体中文");
        put("en", "English");
        put("ja", "日本語");
        put("ko", "한국의");
        put("ms", "Melayu");
        put("th", "ไทย");
        put("de", "Deutsch");
        put("fr", "Français");
        put("vi", "Tiếng Việt");
        put("id", "Bahasa Indonesia");
        put("fil", "Filipino");
        put("pt", "Português");
    }};

    // Base URL
    private static final String BASE_URL = "https://missav.ai";

    /**
     * Extracts movie information for a specific language.
     *
     * @param dvdCode The DVD code of the movie
     * @param languageCode The language code (empty string for Traditional Chinese, "cn" for Simplified Chinese, etc.)
     * @return A map containing the extracted movie information
     * @throws IOException If an error occurs during extraction
     */
    public Map<String, Object> extractMovieInfoForLanguage(String dvdCode, String languageCode) throws IOException {
        return extractMovieInfoForLanguage(dvdCode, languageCode, null);
    }

    /**
     * Extracts movie information for a specific language with optional pre-fetched m3u8 info.
     *
     * @param dvdCode The DVD code of the movie
     * @param languageCode The language code to use (e.g., "en", "ja")
     * @param existingM3u8Info Optional pre-fetched m3u8 info (can be null)
     * @return A map containing the extracted movie information
     * @throws IOException If an error occurs during extraction
     */
    public Map<String, Object> extractMovieInfoForLanguage(String dvdCode, String languageCode, List<String> existingM3u8Info) throws IOException {
        String baseUrl = "https://missav.ai/";
        if (!languageCode.isEmpty()) {
            baseUrl += languageCode + "/";
        }
        String url = baseUrl + dvdCode;
        // Pass null for movieId since we don't know it at this point
        return extractMovieInfoFromUrl(url, null, existingM3u8Info);
    }

    /**
     * Extracts movie information for the default language (English).
     *
     * @param dvdCode The DVD code of the movie
     * @return A map containing the extracted movie information
     * @throws IOException If an error occurs during extraction
     */
    public Map<String, Object> extractMovieInfo(String dvdCode) throws IOException {
        return extractMovieInfoForLanguage(dvdCode, "en");
    }

    /**
     * Internal method to extract movie information from a specified URL.
     *
     * @param url The full URL to extract information from
     * @param movieId The ID of the movie in the database, can be null if not known
     * @return A map containing the extracted movie information
     * @throws IOException If an error occurs during extraction
     */
    /**
     * Extract movie information from a URL
     *
     * @param url The URL to extract information from
     * @param movieId The movie ID (can be null)
     * @param existingM3u8Info Optional pre-fetched m3u8 info for this movie (can be null)
     * @return Map containing extracted movie information
     * @throws IOException If an error occurs during extraction
     */
    /**
     * 从文档中提取电影信息
     *
     * @param doc 要解析的JSoup文档
     * @param url 原始URL
     * @param movieId 电影ID
     * @param existingM3u8Info 预先获取的m3u8信息
     * @return 包含电影信息的映射
     */
    public static Map<String, Object> extractInfoFromDocument(Document doc, String url, Integer movieId, List<String> existingM3u8Info) {
        try {
            // 提取DVD ID
            String dvdId = null;
            Element metaOgUrl = doc.selectFirst("meta[property=og:url]");
            if (metaOgUrl != null && metaOgUrl.attr("content") != null) {
                String contentUrl = metaOgUrl.attr("content");
                dvdId = contentUrl.substring(contentUrl.lastIndexOf('/') + 1);
            } else {
                Element titleTag = doc.selectFirst("title");
                if (titleTag != null) {
                    String[] titleParts = titleTag.text().split("\\s+");
                    if (titleParts.length > 0) {
                        dvdId = titleParts[0];
                    }
                }
            }

            // 初始化m3u8信息列表
            List<String> m3u8Info = new ArrayList<>();

            // 使用预先获取的m3u8信息（如果有）
            if (existingM3u8Info != null && !existingM3u8Info.isEmpty()) {
                m3u8Info = existingM3u8Info;
                logger.info("Using provided m3u8 info for code: " + dvdId);
            } else {
                // 从页面提取m3u8信息
                logger.info("Extracting m3u8 info from page for code: " + dvdId);
                Map<String, Object> m3u8Result = extractM3u8Info(doc);

                if (m3u8Result != null &&
                    m3u8Result.get("encryptedCode") != null &&
                    m3u8Result.get("dictionary") != null) {

                    m3u8Info = deobfuscateM3u8(
                            (String) m3u8Result.get("encryptedCode"),
                            (List<String>) m3u8Result.get("dictionary")
                    );

                    if (!m3u8Info.isEmpty()) {
                        logger.info("Successfully extracted m3u8 info from page for code: " + dvdId);
                    }
                }
            }

            // 提取封面URL
            String coverUrl = null;
            Element metaOgImage = doc.selectFirst("meta[property=og:image]");
            if (metaOgImage != null) {
                coverUrl = metaOgImage.attr("content");
            }

            // 提取标题
            String title = null;
            Element metaTitle = doc.selectFirst("meta[property=og:title]");
            if (metaTitle != null) {
                title = metaTitle.attr("content");
            }

            // 提取描述
            String description = null;
            Element metaDesc = doc.selectFirst("meta[property=og:description]");
            if (metaDesc != null) {
                description = metaDesc.attr("content");
            }

            // 提取元数据元素
            Element metaDate = doc.selectFirst("meta[property=og:article:published_time]");
            String releaseDate = metaDate != null ? metaDate.attr("content") : null;

            // 提取影片详情信息
            Map<String, String> metaData = extractMetaData(doc);
            String actor = metaData.get("actor");
            String series = metaData.get("series");
            String maker = metaData.get("maker");
            String label = metaData.get("label");
            String director = metaData.get("director");
            String htmlReleaseDate = metaData.get("release_date");
            Integer duration = null;
            if (metaData.get("duration") != null) {
                try {
                    duration = Integer.parseInt(metaData.get("duration"));
                } catch (NumberFormatException e) {
                    logger.error("Error parsing duration", e);
                }
            }

            // 提取影片类型
            List<String> genres = extractGenres(doc);

            // 提取演员信息（如果可用）
            String extractedActress = extractActress(doc);

            // 创建返回的电影信息映射
            Map<String, Object> movieInfo = new HashMap<>();
            movieInfo.put("dvd_id", dvdId);
            movieInfo.put("m3u8_info", m3u8Info);
            movieInfo.put("cover_url", coverUrl);
            movieInfo.put("title", title);
            movieInfo.put("description", description);
            // 存储两个发布日期：元标签日期（网站发布）和HTML发布日期（实际电影发布）
            movieInfo.put("website_date", releaseDate); // 来自元标签（网站发布日期）
            movieInfo.put("release_date", htmlReleaseDate != null ? htmlReleaseDate : releaseDate); // 来自HTML内容的实际电影发布日期
            movieInfo.put("duration", duration);
            // 使用提取的女演员（如果可用），否则使用元标签中的演员
            movieInfo.put("actor", extractedActress != null ? extractedActress : actor);
            movieInfo.put("series", series);
            movieInfo.put("maker", maker);
            movieInfo.put("label", label);
            movieInfo.put("director", director);
            movieInfo.put("genres", genres);

            return movieInfo;
        } catch (Exception e) {
            logger.error("Error extracting info from document", e);
            return null;
        }
    }

    /**
     * 从URL提取电影信息
     *
     * @param url 要提取信息的URL
     * @param movieId 电影ID（可以为空）
     * @param existingM3u8Info 可选的预先获取的m3u8信息（可以为空）
     * @return 包含电影信息的映射
     * @throws IOException 如果提取过程中发生错误
     */
    public Map<String, Object> extractMovieInfoFromUrl(String url, Integer movieId, List<String> existingM3u8Info) throws IOException {
        return extractMovieInfoFromUrl(url, movieId, existingM3u8Info, false);
    }

    /**
     * 从URL提取电影信息，可选择是否使用代理
     *
     * @param url 要提取信息的URL
     * @param movieId 电影ID（可以为空）
     * @param existingM3u8Info 可选的预先获取的m3u8信息（可以为空）
     * @param useProxy 是否使用代理池中的代理
     * @return 包含电影信息的映射
     * @throws IOException 如果提取过程中发生错误
     */
    public Map<String, Object> extractMovieInfoFromUrl(String url, Integer movieId, List<String> existingM3u8Info, boolean useProxy) throws IOException {
        String dynamicCookies = "user_uuid=774d39b5-f161-473b-a046-e17deaa922c9; " +
                                "_ga=GA1.1.707042001.1747298480; " +
                                "search_history=[%22Caribbeancom-040622-001%22%2C%22atid-171-uncensored-leaked%22]; " +
                                "cf_clearance=lMBKY46Itq6Miw.SEKMeSpHxlUbNeQ0rGF9LuSyZKYY-1748421475-*******-aS1DSEbLFeMsOjT58jXHibLlO1mHPIyQsOQR.oDB3MGu4d4WEFFEJE9oyCuNS0zXh_g6sxgQ303rS8bsl.YEVyVrkyxxRjVd4EKzGcqZuDkfmY7paeSd8K3bYZcuSpzhZZaQjqQlmw9uH2dQxzFQET9dXVfBF46a_yMtKfKt7OxB8gYmXlEKL.UJAmpwNPzjV0PSEdQG9naSUDSCQF38OgrKJdJ7m6a8XiZB1GmtI_K.JJiQmTkQiPeCkDZ8gz3ZraFYfil1adJP2HuKqCU1kaKIBFeJgoQBQ_zubTRk.9uX5qRxIVyjECWqssTHPNVPtpHT7ajeiFTRtKE5m5ajrSpa2Csg1rtdJQZ95FmM; " +
                                "_ga_0C6GHNFYBF=GS2.1.s1748421477$o6$g0$t1748421477$j60$l0$h0; " + // Update based on current time
                                "CtueuAIeyRG7hr3RlxFX3VfrNP768aephvSj0zV3=eyJpdiI6IlhwVXJIZGpJUUpSQ1VWWDhPUmIvZkE9PSIsInZhbHVlIjoiZTJQMG8zbnpDVGNlZGNlSHBnMkdnbXR5dkVJS3ZZN3ByYkpMMlIvT0NNWjNRSGhoWkVmc1JvbkdidGRxdmh6UTVaUVhiUmhCNUhwcmhNOVcrQjVaK1dJM25pWlZNTTFZTUU5NUgwNFJ3d05jaGV0UFJ5S3VOM3c0cVhoMmQ5VEErZCtiS0FaaHVEdTlsNVpXNmdmWUJuZkw4Q0hOOWtTcWkvY21NWkptcm9RV1FNMmZVRURDMWlRSnRkdlJxaDF1elpsaitUVk52TDJvNWNjY3VVZG9jSGw2ZnBTdmFFNmtjUFowUUllUnFpejI4NVF5YlNBZHRrYzVnclRyZm1VVHAxY3E2Ukt0cWt3RHJ6WFZHaUczTEpyQlJMSzJiSmI1d0VzNUpzT0tEU2NnWG9VcTU0N2RlK3dVTS9tdWlqdWZLanBtRWQ3RGhJMmthTUtzaGsvVjFNMUdwdkNCdC9iVVdvL0Y0NmRtUW1UMGQvbDAxMXNXYkNjd0VSdmFmVTFWalh5dUhIWGF3alpGaDVJNkEzVDNVQT09IiwibWFjIjoiNjc0ZjM5YWU0MDY5ZmIzYzJhZTRkMzJhNjM0Y2VkOGY1MDdiNjVhZDFjMjA0YmE2NzgwMzczOGQyNTEwNTUwNiIsInRhZyI6IiJ9"; // Dynamic

        Headers headers = new Headers.Builder()
            .add("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
            .add("accept-language", "zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7") // Keep consistent with curl
            .add("cache-control", "max-age=0")
            .add("dnt", "1") // From curl
            .add("if-modified-since", "Wed, 28 May 2025 08:37:55 GMT") // From curl
            .add("priority", "u=0, i") // From curl
            .add("sec-ch-ua", "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"") // Update to match curl's user-agent base
            .add("sec-ch-ua-mobile", "?0")
            .add("sec-ch-ua-platform", "macOS")
            .add("sec-fetch-dest", "document")
            .add("sec-fetch-mode", "navigate")
            .add("sec-fetch-site", "none") // This is crucial for initial navigation.
            .add("sec-fetch-user", "?1")
            .add("sec-gpc", "1") // From curl
            .add("upgrade-insecure-requests", "1")
            .add("user-agent", getRandomUserAgent()) // 使用随机用户代理
            // Add Referer if you came from another page on the same domain
            .add("Referer", "https://missav.ai/") // Example: if you navigated from the homepage
            .add("Cookie", dynamicCookies) // The potentially problematic part
            .build();

            OkHttpClient httpClient = useProxy ? this.clientWithProxy : this.client;
            // Build the request with OkHttpClient
            Request request = new Request.Builder()
                .url(url)
                .headers(headers)
                .build();

            // Execute the request
            Response response = null;
            try {
                // 根据是否使用代理选择合适的HTTP客户端
                // Execute the request
                response = httpClient.newCall(request).execute();

                // Handle 304 Not Modified by making a new request without if-modified-since header
                if (response.code() == 304) {
                    logger.info("收到响应: 304 Not Modified (content unchanged since if-modified-since date)");
                    logger.info("最终URL: " + response.request().url());

                    // Close the current response before making a new request
                    response.close();

                    // Create a new request without the if-modified-since header
                    Request.Builder newRequestBuilder = new Request.Builder()
                        .url(url)
                        .headers(headers.newBuilder().removeAll("if-modified-since").build())
                        .get();

                    Request newRequest = newRequestBuilder.build();
                    response = client.newCall(newRequest).execute();

                    logger.info("重新发送请求(无if-modified-since头): " + url);
                }

                // Check if we got a successful response
                if (!response.isSuccessful()) {
                    logger.error("请求失败: " + response.code() + " " + response.message());
                    logger.error("原始URL: " + url);
                    logger.error("最终URL: " + response.request().url());
                    throw new IOException("Unexpected HTTP response: " + response.code());
                }

                // 打印URL重定向信息
                String finalUrl = response.request().url().toString();
                if (!url.equals(finalUrl)) {
                    logger.info("URL重定向: " + url + " -> " + finalUrl);
                }

                // Get response body and parse with JSoup
                String responseBody = response.body().string();
                Document doc = Jsoup.parse(responseBody, url);

                // Add cookies from the response if needed
                // The cookies were included in the curl command but we're not explicitly using them here
                // as JSoup will handle the HTML parsing without needing the cookies

                // Extract DVD ID
                String dvdId = null;
                Element metaOgUrl = doc.selectFirst("meta[property=og:url]");
                if (metaOgUrl != null && metaOgUrl.attr("content") != null) {
                    String contentUrl = metaOgUrl.attr("content");
                    dvdId = contentUrl.substring(contentUrl.lastIndexOf('/') + 1);
                } else {
                    Element titleTag = doc.selectFirst("title");
                    if (titleTag != null) {
                        String[] titleParts = titleTag.text().split("\\s+");
                        if (titleParts.length > 0) {
                            dvdId = titleParts[0];
                        }
                    }
                }

                // Initialize m3u8Info list
                List<String> m3u8Info = new ArrayList<>();

                // Use existing m3u8 info if provided by the service layer
                if (existingM3u8Info != null && !existingM3u8Info.isEmpty()) {
                    m3u8Info = existingM3u8Info;
                    logger.info("Using provided m3u8 info for code: " + dvdId);
                } else {
                    // Extract m3u8 info from the page
                    logger.info("Extracting m3u8 info from page for code: " + dvdId);
                    Map<String, Object> m3u8Result = extractM3u8Info(doc);

                    if (m3u8Result != null &&
                        m3u8Result.get("encryptedCode") != null &&
                        m3u8Result.get("dictionary") != null) {

                        m3u8Info = deobfuscateM3u8(
                                (String) m3u8Result.get("encryptedCode"),
                                (List<String>) m3u8Result.get("dictionary")
                        );

                        if (!m3u8Info.isEmpty()) {
                            logger.info("Successfully extracted m3u8 info from page for code: " + dvdId);
                        }
                    }
                }

                String coverUrl = null;
                Element metaOgImage = doc.selectFirst("meta[property=og:image]");
                if (metaOgImage != null) {
                    coverUrl = metaOgImage.attr("content");
                }

                String title = null;
                Element metaTitle = doc.selectFirst("meta[property=og:title]");
                if (metaTitle != null) {
                    title = metaTitle.attr("content");
                }

                String description = null;
                Element metaDesc = doc.selectFirst("meta[property=og:description]");
                if (metaDesc != null) {
                    description = metaDesc.attr("content");
                }

                String releaseDate = null;
                Element metaDate = doc.selectFirst("meta[property=og:video:release_date]");
                if (metaDate != null) {
                    releaseDate = metaDate.attr("content");
                }

                String duration = null;
                Element metaDuration = doc.selectFirst("meta[property=og:video:duration]");
                if (metaDuration != null) {
                    duration = metaDuration.attr("content");
                }

                String actor = null;
                Element metaActor = doc.selectFirst("meta[property=og:video:actor]");
                if (metaActor != null) {
                    actor = metaActor.attr("content");
                }

                String series = null;
                String maker = null;
                String label = null;
                String director = null;
                String extractedActress = null;
                String htmlReleaseDate = null;
                List<String> genres = new ArrayList<>();

                // Language-specific field names (case-insensitive)
                Map<String, String[]> fieldPatterns = initializeFieldPatterns();

                // Create optimized lookup maps for faster pattern matching
                Map<String, Map<String, String>> fieldPatternMaps = new HashMap<>();
                for (String field : fieldPatterns.keySet()) {
                    Map<String, String> patternMap = new HashMap<>();
                    for (String pattern : fieldPatterns.get(field)) {
                        patternMap.put(pattern.toLowerCase(), field);
                    }
                    fieldPatternMaps.put(field, patternMap);
                }

                Element infoDiv = doc.selectFirst("div.space-y-2");
                if (infoDiv != null) {
                    Elements infoItems = infoDiv.select("div.text-secondary");
                    for (Element item : infoItems) {
                        String text = item.text().trim();
                        int colonIndex = text.indexOf(':');
                        if (colonIndex <= 0) continue;

                        String key = text.substring(0, colonIndex).trim();
                        String value = text.substring(colonIndex + 1).trim();

                        // Use case-insensitive matching
                        // Check for series
                        boolean matchFound = false;
                        for (String pattern : fieldPatterns.get("series")) {
                            if (key.toLowerCase().contains(pattern.toLowerCase())) {
                                series = value;
                                matchFound = true;
                                break;
                            }
                        }

                        // If series was matched, continue to next item
                        if (matchFound) continue;

                        // Check for maker
                        for (String pattern : fieldPatterns.get("maker")) {
                            if (key.toLowerCase().contains(pattern.toLowerCase())) {
                                maker = value;
                                matchFound = true;
                                break;
                            }
                        }

                        // If maker was matched, continue to next item
                        if (matchFound) continue;

                        // Check for label
                        for (String pattern : fieldPatterns.get("label")) {
                            if (key.toLowerCase().contains(pattern.toLowerCase())) {
                                label = value;
                                matchFound = true;
                                break;
                            }
                        }

                        // If label was matched, continue to next item
                        if (matchFound) continue;

                        // Check for genre
                        for (String pattern : fieldPatterns.get("genre")) {
                            if (key.toLowerCase().contains(pattern.toLowerCase())) {
                                String[] genreArray = value.split(",");
                                for (String genre : genreArray) {
                                    genres.add(genre.trim());
                                }
                                matchFound = true;
                                break;
                            }
                        }

                        // If genre was matched, continue to next item
                        if (matchFound) continue;

                        // Check for actress - using optimized lookup
                        Map<String, String> actressPatternMap = fieldPatternMaps.get("actress");
                        String keyLower = key.toLowerCase();
                        for (Map.Entry<String, String> entry : actressPatternMap.entrySet()) {
                            if (keyLower.contains(entry.getKey())) {
                                extractedActress = value;
                                matchFound = true;
                                break;
                            }
                        }

                        // If actress was matched, continue to next item
                        if (matchFound) continue;

                        // Check for director
                        for (String pattern : fieldPatterns.get("director")) {
                            if (key.toLowerCase().contains(pattern.toLowerCase())) {
                                director = value;
                                matchFound = true;
                                break;
                            }
                        }

                        // If director was matched, continue to next item
                        if (matchFound) continue;

                        // Check for release date
                        for (String pattern : fieldPatterns.get("releaseDate")) {
                            if (key.toLowerCase().contains(pattern.toLowerCase())) {
                                htmlReleaseDate = value;
                                // If the date is in a time tag, try to extract the datetime attribute value
                                Element timeElement = item.selectFirst("time");
                                if (timeElement != null && timeElement.hasAttr("datetime")) {
                                    htmlReleaseDate = timeElement.attr("datetime");
                                    // If datetime has time component, strip it for consistency
                                    if (htmlReleaseDate.contains("T")) {
                                        htmlReleaseDate = htmlReleaseDate.split("T")[0];
                                    }
                                }
                                break;
                            }
                        }
                    }
                }

                Map<String, Object> movieInfo = new HashMap<>();
                // Use actress field extraction if available

                movieInfo.put("dvd_id", dvdId);
                movieInfo.put("m3u8_info", m3u8Info);
                movieInfo.put("cover_url", coverUrl);
                movieInfo.put("title", title);
                movieInfo.put("description", description);
                // Store both release dates: meta tag date (website publish) and HTML release date (actual movie release)
                movieInfo.put("website_date", releaseDate); // From meta tags (website publication date)
                movieInfo.put("release_date", htmlReleaseDate != null ? htmlReleaseDate : releaseDate); // Actual movie release date from HTML content
                movieInfo.put("duration", duration);
                // Use extracted actress if available, otherwise use the actor from meta tags
                movieInfo.put("actor", extractedActress != null ? extractedActress : actor);
                movieInfo.put("series", series);
                movieInfo.put("maker", maker);
                movieInfo.put("label", label);
                movieInfo.put("director", director);
                movieInfo.put("genres", genres);

                return movieInfo;
            } catch (IOException e) {
            System.err.println("Error fetching URL: " + url);
            throw e;
        }
    }

    private static Map<String, Object> extractM3u8Info(Document doc) {
        Map<String, Object> result = new HashMap<>();
        Elements scripts = doc.select("script");
        // Fixed pattern with properly balanced parentheses
    Pattern pattern = Pattern.compile("eval\\(function\\(p,a,c,k,e,d\\)\\{(.+?)\\}\\('(.+?)',([0-9]+),([0-9]+),'(.+?)'\\.((?:split\\('\\|'\\))|(?:split\\('\\|'\\),0,\\{\\}))\\)");

        for (Element script : scripts) {
            String scriptContent = script.html();
            if (scriptContent.contains("eval(function(p,a,c,k,e,d)")) {
                Matcher matcher = pattern.matcher(scriptContent);
                if (matcher.find()) {
                    String dictionaryStr = matcher.group(5);
                    List<String> dictionary = List.of(dictionaryStr.split("\\|"));
                    String encryptedCode = matcher.group(2);
                    result.put("encryptedCode", encryptedCode);
                    result.put("dictionary", dictionary);
                    return result;
                }
            }
        }
        result.put("encryptedCode", null);
        result.put("dictionary", null);
        return result;
    }

    private static List<String> deobfuscateM3u8(String encryptedCode, List<String> dictionary) {
        if (encryptedCode == null || dictionary == null) {
            return new ArrayList<>();
        }

        String[] parts = encryptedCode.split(";");
        List<String> results = new ArrayList<>();

        for (String part : parts) {
            if (!part.contains("=")) continue;
            String value = part.split("=")[1].replaceAll("[\"'\\\\\\s]", "");

            StringBuilder decoded = new StringBuilder();
            for (char c : value.toCharArray()) {
                if (c == '.' || c == '-' || c == '/' || c == ':') {
                    decoded.append(c);
                } else {
                    int number = Integer.parseInt(String.valueOf(c), 16);
                    decoded.append(dictionary.get(number));
                }
            }
            results.add(decoded.toString());
        }
        return results;
    }

    /**
     * Initialize and return field patterns for all supported languages and fields
     * @return A map containing all field patterns
     */
    public static Map<String, String[]> initializeFieldPatterns() {
        Map<String, String[]> fieldPatterns = new HashMap<>();
        // For series field patterns - All 13 languages
        fieldPatterns.put("series", new String[]{
            "系列", // Traditional Chinese
            "系列", // Simplified Chinese
            "Series", // English
            "シリーズ", // Japanese
            "시리즈", // Korean
            "Siri", "Series", // Malay
            "ชุด", // Thai
            "Serie", // German
            "Série", // French
            "Loạt", // Vietnamese
            "Seri", // Indonesian
            "Serye", // Filipino
            "Série" // Portuguese
        });

        // For maker field patterns - All 13 languages
        fieldPatterns.put("maker", new String[]{
            "製作商", // Traditional Chinese
            "发行商", // Simplified Chinese
            "Maker", // English
            "メーカー", // Japanese
            "메이커", // Korean
            "Pembuat", // Malay
            "ผู้ผลิต", // Thai
            "Hersteller", // German
            "Fabricant", // French
            "nhà sản xuất", // Vietnamese
            "Pembuat", // Indonesian
            "Gumawa", // Filipino
            "Fabricante" // Portuguese
        });

        // For label field patterns - All 13 languages
        fieldPatterns.put("label", new String[]{
            "標籤", // Traditional Chinese
            "标籤", // Simplified Chinese
            "Label", // English
            "レーベル", // Japanese
            "상표", // Korean
            "Label", // Malay
            "ฉลาก", // Thai
            "Etikett", // German
            "Étiqueter", // French
            "Nhãn", // Vietnamese
            "Label", // Indonesian
            "Label", // Filipino
            "Rótulo" // Portuguese
        });

        // For genre field patterns - All 13 languages
        fieldPatterns.put("genre", new String[]{
            "類型", // Traditional Chinese
            "类型", // Simplified Chinese
            "Genre", // English
            "ジャンル", // Japanese
            "장르", // Korean
            "Genre", // Malay
            "ประเภท", // Thai
            "Genre", // German
            "Le genre", // French
            "thể loại", // Vietnamese
            "Genre", // Indonesian
            "Genre", // Filipino
            "Gênero" // Portuguese
        });

        // For actress field patterns - All 13 languages
        fieldPatterns.put("actress", new String[]{
            "女優", // Traditional Chinese
            "女优", // Simplified Chinese
            "Actress", // English
            "女優", // Japanese
            "여배우", // Korean
            "Pelakon wanita", // Malay
            "นักแสดงหญิง", // Thai
            "Schauspielerin", // German
            "Actrice", // French
            "Diễn viên", // Vietnamese
            "Aktris", // Indonesian
            "Artista", // Filipino
            "Actriz" // Portuguese
        });

        // For release date field patterns - All 13 languages
        fieldPatterns.put("releaseDate", new String[]{
            "發行日期", "发行日期", // Traditional & Simplified Chinese
            "Release date", // English
            "配信開始日", // Japanese
            "출시일", // Korean
            "Tarikh keluaran", // Malay
            "วันที่วางจำหน่าย", // Thai
            "Veröffentlichungsdatum", // German
            "Date de sortie", // French
            "Ngày phát hành", // Vietnamese
            "Tanggal rilis", // Indonesian
            "Petsa ng Paglabas", // Filipino
            "Data de lançamento" // Portuguese
        });

        // For male actor field patterns - All 13 languages
        fieldPatterns.put("maleActor", new String[]{
            "男優", // Traditional Chinese
            "男优", // Simplified Chinese
            "Actor", "Male actor", // English
            "男優", // Japanese
            "남자 배우", "남배우", // Korean
            "Pelakon lelaki", // Malay
            "นักแสดงชาย", // Thai
            "Schauspieler", "Darsteller", // German
            "Acteur", // French
            "Diễn viên nam", // Vietnamese
            "Aktor", // Indonesian
            "Aktor", // Filipino
            "Ator" // Portuguese
        });

        // For director field patterns - All 13 languages
        fieldPatterns.put("director", new String[]{
            "導演", "监督", // Traditional Chinese
            "导演", "监督", // Simplified Chinese
            "Director", // English
            "監督", "ディレクター", // Japanese
            "관리자", // Korean
            "Pengarah", // Malay
            "ผู้อำนวยการ", // Thai
            "Direktor", "Regisseur", // German
            "Réalisateur", // French
            "Giám đốc", // Vietnamese
            "Direktur", // Indonesian
            "Direktor", // Filipino
            "Diretor" // Portuguese
        });

        return fieldPatterns;
    }

    /**
     * Downloads HTML content for all language variants and saves them to files
     * @param videoId The video ID to fetch
     * @throws IOException If an I/O error occurs
     */
    public void downloadAllLanguages(String videoId) throws IOException {
        // Create data directory if it doesn't exist
        Path dataDir = Paths.get("data");
        if (!Files.exists(dataDir)) {
            Files.createDirectories(dataDir);
        }

        // Download HTML for each language
        for (Map.Entry<String, String> language : LANGUAGES.entrySet()) {
            String langCode = language.getKey();
            String langName = language.getValue();

            String url;
            String fileName;

            if (langCode.isEmpty()) {
                // Base URL for default language
                url = BASE_URL + "/" + videoId;
                fileName = "missav_default.html";
            } else {
                // Localized URL
                url = BASE_URL + "/" + langCode + "/" + videoId;
                fileName = "missav_" + langCode + ".html";
            }

            System.out.println("Downloading " + langName + " (" + langCode + ") from: " + url);

            try {
                // Download the HTML
                String html = downloadHtml(url);

                // Save to file
                saveToFile(dataDir.resolve(fileName).toString(), html);

                System.out.println("Saved to: " + fileName);

                // Delay to avoid hitting rate limits
                Thread.sleep(1000);
            } catch (Exception e) {
                System.err.println("Error downloading " + langName + ": " + e.getMessage());
            }
        }
    }

    /**
     * Downloads HTML content from the given URL
     */
    private String downloadHtml(String url) throws IOException {
        // Build headers similar to a browser request
        Headers headers = new Headers.Builder()
            .add("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
            .add("accept-language", "zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7")
            .add("cache-control", "max-age=0")
            .add("dnt", "1")
            .add("sec-fetch-dest", "document")
            .add("sec-fetch-mode", "navigate")
            .add("sec-fetch-site", "none")
            .add("sec-fetch-user", "?1")
            .add("sec-gpc", "1")
            .add("upgrade-insecure-requests", "1")
            .add("user-agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1")
            .build();

        // Build the request
        Request request = new Request.Builder()
            .url(url)
            .headers(headers)
            .build();

        // Execute the request
        try (Response response = client.newCall(request).execute()) {
            // Check if we got a successful response
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected HTTP response: " + response.code());
            }

            // Return response body
            return response.body().string();
        }
    }

    /**
     * Saves content to a file
     */
    private static void saveToFile(String filePath, String content) throws IOException {
        try (FileWriter writer = new FileWriter(filePath)) {
            writer.write(content);
        }
    }

    /**
     * Extracts information from MissAV HTML content
     *
     * @param htmlContent The HTML content to parse
     * @return A map containing the extracted information
     */
    public static Map<String, Object> extractMissavInfo(String htmlContent) {
        Document doc = Jsoup.parse(htmlContent);
        Map<String, Object> info = new HashMap<>();

        // Extract title
        String title = extractMissavTitle(doc);
        info.put("title", title);

        // Extract details
        Map<String, String> details = extractMissavDetails(doc);
        for (Map.Entry<String, String> entry : details.entrySet()) {
            info.put(entry.getKey(), entry.getValue());
        }

        // Extract movie ID
        String movieId = extractMissavMovieId(doc);
        info.put("movie_id", movieId);

        // Extract cover URL
        String coverUrl = extractMissavCoverUrl(doc);
        info.put("cover_url", coverUrl);

        return info;
    }

    /**
     * Extracts the title from a MissAV HTML document
     *
     * @param doc The Jsoup Document
     * @return The extracted title
     */
    public static String extractMissavTitle(Document doc) {
        Element titleElement = doc.selectFirst("h1");
        return titleElement != null ? titleElement.text() : null;
    }

    /**
     * Extracts the details from a MissAV HTML document
     *
     * @param doc The Jsoup Document
     * @return Map of detail keys and values
     */
    public static Map<String, String> extractMissavDetails(Document doc) {
        Map<String, String> details = new HashMap<>();
        Element detailsDiv = doc.selectFirst("div.detail-item");

        if (detailsDiv != null) {
            Elements detailItems = detailsDiv.select("div");
            for (Element item : detailItems) {
                Elements spans = item.select("span");
                if (spans.size() >= 2) {
                    String key = spans.get(0).text().replace(":", "").trim().toLowerCase();
                    String value = spans.get(1).text().trim();
                    details.put(key, value);
                }
            }
        }

        return details;
    }

    /**
     * Extracts the movie ID from a MissAV HTML document
     *
     * @param doc The Jsoup Document
     * @return The extracted movie ID
     */
    public static String extractMissavMovieId(Document doc) {
        // Look for the v-scope attribute containing Favourite('movie', ID, 0)
        Elements elements = doc.select("[v-scope*=Favourite('movie']");

        for (Element element : elements) {
            String vScope = element.attr("v-scope");
            Pattern pattern = Pattern.compile("Favourite\\('movie', (\\d+),");
            Matcher matcher = pattern.matcher(vScope);

            if (matcher.find()) {
                return matcher.group(1);
            }
        }

        // Try alternative method - look for data-code attribute
        Element dataCodeElement = doc.selectFirst("[data-code]");
        if (dataCodeElement != null) {
            String dataCode = dataCodeElement.attr("data-code");
            if (dataCode != null && !dataCode.isEmpty()) {
                return dataCode;
            }
        }

        return null;
    }

    /**
     * Extracts the cover URL from a MissAV HTML document
     *
     * @param doc The Jsoup Document
     * @return The extracted cover URL
     */
    public static String extractMissavCoverUrl(Document doc) {
        // Method 1: Look for the player div with data-poster attribute
        Element playerDiv = doc.selectFirst("div#player[data-poster]");
        if (playerDiv != null) {
            String dataPoster = playerDiv.attr("data-poster");
            if (dataPoster != null && !dataPoster.isEmpty()) {
                return dataPoster;
            }
        }

        // Method 2: Look for meta og:image tag
        Element metaOgImage = doc.selectFirst("meta[property=og:image]");
        if (metaOgImage != null) {
            String content = metaOgImage.attr("content");
            if (content != null && !content.isEmpty()) {
                return content;
            }
        }

        return null;
    }

    /**
     * 从文档中提取元数据（演员、制作商、标签等）
     *
     * @param doc 要解析的JSoup文档
     * @return 包含提取元数据的映射
     */
    public static Map<String, String> extractMetaData(Document doc) {
        Map<String, String> metaData = new HashMap<>();

        try {
            // 查找包含影片信息的表格或容器
            Elements infoElements = doc.select("div.movie-info span.tag-genre");
            if (!infoElements.isEmpty()) {
                for (Element element : infoElements) {
                    String text = element.text().trim();
                    if (text.contains(":")) {
                        String[] parts = text.split(":", 2);
                        if (parts.length == 2) {
                            String key = parts[0].trim().toLowerCase().replace(" ", "_");
                            String value = parts[1].trim();
                            metaData.put(key, value);
                        }
                    }
                }
            } else {
                // 尝试备用方法 - 查找包含影片信息的div
                Elements divElements = doc.select("div.info-div");
                for (Element div : divElements) {
                    String label = div.selectFirst("p.info-label") != null ?
                                  div.selectFirst("p.info-label").text().trim() : "";
                    String value = div.selectFirst("p.info-content") != null ?
                                  div.selectFirst("p.info-content").text().trim() : "";

                    if (!label.isEmpty()) {
                        String key = label.toLowerCase().replace(" ", "_");
                        metaData.put(key, value);
                    }
                }
            }

            // 查找特定元数据，如发布日期、时长等
            Elements metaElements = doc.select("meta[itemprop]");
            for (Element meta : metaElements) {
                String itemProp = meta.attr("itemprop");
                String content = meta.attr("content");

                if (itemProp.equals("datePublished")) {
                    metaData.put("release_date", content);
                } else if (itemProp.equals("duration")) {
                    // 尝试从格式如"PT120M"的内容中提取分钟
                    if (content.startsWith("PT") && content.endsWith("M")) {
                        try {
                            int minutes = Integer.parseInt(content.substring(2, content.length() - 1));
                            metaData.put("duration", String.valueOf(minutes * 60)); // 转换为秒
                        } catch (NumberFormatException e) {
                            logger.warn("无法解析时长: " + content);
                        }
                    } else {
                        metaData.put("duration", content);
                    }
                } else if (itemProp.equals("actor")) {
                    metaData.put("actor", content);
                } else if (itemProp.equals("director")) {
                    metaData.put("director", content);
                }
            }

        } catch (Exception e) {
            logger.error("提取元数据时出错", e);
        }

        return metaData;
    }

    /**
     * 从文档中提取影片类型/标签
     *
     * @param doc 要解析的JSoup文档
     * @return 提取的类型列表
     */
    public static List<String> extractGenres(Document doc) {
        List<String> genres = new ArrayList<>();

        try {
            // 方法1: 查找带有genre标签的元素
            Elements genreElements = doc.select("span.genre-item");
            if (!genreElements.isEmpty()) {
                for (Element element : genreElements) {
                    String genre = element.text().trim();
                    if (!genre.isEmpty()) {
                        genres.add(genre);
                    }
                }
            }

            // 方法2: 查找其他可能包含类型的元素
            if (genres.isEmpty()) {
                Elements tagElements = doc.select("div.tag-container a");
                for (Element element : tagElements) {
                    String genre = element.text().trim();
                    if (!genre.isEmpty() && !genre.contains(":")) {
                        genres.add(genre);
                    }
                }
            }

            // 方法3: 从元数据中查找类型
            if (genres.isEmpty()) {
                Elements metaGenres = doc.select("meta[property=video:tag]");
                for (Element meta : metaGenres) {
                    String content = meta.attr("content").trim();
                    if (!content.isEmpty()) {
                        genres.add(content);
                    }
                }
            }

        } catch (Exception e) {
            logger.error("提取影片类型时出错", e);
        }

        return genres;
    }

    /**
     * 从文档中提取演员信息
     *
     * @param doc 要解析的JSoup文档
     * @return 提取的演员名称，如果未找到则返回null
     */
    public static String extractActress(Document doc) {
        try {
            // 方法1: 查找特定的演员元素
            Element actressElement = doc.selectFirst("div.actress-info h5");
            if (actressElement != null) {
                return actressElement.text().trim();
            }

            // 方法2: 从标题中提取演员名称
            Element titleElement = doc.selectFirst("title");
            if (titleElement != null) {
                String title = titleElement.text();
                // 假设标题格式为"DVD-ID 演员名 - 标题"
                if (title.contains(" - ")) {
                    String firstPart = title.split(" - ")[0].trim();
                    // 分离DVD-ID和演员名
                    String[] parts = firstPart.split("\\s+", 2);
                    if (parts.length == 2) {
                        return parts[1].trim();
                    }
                }
            }

            // 方法3: 查找其他可能包含演员信息的元素
            Elements infoElements = doc.select("div.movie-info span.tag-genre");
            for (Element element : infoElements) {
                String text = element.text().trim();
                if (text.toLowerCase().startsWith("actress:") || text.toLowerCase().startsWith("actor:")) {
                    String[] parts = text.split(":", 2);
                    if (parts.length == 2) {
                        return parts[1].trim();
                    }
                }
            }

        } catch (Exception e) {
            logger.error("提取演员信息时出错", e);
        }

        return null;
    }

    /**
     * 从123av网站的HTML内容中提取电影信息
     *
     * @param htmlContent HTML内容
     * @param url 原始URL
     * @param languageCode 语言代码
     * @return 包含电影信息的映射
     */
    public static Map<String, Object> extract123AvInfo(String htmlContent, String url, String languageCode) {
        try {
            // 使用Jsoup解析HTML内容
            Document doc = Jsoup.parse(htmlContent);

            // 创建提取信息的Map
            Map<String, Object> extractedInfo = new HashMap<>();

            // 提取DVD ID (电影代码)
            String dvdId = null;
            Elements codeElements = doc.select("div.detail-item div:contains(コード) span:eq(1)");
            if (!codeElements.isEmpty()) {
                dvdId = codeElements.first().text().trim();
            } else {
                // 尝试从URL中提取
                dvdId = url.substring(url.lastIndexOf('/') + 1);
            }
            extractedInfo.put("dvd_id", dvdId);

            // 提取标题 - 尝试多种可能的选择器
            String title = null;
            // 尝试从meta标签获取标题
            Element metaTitle = doc.selectFirst("meta[property=og:title]");
            if (metaTitle != null) {
                title = metaTitle.attr("content");
            }

            // 如果meta标签没有标题，尝试其他选择器
            if (title == null || title.isEmpty()) {
                Elements titleElements = doc.select("h1.entry-title, div.video-details h2, title");
                if (!titleElements.isEmpty()) {
                    title = titleElements.first().text().trim();
                } else {
                    // 如果找不到标题，使用电影代码作为标题
                    title = dvdId != null ? dvdId : url.substring(url.lastIndexOf('/') + 1);
                }
            }
            extractedInfo.put("title", title);

            // 提取描述
            Elements descElements = doc.select("div.description p");
            if (!descElements.isEmpty()) {
                extractedInfo.put("description", descElements.first().text().trim());
            } else {
                // 尝试其他可能的描述选择器
                descElements = doc.select("div.entry-content, div.video-description");
                if (!descElements.isEmpty()) {
                    extractedInfo.put("description", descElements.first().text().trim());
                }
            }

            // 提取封面URL
            String coverUrl = null;
            // 尝试从meta标签获取
            Element metaImage = doc.selectFirst("meta[property=og:image]");
            if (metaImage != null) {
                coverUrl = metaImage.attr("content");
            }

            // 如果meta标签没有图片，尝试其他选择器
            if (coverUrl == null || coverUrl.isEmpty()) {
                Elements imgElements = doc.select("div.video-player img, img.poster");
                if (!imgElements.isEmpty()) {
                    coverUrl = imgElements.first().attr("src");
                }
            }

            if (coverUrl != null && !coverUrl.isEmpty()) {
                extractedInfo.put("cover_url", coverUrl);
            }

            // 提取发布日期
            Elements releaseDateElements = doc.select("div.detail-item div:contains(リリース日) span:eq(1)");
            if (!releaseDateElements.isEmpty()) {
                extractedInfo.put("release_date", releaseDateElements.first().text().trim());
            }

            // 提取时长
            Elements durationElements = doc.select("div.detail-item div:contains(再生時間) span:eq(1)");
            if (!durationElements.isEmpty()) {
                extractedInfo.put("duration", durationElements.first().text().trim());
            }

            // 提取演员列表
            Elements actressElements = doc.select("div.detail-item div:contains(女優) span:eq(1) a");
            if (!actressElements.isEmpty()) {
                List<String> actresses = new ArrayList<>();
                for (Element actress : actressElements) {
                    actresses.add(actress.text().trim());
                }
                extractedInfo.put("actresses", actresses);
            }

            // 提取类型/标签
            Elements genreElements = doc.select("div.detail-item div:contains(ジャンル) span.genre a");
            if (!genreElements.isEmpty()) {
                List<String> genres = new ArrayList<>();
                for (Element genre : genreElements) {
                    genres.add(genre.text().trim());
                }
                extractedInfo.put("genres", genres);
            }

            // 提取制作商
            Elements makerElements = doc.select("div.detail-item div:contains(メーカー) span:eq(1) a");
            if (!makerElements.isEmpty()) {
                extractedInfo.put("maker", makerElements.first().text().trim());
            }

            // 提取标签
            Elements labelElements = doc.select("div.detail-item div:contains(ラベル) span:eq(1) a");
            if (!labelElements.isEmpty()) {
                extractedInfo.put("label", labelElements.first().text().trim());
            }


            Elements magnetElements = doc.select("div.magnet a"); // 直接选择磁力链接的<a>标签
            if (!magnetElements.isEmpty()) {
                List<Map<String, String>> magnets = new ArrayList<>();
                for (Element magnetLinkElement : magnetElements) {
                    Map<String, String> magnetInfo = new HashMap<>();

                    // 1. 提取磁力链接本身 (href)
                    magnetInfo.put("href", magnetLinkElement.attr("href"));

                    // 2. 提取名称 (span.name)
                    Element nameElement = magnetLinkElement.selectFirst("span.name");
                    if (nameElement != null) {
                        magnetInfo.put("name", nameElement.text().trim());
                    }

                    // 3. 提取文件大小和日期 (div.detail-item)
                    Elements detailItems = magnetLinkElement.select("div.detail-item");
                    if (detailItems.size() >= 2) {
                        magnetInfo.put("size", detailItems.get(0).text().trim());
                        magnetInfo.put("date", detailItems.get(1).text().trim());
                    }
                    
                    // 只有在成功提取到 href 时才将该磁力链接信息添加到列表
                    if (magnetInfo.containsKey("href") && !magnetInfo.get("href").isEmpty()) {
                        magnets.add(magnetInfo);
                    }
                }
                // 如果列表不为空，则将其放入最终结果中
                if (!magnets.isEmpty()) {
                    extractedInfo.put("magnets", JacksonUtils.toJsonString(magnets));
                }
            }

            // 添加额外信息
            extractedInfo.put("source", "123av");
            extractedInfo.put("language_code", languageCode);

            return extractedInfo;
        } catch (Exception e) {
            logger.errorf("Error extracting info from 123av HTML: %s", e.getMessage());
            return null;
        }
    }
}
