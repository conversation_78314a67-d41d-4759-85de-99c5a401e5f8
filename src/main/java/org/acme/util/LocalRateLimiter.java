package org.acme.util;


import java.time.Duration;

import io.github.bucket4j.Bandwidth;
import io.github.bucket4j.Bucket;
import io.github.bucket4j.Refill;

public class LocalRateLimiter {
    private final Bucket bucket;

    /**
     * 初始化令牌桶限流器
     * 配置：每分钟12个令牌（即每5秒生成1个令牌）
     * 桶容量：1（强制每次请求间隔≥5秒）
     */
    public LocalRateLimiter() {
        Bandwidth limit = Bandwidth.classic(1,
                Refill.intervally(1, Duration.ofSeconds(5))); // 每5秒补充1个令牌
        this.bucket = Bucket.builder()
                .addLimit(limit)
                .build();
    }

    /**
     * 尝试获取令牌
     * @return true-允许执行, false-需要等待
     */
    public boolean tryAcquire() {
        return bucket.tryConsume(1);
    }

    /**
     * 安全等待并获取令牌（阻塞式）
     */
    public void acquireWithWait() {
        while (!tryAcquire()) {
            try {
                // 精确计算需要等待的毫秒数
                long waitNanos = bucket.estimateAbilityToConsume(1).getNanosToWaitForRefill();
                Thread.sleep(waitNanos / 1_000_000, (int) (waitNanos % 1_000_000));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("限流等待被中断", e);
            }
        }
    }
}
