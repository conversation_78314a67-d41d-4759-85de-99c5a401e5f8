package org.acme.util;

import org.jboss.logging.Logger;

import jakarta.enterprise.context.ApplicationScoped;
import java.time.Instant;
import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.Map;

/**
 * 电影信息处理性能监控工具
 * 专门用于跟踪电影信息提取和处理的性能指标
 */
@ApplicationScoped
public class MovieInfoMetrics {
    
    private static final Logger LOG = Logger.getLogger(MovieInfoMetrics.class);
    
    // 计数器
    private final AtomicInteger totalMoviesProcessed = new AtomicInteger(0);
    private final AtomicInteger successfulExtractions = new AtomicInteger(0);
    private final AtomicInteger failedExtractions = new AtomicInteger(0);
    private final AtomicInteger activeThreads = new AtomicInteger(0);
    
    // 时间统计
    private final AtomicLong totalProcessingTime = new AtomicLong(0);
    private final AtomicLong minProcessingTime = new AtomicLong(Long.MAX_VALUE);
    private final AtomicLong maxProcessingTime = new AtomicLong(0);
    
    // 网络请求统计
    private final AtomicInteger totalNetworkRequests = new AtomicInteger(0);
    private final AtomicInteger successfulNetworkRequests = new AtomicInteger(0);
    private final AtomicInteger failedNetworkRequests = new AtomicInteger(0);
    
    // 电影代码统计
    private final ConcurrentHashMap<String, AtomicInteger> movieCodePrefixStats = new ConcurrentHashMap<>();
    
    // 错误统计
    private final ConcurrentHashMap<String, AtomicInteger> errorStats = new ConcurrentHashMap<>();
    
    // 会话跟踪
    private final ConcurrentHashMap<String, MovieProcessingSession> activeSessions = new ConcurrentHashMap<>();
    
    /**
     * 开始一个电影处理会话
     */
    public String startMovieProcessing(String movieCode) {
        String sessionId = generateSessionId(movieCode);
        MovieProcessingSession session = new MovieProcessingSession(sessionId, movieCode);
        activeSessions.put(sessionId, session);
        activeThreads.incrementAndGet();
        
        LOG.debugf("开始处理电影: %s (会话: %s)", movieCode, sessionId);
        return sessionId;
    }
    
    /**
     * 记录网络请求
     */
    public void recordNetworkRequest(String sessionId, boolean success) {
        totalNetworkRequests.incrementAndGet();
        if (success) {
            successfulNetworkRequests.incrementAndGet();
        } else {
            failedNetworkRequests.incrementAndGet();
        }
        
        MovieProcessingSession session = activeSessions.get(sessionId);
        if (session != null) {
            session.networkRequestCount++;
            if (!success) {
                session.networkErrorCount++;
            }
        }
    }
    
    /**
     * 结束一个电影处理会话
     */
    public void endMovieProcessing(String sessionId, boolean success, String errorMessage) {
        MovieProcessingSession session = activeSessions.remove(sessionId);
        if (session == null) {
            LOG.warnf("找不到处理会话: %s", sessionId);
            return;
        }
        
        session.endTime = Instant.now();
        long duration = Duration.between(session.startTime, session.endTime).toMillis();
        
        // 更新统计
        totalMoviesProcessed.incrementAndGet();
        totalProcessingTime.addAndGet(duration);
        activeThreads.decrementAndGet();
        
        // 更新最小/最大处理时间
        updateMinTime(duration);
        updateMaxTime(duration);
        
        if (success) {
            successfulExtractions.incrementAndGet();
            
            // 统计电影代码前缀
            String prefix = extractMovieCodePrefix(session.movieCode);
            movieCodePrefixStats.computeIfAbsent(prefix, k -> new AtomicInteger(0)).incrementAndGet();
        } else {
            failedExtractions.incrementAndGet();
            if (errorMessage != null) {
                errorStats.computeIfAbsent(errorMessage, k -> new AtomicInteger(0)).incrementAndGet();
            }
        }
        
        LOG.debugf("完成处理电影: %s, 耗时: %dms, 成功: %s, 网络请求: %d", 
            session.movieCode, duration, success, session.networkRequestCount);
    }
    
    /**
     * 获取当前统计快照
     */
    public MovieMetricsSnapshot getSnapshot() {
        MovieMetricsSnapshot snapshot = new MovieMetricsSnapshot();
        snapshot.totalMovies = totalMoviesProcessed.get();
        snapshot.successfulMovies = successfulExtractions.get();
        snapshot.failedMovies = failedExtractions.get();
        snapshot.activeThreads = activeThreads.get();
        snapshot.totalProcessingTime = totalProcessingTime.get();
        snapshot.minProcessingTime = minProcessingTime.get() == Long.MAX_VALUE ? 0 : minProcessingTime.get();
        snapshot.maxProcessingTime = maxProcessingTime.get();
        snapshot.averageProcessingTime = snapshot.totalMovies > 0 ? 
            snapshot.totalProcessingTime / snapshot.totalMovies : 0;
        snapshot.successRate = snapshot.totalMovies > 0 ? 
            (double) snapshot.successfulMovies / snapshot.totalMovies * 100 : 0;
        
        // 网络请求统计
        snapshot.totalNetworkRequests = totalNetworkRequests.get();
        snapshot.successfulNetworkRequests = successfulNetworkRequests.get();
        snapshot.failedNetworkRequests = failedNetworkRequests.get();
        snapshot.networkSuccessRate = snapshot.totalNetworkRequests > 0 ?
            (double) snapshot.successfulNetworkRequests / snapshot.totalNetworkRequests * 100 : 0;
        
        // 复制统计映射
        snapshot.movieCodePrefixStats = new ConcurrentHashMap<>();
        movieCodePrefixStats.forEach((k, v) -> snapshot.movieCodePrefixStats.put(k, v.get()));
        
        snapshot.errorStats = new ConcurrentHashMap<>();
        errorStats.forEach((k, v) -> snapshot.errorStats.put(k, v.get()));
        
        return snapshot;
    }
    
    /**
     * 打印详细的统计报告
     */
    public void printReport() {
        MovieMetricsSnapshot snapshot = getSnapshot();
        
        LOG.info("=== 电影信息处理统计报告 ===");
        LOG.infof("总电影数: %d", snapshot.totalMovies);
        LOG.infof("成功处理: %d", snapshot.successfulMovies);
        LOG.infof("处理失败: %d", snapshot.failedMovies);
        LOG.infof("成功率: %.2f%%", snapshot.successRate);
        LOG.infof("活跃线程: %d", snapshot.activeThreads);
        
        LOG.info("--- 处理时间统计 ---");
        LOG.infof("总处理时间: %dms", snapshot.totalProcessingTime);
        LOG.infof("平均处理时间: %dms", snapshot.averageProcessingTime);
        LOG.infof("最短处理时间: %dms", snapshot.minProcessingTime);
        LOG.infof("最长处理时间: %dms", snapshot.maxProcessingTime);
        
        LOG.info("--- 网络请求统计 ---");
        LOG.infof("总网络请求: %d", snapshot.totalNetworkRequests);
        LOG.infof("成功请求: %d", snapshot.successfulNetworkRequests);
        LOG.infof("失败请求: %d", snapshot.failedNetworkRequests);
        LOG.infof("网络成功率: %.2f%%", snapshot.networkSuccessRate);
        
        if (!snapshot.movieCodePrefixStats.isEmpty()) {
            LOG.info("--- 电影类型统计 ---");
            snapshot.movieCodePrefixStats.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .limit(10) // 只显示前10个最常见的前缀
                .forEach(entry -> LOG.infof("  %s: %d", entry.getKey(), entry.getValue()));
        }
        
        if (!snapshot.errorStats.isEmpty()) {
            LOG.info("--- 错误统计 ---");
            snapshot.errorStats.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .limit(5) // 只显示前5个最常见的错误
                .forEach(entry -> LOG.infof("  %s: %d", entry.getKey(), entry.getValue()));
        }
        
        LOG.info("============================");
    }
    
    /**
     * 重置所有统计
     */
    public void reset() {
        totalMoviesProcessed.set(0);
        successfulExtractions.set(0);
        failedExtractions.set(0);
        activeThreads.set(0);
        totalProcessingTime.set(0);
        minProcessingTime.set(Long.MAX_VALUE);
        maxProcessingTime.set(0);
        totalNetworkRequests.set(0);
        successfulNetworkRequests.set(0);
        failedNetworkRequests.set(0);
        movieCodePrefixStats.clear();
        errorStats.clear();
        activeSessions.clear();
        
        LOG.info("电影信息处理统计已重置");
    }
    
    private void updateMinTime(long duration) {
        long current = minProcessingTime.get();
        while (duration < current && !minProcessingTime.compareAndSet(current, duration)) {
            current = minProcessingTime.get();
        }
    }
    
    private void updateMaxTime(long duration) {
        long current = maxProcessingTime.get();
        while (duration > current && !maxProcessingTime.compareAndSet(current, duration)) {
            current = maxProcessingTime.get();
        }
    }
    
    private String generateSessionId(String movieCode) {
        return String.format("%s-%d", movieCode, System.currentTimeMillis());
    }
    
    /**
     * 提取电影代码前缀用于分类统计
     */
    private String extractMovieCodePrefix(String movieCode) {
        if (movieCode == null || movieCode.isEmpty()) {
            return "UNKNOWN";
        }
        
        // 提取字母前缀，例如从 "ABP-123" 提取 "ABP"
        int i = 0;
        while (i < movieCode.length() && Character.isLetter(movieCode.charAt(i))) {
            i++;
        }
        
        if (i > 0) {
            return movieCode.substring(0, i).toUpperCase();
        } else {
            return "NUMERIC";
        }
    }
    
    /**
     * 电影处理会话
     */
    private static class MovieProcessingSession {
        final String sessionId;
        final String movieCode;
        final Instant startTime;
        Instant endTime;
        int networkRequestCount = 0;
        int networkErrorCount = 0;
        
        MovieProcessingSession(String sessionId, String movieCode) {
            this.sessionId = sessionId;
            this.movieCode = movieCode;
            this.startTime = Instant.now();
        }
    }
    
    /**
     * 电影处理统计快照
     */
    public static class MovieMetricsSnapshot {
        public int totalMovies;
        public int successfulMovies;
        public int failedMovies;
        public int activeThreads;
        public long totalProcessingTime;
        public long averageProcessingTime;
        public long minProcessingTime;
        public long maxProcessingTime;
        public double successRate;
        
        // 网络请求统计
        public int totalNetworkRequests;
        public int successfulNetworkRequests;
        public int failedNetworkRequests;
        public double networkSuccessRate;
        
        public Map<String, Integer> movieCodePrefixStats;
        public Map<String, Integer> errorStats;
    }
}
