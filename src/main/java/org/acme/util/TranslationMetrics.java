package org.acme.util;

import org.jboss.logging.Logger;

import jakarta.enterprise.context.ApplicationScoped;
import java.time.Instant;
import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.Map;

/**
 * 翻译处理性能监控和统计工具
 * 用于跟踪多线程翻译处理的性能指标
 */
@ApplicationScoped
public class TranslationMetrics {
    
    private static final Logger LOG = Logger.getLogger(TranslationMetrics.class);
    
    // 计数器
    private final AtomicInteger totalTasksProcessed = new AtomicInteger(0);
    private final AtomicInteger successfulTranslations = new AtomicInteger(0);
    private final AtomicInteger failedTranslations = new AtomicInteger(0);
    private final AtomicInteger activeThreads = new AtomicInteger(0);
    
    // 时间统计
    private final AtomicLong totalProcessingTime = new AtomicLong(0);
    private final AtomicLong minProcessingTime = new AtomicLong(Long.MAX_VALUE);
    private final AtomicLong maxProcessingTime = new AtomicLong(0);
    
    // 语言统计
    private final ConcurrentHashMap<String, AtomicInteger> languageStats = new ConcurrentHashMap<>();
    
    // 内容类型统计
    private final ConcurrentHashMap<String, AtomicInteger> contentTypeStats = new ConcurrentHashMap<>();
    
    // 错误统计
    private final ConcurrentHashMap<String, AtomicInteger> errorStats = new ConcurrentHashMap<>();
    
    // 会话跟踪
    private final ConcurrentHashMap<String, SessionMetrics> activeSessions = new ConcurrentHashMap<>();
    
    /**
     * 开始一个翻译会话
     */
    public String startSession(String contentId, String contentType, String targetLanguage) {
        String sessionId = generateSessionId(contentId, contentType, targetLanguage);
        SessionMetrics session = new SessionMetrics(sessionId, contentId, contentType, targetLanguage);
        activeSessions.put(sessionId, session);
        activeThreads.incrementAndGet();
        
        LOG.debugf("开始翻译会话: %s", sessionId);
        return sessionId;
    }
    
    /**
     * 结束一个翻译会话
     */
    public void endSession(String sessionId, boolean success, String errorMessage) {
        SessionMetrics session = activeSessions.remove(sessionId);
        if (session == null) {
            LOG.warnf("找不到会话: %s", sessionId);
            return;
        }
        
        session.endTime = Instant.now();
        long duration = Duration.between(session.startTime, session.endTime).toMillis();
        
        // 更新统计
        totalTasksProcessed.incrementAndGet();
        totalProcessingTime.addAndGet(duration);
        activeThreads.decrementAndGet();
        
        // 更新最小/最大处理时间
        updateMinTime(duration);
        updateMaxTime(duration);
        
        if (success) {
            successfulTranslations.incrementAndGet();
            languageStats.computeIfAbsent(session.targetLanguage, k -> new AtomicInteger(0)).incrementAndGet();
            contentTypeStats.computeIfAbsent(session.contentType, k -> new AtomicInteger(0)).incrementAndGet();
        } else {
            failedTranslations.incrementAndGet();
            if (errorMessage != null) {
                errorStats.computeIfAbsent(errorMessage, k -> new AtomicInteger(0)).incrementAndGet();
            }
        }
        
        LOG.debugf("结束翻译会话: %s, 耗时: %dms, 成功: %s", sessionId, duration, success);
    }
    
    /**
     * 获取当前统计信息
     */
    public MetricsSnapshot getSnapshot() {
        MetricsSnapshot snapshot = new MetricsSnapshot();
        snapshot.totalTasks = totalTasksProcessed.get();
        snapshot.successfulTasks = successfulTranslations.get();
        snapshot.failedTasks = failedTranslations.get();
        snapshot.activeThreads = activeThreads.get();
        snapshot.totalProcessingTime = totalProcessingTime.get();
        snapshot.minProcessingTime = minProcessingTime.get() == Long.MAX_VALUE ? 0 : minProcessingTime.get();
        snapshot.maxProcessingTime = maxProcessingTime.get();
        snapshot.averageProcessingTime = snapshot.totalTasks > 0 ? 
            snapshot.totalProcessingTime / snapshot.totalTasks : 0;
        snapshot.successRate = snapshot.totalTasks > 0 ? 
            (double) snapshot.successfulTasks / snapshot.totalTasks * 100 : 0;
        
        // 复制统计映射
        snapshot.languageStats = new ConcurrentHashMap<>();
        languageStats.forEach((k, v) -> snapshot.languageStats.put(k, v.get()));
        
        snapshot.contentTypeStats = new ConcurrentHashMap<>();
        contentTypeStats.forEach((k, v) -> snapshot.contentTypeStats.put(k, v.get()));
        
        snapshot.errorStats = new ConcurrentHashMap<>();
        errorStats.forEach((k, v) -> snapshot.errorStats.put(k, v.get()));
        
        return snapshot;
    }
    
    /**
     * 打印统计报告
     */
    public void printReport() {
        MetricsSnapshot snapshot = getSnapshot();
        
        LOG.info("=== 翻译处理统计报告 ===");
        LOG.infof("总任务数: %d", snapshot.totalTasks);
        LOG.infof("成功任务: %d", snapshot.successfulTasks);
        LOG.infof("失败任务: %d", snapshot.failedTasks);
        LOG.infof("成功率: %.2f%%", snapshot.successRate);
        LOG.infof("活跃线程: %d", snapshot.activeThreads);
        LOG.infof("总处理时间: %dms", snapshot.totalProcessingTime);
        LOG.infof("平均处理时间: %dms", snapshot.averageProcessingTime);
        LOG.infof("最小处理时间: %dms", snapshot.minProcessingTime);
        LOG.infof("最大处理时间: %dms", snapshot.maxProcessingTime);
        
        if (!snapshot.languageStats.isEmpty()) {
            LOG.info("语言统计:");
            snapshot.languageStats.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .forEach(entry -> LOG.infof("  %s: %d", entry.getKey(), entry.getValue()));
        }
        
        if (!snapshot.contentTypeStats.isEmpty()) {
            LOG.info("内容类型统计:");
            snapshot.contentTypeStats.forEach((type, count) -> 
                LOG.infof("  %s: %d", type, count));
        }
        
        if (!snapshot.errorStats.isEmpty()) {
            LOG.info("错误统计:");
            snapshot.errorStats.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .limit(5) // 只显示前5个最常见的错误
                .forEach(entry -> LOG.infof("  %s: %d", entry.getKey(), entry.getValue()));
        }
        
        LOG.info("========================");
    }
    
    /**
     * 重置所有统计
     */
    public void reset() {
        totalTasksProcessed.set(0);
        successfulTranslations.set(0);
        failedTranslations.set(0);
        activeThreads.set(0);
        totalProcessingTime.set(0);
        minProcessingTime.set(Long.MAX_VALUE);
        maxProcessingTime.set(0);
        languageStats.clear();
        contentTypeStats.clear();
        errorStats.clear();
        activeSessions.clear();
        
        LOG.info("翻译统计已重置");
    }
    
    private void updateMinTime(long duration) {
        long current = minProcessingTime.get();
        while (duration < current && !minProcessingTime.compareAndSet(current, duration)) {
            current = minProcessingTime.get();
        }
    }
    
    private void updateMaxTime(long duration) {
        long current = maxProcessingTime.get();
        while (duration > current && !maxProcessingTime.compareAndSet(current, duration)) {
            current = maxProcessingTime.get();
        }
    }
    
    private String generateSessionId(String contentId, String contentType, String targetLanguage) {
        return String.format("%s-%s-%s-%d", 
            contentId, contentType, targetLanguage, System.currentTimeMillis());
    }
    
    /**
     * 会话指标
     */
    private static class SessionMetrics {
        final String sessionId;
        final String contentId;
        final String contentType;
        final String targetLanguage;
        final Instant startTime;
        Instant endTime;
        
        SessionMetrics(String sessionId, String contentId, String contentType, String targetLanguage) {
            this.sessionId = sessionId;
            this.contentId = contentId;
            this.contentType = contentType;
            this.targetLanguage = targetLanguage;
            this.startTime = Instant.now();
        }
    }
    
    /**
     * 统计快照
     */
    public static class MetricsSnapshot {
        public int totalTasks;
        public int successfulTasks;
        public int failedTasks;
        public int activeThreads;
        public long totalProcessingTime;
        public long averageProcessingTime;
        public long minProcessingTime;
        public long maxProcessingTime;
        public double successRate;
        public Map<String, Integer> languageStats;
        public Map<String, Integer> contentTypeStats;
        public Map<String, Integer> errorStats;
    }
}
