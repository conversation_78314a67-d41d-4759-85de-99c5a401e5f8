package org.acme.util;

import java.io.File;
import java.io.IOException;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class JdImageUploader {
    private static final String UPLOAD_URL = "https://search.jd.com/image?op=upload";
    private static final MediaType MEDIA_TYPE_OCTET = MediaType.parse("application/octet-stream");

    // 核心上传方法（与PHP逻辑完全对应）
    public static String uploadImage(File tmpFile, String originalFileName) throws Exception {
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(3, java.util.concurrent.TimeUnit.SECONDS)
                .build();

        // 构建multipart表单（完全匹配PHP的字段结构）
        MultipartBody.Builder builder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("file", originalFileName,
                        RequestBody.create(tmpFile, MEDIA_TYPE_OCTET));

        Request request = new Request.Builder()
                .url(UPLOAD_URL)
                .header("User-Agent", "Mozilla/5.0 (Linux; U; Android 4.0.4; es-mx; HTC_One_X Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0")
                .header("Accept", "application/json")
                .post(builder.build())
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("HTTP错误: " + response.code());
            }

            String responseBody = response.body().string();
            return parseImageUrl(responseBody);
        }
    }

    // 修正的正则表达式（完全匹配PHP的preg_match逻辑）
    private static String parseImageUrl(String responseText) throws Exception {
        Pattern pattern = Pattern.compile("callback\\\\s*\"([^\"]+)\"\\\\s*\\)");
        Matcher matcher = pattern.matcher(responseText);

        if (!matcher.find()) {
            throw new IOException("响应解析失败: " + responseText.substring(0, Math.min(50, responseText.length())));
        }

        String imageId = matcher.group(1);
        int subdomain = new Random().nextInt(5) + 10; // 10-14随机数
        return "https://img" + subdomain + ".360buyimg.com/uba/" + imageId;
    }

    // 完整处理流程（模拟PHP的$_FILES处理）
    public static String handleUpload(File tmpFile, String originalFileName) {
        try {
            if (!tmpFile.exists()) {
                return errorResponse(400, "文件不存在");
            }

            String imgUrl = uploadImage(tmpFile, originalFileName);
            return successResponse(imgUrl);

        } catch (Exception e) {
            return errorResponse(500, e.getMessage());
        }
    }

    // 生成成功响应（匹配PHP的json_encode结构）
    private static String successResponse(String imgUrl) {
        return "{\"code\":200,\"imgurl\":\"" + imgUrl + "\"}";
    }

    // 生成错误响应（匹配PHP的exit输出结构）
    private static String errorResponse(int code, String error) {
        return "{\"code\":" + code + ",\"error\":\"" + error.replace("\"", "\\\"") + "\"}";
    }

    // 示例使用（Servlet环境中）
    public static void main(String[] args) {
        // 模拟PHP的$_FILES数据
        File tmpFile = new File("data/哥布林杀手.png");
        String originalFileName = "哥布林杀手.jpg";

        System.out.println(handleUpload(tmpFile, originalFileName));
    }
}
