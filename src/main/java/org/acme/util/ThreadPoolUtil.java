package org.acme.util;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import org.jboss.logging.Logger;

/**
 * 线程池工具类，符合阿里巴巴Java开发规范
 * 提供线程池创建和管理的工具方法
 */
public class ThreadPoolUtil {
    
    private static final Logger logger = Logger.getLogger(ThreadPoolUtil.class);
    
    /**
     * 创建自定义线程池
     * 
     * @param namePrefix 线程名称前缀
     * @param corePoolSize 核心线程数
     * @param maximumPoolSize 最大线程数
     * @param queueCapacity 队列容量
     * @return 线程池实例
     */
    public static ThreadPoolExecutor createThreadPool(
        String namePrefix, 
        int corePoolSize, 
        int maximumPoolSize, 
        int queueCapacity) {
        
        logger.infof("创建线程池: %s, 核心线程数: %d, 最大线程数: %d, 队列容量: %d", 
                namePrefix, corePoolSize, maximumPoolSize, queueCapacity);
        
        return new ThreadPoolExecutor(
            corePoolSize,
            maximumPoolSize,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(queueCapacity),
            new NamedThreadFactory(namePrefix),
            new ThreadPoolExecutor.CallerRunsPolicy());
    }
    
    /**
     * 安全关闭线程池
     * 
     * @param executor 要关闭的线程池
     * @param timeoutSeconds 等待关闭的最大秒数
     */
    public static void shutdownThreadPool(ThreadPoolExecutor executor, int timeoutSeconds) {
        if (executor != null && !executor.isShutdown()) {
            try {
                logger.info("正在关闭线程池: " + executor);
                executor.shutdown();
                if (!executor.awaitTermination(timeoutSeconds, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                    if (!executor.awaitTermination(timeoutSeconds, TimeUnit.SECONDS)) {
                        logger.warn("线程池无法完全终止");
                    }
                }
                logger.info("线程池已关闭");
            } catch (InterruptedException ie) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
                logger.error("关闭线程池时被中断", ie);
            }
        }
    }

    /**
     * 命名线程工厂，用于创建具有指定前缀名称的线程
     */
    private static class NamedThreadFactory implements ThreadFactory {
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;
        private final ThreadGroup group;

        public NamedThreadFactory(String namePrefix) {
            this.group = Thread.currentThread().getThreadGroup();
            this.namePrefix = namePrefix + "-";
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(group, r, namePrefix + threadNumber.getAndIncrement());
            if (t.isDaemon()) {
                t.setDaemon(false); // 确保非守护线程
            }
            if (t.getPriority() != Thread.NORM_PRIORITY) {
                t.setPriority(Thread.NORM_PRIORITY); // 使用标准优先级
            }
            return t;
        }
    }
}
