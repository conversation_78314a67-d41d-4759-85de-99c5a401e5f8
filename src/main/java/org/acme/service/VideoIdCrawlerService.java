package org.acme.service;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;

import org.acme.entity.WatchUrl;
import org.acme.entity.Movie;
import org.acme.entity.VideoSource;
import org.acme.enums.WatchUrlStatus;
import org.acme.model.VScopeParseResult;
import org.acme.service.MovieParser.WatchInfo;
import org.acme.service.favourite.CollectionProcessService;
import org.acme.util.UrlPrefixReplacer;
import org.jboss.logging.Logger;

/**
 * Service to crawl videos by ID from 1 to 200000
 */
@ApplicationScoped
public class VideoIdCrawlerService {

    private static final Logger logger = Logger.getLogger(VideoIdCrawlerService.class);
    private static final int MAX_RETRIES = 3;
    private static final long DELAY_MS = 300; // 300ms delay between requests
    
    @Inject
    MovieParser movieParser;
    
    @Inject
    EntityManager entityManager;
    
    @Inject
    CollectionProcessService collectionProcessService;
    
    /**
     * Result class for processing range information
     */
    public static class ProcessingRangeResult {
        public Integer minId;
        public Integer maxId;
        public List<Integer> missingIds = new ArrayList<>();
    }
    
    /**
     * Get the range of movie IDs to process and find missing IDs in a single transaction
     * This method handles database operations in a proper transactional context
     * 
     * @param startId The starting ID to consider
     * @param endId The ending ID to consider
     * @param resultConsumer Consumer to process the result
     */
    @Transactional
    public void getProcessingRange(int startId, int endId, Consumer<ProcessingRangeResult> resultConsumer) {
        ProcessingRangeResult result = new ProcessingRangeResult();
        
        try {
            // Get minimum movie_id from database if exists
            Number dbMinId = (Number) entityManager
                    .createQuery("SELECT MIN(w.movieId) FROM WatchUrl w")
                    .getSingleResult();
                    
            // Get maximum movie_id from database if exists
            Number dbMaxId = (Number) entityManager
                    .createQuery("SELECT MAX(w.movieId) FROM WatchUrl w")
                    .getSingleResult();
            
            if (dbMinId != null) {
                result.minId = dbMinId.intValue();
            }
            
            if (dbMaxId != null) {
                result.maxId = dbMaxId.intValue();
                
                // Only find missing IDs if we have both min and max
                if (dbMinId != null) {
                    // Use native query to find gaps
                    String nativeQuery = 
                        "WITH series AS (" +
                        "  SELECT generate_series(?1, ?2) AS id" +
                        ")" +
                        "SELECT s.id AS missing_id " +
                        "FROM series s " +
                        "LEFT JOIN watch_urls w ON s.id = w.movie_id " +
                        "WHERE w.movie_id IS NULL";
                    
                    // Execute the query to find gaps
                    @SuppressWarnings("unchecked")
                    List<Number> gaps = entityManager
                            .createNativeQuery(nativeQuery)
                            .setParameter(1, Math.max(startId, dbMinId.intValue()))
                            .setParameter(2, dbMaxId.intValue())
                            .getResultList();
                    
                    // Convert the results to integers
                    for (Number gap : gaps) {
                        result.missingIds.add(gap.intValue());
                    }
                }
            }
            
            resultConsumer.accept(result);
        } catch (Exception e) {
            logger.errorf("Error getting processing range: %s", e.getMessage());
            logger.error("Stack trace:", e);
            // Return an empty result in case of error
            resultConsumer.accept(new ProcessingRangeResult());
        }
    }

    // This method is no longer needed as the controller now handles batch processin

    @Transactional
    public void processVideoUrls(MovieParser.VideoUrlsResult urlsResult, int movieId) {
        // Check if there are watch URLs to process
        if (urlsResult.watch() != null && !urlsResult.watch().isEmpty()) {
            // Iterate through the list of WatchInfo objects
            for (MovieParser.WatchInfo watchInfo : urlsResult.watch()) {
                VScopeParseResult m3u8Result = movieParser.extractM3U8FromPlayer(watchInfo.url());
                // Find existing WatchUrl or create a new one if it doesn't exist
                WatchUrl watchUrl = new WatchUrl();
                watchUrl.setMovieId(movieId);
                watchUrl.setUrl(m3u8Result.getStream());
                watchUrl.setIndex(watchInfo.index());
                watchUrl.setName(watchInfo.name());
                watchUrl.setOriginalUrl(watchInfo.url());
                watchUrl.setStatus(WatchUrlStatus.SUCCEED.getValue());
                WatchUrl.persist(watchUrl);
            }
        }
    }

    /**
     * Count how many records have the specified URL prefix
     *
     * @param newPrefix the prefix to search for
     * @return the number of records with the specified prefix
     */
    @Transactional
    public long countUrlsWithoutPrefix(String newPrefix) {
        if (newPrefix == null || newPrefix.isEmpty()) {
            return 0;
        }
        return WatchUrl.count("originalUrl not LIKE ?1", newPrefix + "%");
    }

    /**
     * Get a batch of WatchUrl records with the specified URL prefix
     *
     * @param newPrefix the prefix to search for
     * @param batchSize the number of records to retrieve
     * @return a list of WatchUrl records
     */
    @Transactional
    public List<WatchUrl> getUrlBatch(String newPrefix, int batchSize) {
        if (newPrefix == null || newPrefix.isEmpty()) {
            return List.of();
        }
        return WatchUrl.find("originalUrl not LIKE ?1", newPrefix + "%")
                .page(0, batchSize)
                .list();
    }

    /**
     * Update a single WatchUrl record with a new prefix
     *
     * @param watchUrl the WatchUrl record which might be detached
     * @param newPrefix the new prefix to use
     * @return true if the update was successful, false otherwise
     */
    @Transactional
    public boolean updateSingleUrl(WatchUrl watchUrl, String newPrefix) {
        if (watchUrl == null || newPrefix == null) {
            return false;
        }

        Long id = watchUrl.id;
        int movieId = watchUrl.getMovieId();
        String originalUrl = watchUrl.getOriginalUrl();

        if (originalUrl == null) {
            return false;
        }

        try {
            // Get a fresh attached entity instead of using the potentially detached one
            WatchUrl freshWatchUrl;
            if (id != null) {
                // If we have an ID, find by ID
                freshWatchUrl = WatchUrl.findById(id);
            } else {
                // Otherwise find by movieId (unique constraint)
                freshWatchUrl = WatchUrl.find("movieId = ?1", movieId).firstResult();
            }

            if (freshWatchUrl == null) {
                logger.warnf("Could not find WatchUrl with ID %d or movieId %d", id, movieId);
                return false;
            }

            // Replace the prefix
            String newUrl = UrlPrefixReplacer.replaceUrlPrefix(originalUrl, newPrefix);
            freshWatchUrl.setOriginalUrl(newUrl);

            // Update m3u8 URL if needed by re-processing
            try {
                VScopeParseResult m3u8Result = movieParser.extractM3U8FromPlayer(newUrl);
                if (m3u8Result != null && m3u8Result.getStream() != null) {
                    freshWatchUrl.setUrl(m3u8Result.getStream());
                }
            } catch (Exception e) {
                logger.warnf("Failed to update m3u8 URL for movie ID %d: %s",
                            movieId, e.getMessage());
            }

            // Persist changes - now with a managed entity
            freshWatchUrl.persist();
            logger.infof("Successfully updated URL for movie ID %d", movieId);
            return true;
        } catch (Exception e) {
            logger.errorf("Error updating URL for movie ID %d: %s",
                        movieId, e.getMessage());
            return false;
        }
    }

    public void updateMovieStatus(long videoId, String status) {
        WatchUrl watchUrl = WatchUrl.findById(videoId);
        if (watchUrl != null) {
            watchUrl.setStatus(status);
            watchUrl.persist();
        }
    }

    public void processVideoById(int videoId) {
        // Check if this ID already exists and has been successfully processed
        WatchUrl existingUrl = WatchUrl.find("movieId = ?1", videoId).firstResult();
        if (existingUrl != null) {
            WatchUrlStatus status = WatchUrlStatus.fromValue(existingUrl.getStatus());
            if (status != null && status.noNeedRetry()) {
                logger.debugf("Skipping already processed video ID %d (status: %s)", 
                        videoId, existingUrl.getStatus());
                return;
            }
        }
        
        // Process this ID with retries
        try {       
            logger.infof("Processing video ID: %d", videoId);

            Optional<MovieParser.VideoUrlsResult> result = movieParser.getVideoUrls(videoId);

            if (result.isPresent() && !result.get().watch().isEmpty()) {
                // Get the result and check if it has any watch or download URLs
                MovieParser.VideoUrlsResult urlsResult = result.get();
                boolean hasWatchUrls = urlsResult.watch() != null && !urlsResult.watch().isEmpty();

                // Process based on availability of URLs
                if (!hasWatchUrls) {
                    logger.infof("Video ID %d has no watch or download URLs", videoId);
                    return;
                }

                // Try to save watch and download URLs
                try {
                    processVideoUrls(urlsResult, (int)videoId);
                    logger.infof("Successfully processed video ID: %d", videoId);
                } catch (Exception e) {
                    logger.errorf("Error processing video ID %d: %s", videoId, e.getMessage());
                    throw e; // Re-throw to be handled by the controller
                }

                // 检查对应 movies 是否存在
                Movie movie = Movie.find("originalId = ?1", videoId).firstResult();
                if (movie == null) {
                    logger.infof("Movie ID %d not found", videoId);
                    collectionProcessService.addIdsToFavourites(Set.of(videoId));
                }
            } else {
                // create placeholder watch url
                createPlaceholderWatchUrl(videoId);
                logger.infof("No data found for video ID: %d", videoId);
            }
        } catch (Exception e) {
            // Make sure to mark the movie as failed if we have a reference to it
            logger.errorf("Error processing video ID %d: %s", videoId, e.getMessage());
            throw e; // Re-throw to be handled by the controller
        }
    }

    @Transactional
    public void createPlaceholderWatchUrl(int videoId) {
        WatchUrl watchUrl = new WatchUrl();
        watchUrl.setMovieId(videoId);
        watchUrl.setStatus(WatchUrlStatus.NO_RESULT.getValue());
        WatchUrl.persist(watchUrl);
    }
}
