package org.acme.service.feed;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.acme.entity.Movie;
import org.acme.entity.MovieInfo;
import org.acme.entity.WatchUrl;
import org.acme.service.VideoIdCrawlerService;
import org.acme.service.browser.BrowserLoginService;
import org.acme.service.favourite.CollectionProcessService;
import org.acme.service.favourite.FavouriteService;
import org.acme.util.HttpClientUtils;
import org.jboss.logging.Logger;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.context.control.ActivateRequestContext;
import jakarta.inject.Inject;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.acme.service.MovieInfoExtractionService;
import org.acme.service.MovieService;

/**
 * Service for processing user feed
 * This service handles fetching latest videos from the feed page and processing them
 */
@ApplicationScoped
public class FeedService {

    private static final Logger logger = Logger.getLogger(FeedService.class);
    private static final String FEED_BASE_URL = "https://123av.com/ja/user/feed?sort=recent_update";

    private static final int FAVOURITE_PAGE_LIMIT = 1000;
    private static final int FAVOURITE_MOVIE_LIMIT = 12000;
    private final OkHttpClient httpClient;
    private final Pattern favouritePattern = Pattern.compile("Favourite\\('movie', (\\d+), \\d+\\)");
    
    @Inject
    CollectionProcessService collectionProcessService;
    
    @Inject
    FavouriteService favouriteService;

    @Inject
    VideoIdCrawlerService videoIdCrawlerService;
    
    @Inject
    BrowserLoginService browserLoginService;

    @Inject
    MovieInfoExtractionService movieInfoExtractionService;

    @Inject
    MovieService movieService;

    public FeedService() {
        this.httpClient = HttpClientUtils.createHttpClient();
    }

    /**
     * Get the total number of feed pages
     * 
     * @return Total number of feed pages
     */
    public int getTotalFeedPages() {
        try {
            // Get cached cookies or fetch new ones if needed
            String cookieString = browserLoginService.getAuthCookies();
            
            Request request = new Request.Builder()
                    .url(FEED_BASE_URL)
                    .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3")
                    .header("Cookie", cookieString)
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    int responseCode = response.code();
                    logger.errorf("Failed to fetch feed page: %d", responseCode);
                    
                    // If unauthorized, refresh cookies and try again
                    if (responseCode == 401) {
                        if (browserLoginService.handleUnauthorized()) {
                            logger.info("Retrying after cookie refresh");
                            return getTotalFeedPages(); // Recursive call to retry with new cookies
                        }
                    }
                    return 0;
                }

                String html = response.body().string();
                Document doc = Jsoup.parse(html);
                
                // Look for pagination info
                Elements paginationItems = doc.select("nav.navigation li.page-item:not(.disabled)");
                if (!paginationItems.isEmpty()) {
                    // Get the last page number
                    String lastPageText = paginationItems.get(paginationItems.size() - 3).text();
                    try {
                        return Integer.parseInt(lastPageText);
                    } catch (NumberFormatException e) {
                        logger.warnf("Could not parse last page number: %s", lastPageText);
                    }
                }
                
                // Alternative method - look for Navigation component with lastPage property
                Elements navigationElements = doc.select("[v-scope^=Navigation]");
                for (Element navElement : navigationElements) {
                    String vScope = navElement.attr("v-scope");
                    Pattern pattern = Pattern.compile("lastPage: (\\d+)");
                    Matcher matcher = pattern.matcher(vScope);
                    if (matcher.find()) {
                        return Integer.parseInt(matcher.group(1));
                    }
                }
            }
        } catch (Exception e) {
            logger.errorf("Error getting total feed pages: %s", e.getMessage());
        }
        
        return 1; // Default to at least 1 page
    }

    /**
     * Fetch movie IDs from a specific feed page
     * 
     * @param pageNumber Page number to fetch
     * @return Set of movie IDs found on the page
     */
    public Set<Integer> getFeedMovieIdsFromPageAndSaveMovies(int pageNumber) {
        Set<Integer> movieIds = new HashSet<>();
        String url = FEED_BASE_URL + (pageNumber > 1 ? "?page=" + pageNumber : "");
        
        try {
            // Get cached cookies or fetch new ones if needed
            String cookieString = browserLoginService.getAuthCookies();
            
            Request request = new Request.Builder()
                    .url(url)
                    .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3")
                    .header("Cookie", cookieString)
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    int responseCode = response.code();
                    logger.errorf("Failed to fetch feed page %d: %d", pageNumber, responseCode);
                    
                    // If unauthorized, refresh cookies and try again
                    if (responseCode == 401) {
                        if (browserLoginService.handleUnauthorized()) {
                            logger.info("Retrying after cookie refresh");
                            return getFeedMovieIdsFromPageAndSaveMovies(pageNumber); // Recursive call to retry with new cookies
                        }
                    }
                    return movieIds;
                }

                String html = response.body().string();
                List<Movie> feedMovies = collectionProcessService.extractMovieFromElement(html);
                // updateOrInsertMovies
                movieService.saveOrUpdateMovies(feedMovies);
                movieIds = feedMovies.stream().map(Movie::getOriginalId).collect(Collectors.toSet());
            }
        } catch (Exception e) {
            logger.errorf("Error getting movie IDs from feed page %d: %s", pageNumber, e.getMessage());
        }
        
        return movieIds;
    }
    
    /**
     * Get movie IDs from feed pages with pagination
     * 
     * @param pagesToFetch Number of pages to fetch
     * @return Set of movie IDs found on the pages
     */
    public void handleFeedMovies(int pagesToFetch) {
        
        int totalPages = getTotalFeedPages();
        int pagesToProcess = Math.min(pagesToFetch, totalPages);
        
        logger.infof("Fetching %d feed pages out of %d total pages", pagesToProcess, totalPages);
        
        for (int page = 1; page <= pagesToProcess; page++) {
            Set<Integer> pageMovieIds = getFeedMovieIdsFromPageAndSaveMovies(page);
            logger.infof("Found %d movie IDs on feed page %d", pageMovieIds.size(), page);

            // if all pageMovieIds exist in watchUrl, break
            if (!pageMovieIds.isEmpty()) {
                // 找到 watchurl 中没有的 movieIds
                Set<Integer> movieIdsNotInWatchUrl = pageMovieIds.stream()
                        .filter(movieId -> WatchUrl.count("movieId = ?1", movieId) == 0)
                        .collect(Collectors.toSet());
                logger.infof("Found %d movie IDs not in watchUrl on feed page %d", movieIdsNotInWatchUrl.size(), page);
                if (movieIdsNotInWatchUrl.isEmpty()) {
                    logger.info("All movies on this page already exist in database, stopping pagination processing");
                    break;
                }
                // 爬取对应的 watchUrl
                movieIdsNotInWatchUrl.forEach(videoIdCrawlerService::processVideoById);
            }
            
        }
    }
    
    /**
     * Process feed movies - fetch and update movies from feed
     * 
     * @param pagesToFetch Number of pages to fetch from feed
     * @return Processing results
     */
    public Map<String, Object> processFeedMovies(int pagesToFetch) {
        logger.info("Starting feed movie processing");
        AtomicInteger newMoviesCount = new AtomicInteger(0);
        AtomicInteger existingMoviesCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        
        try {
            // 根据feed页面获取新增的 movies.直到某页面的movie全部存在则停止
            handleFeedMovies(pagesToFetch);

            // 处理剩下的还没有被获取的 movies
            handleMovieInfo(pagesToFetch);
        } catch (Exception e) {
            logger.errorf("Error processing feed movies: %s", e.getMessage());
            logger.error("Stack trace:", e);
            errorCount.incrementAndGet();
        }
        
        // Return results
        Map<String, Object> results = Map.of(
            "new_movies", newMoviesCount.get(),
            "existing_movies", existingMoviesCount.get(),
            "errors", errorCount.get()
        );
        
        return results;
    }
    
    private void handleMovieInfo(Integer pages) {
        logger.infof("Starting handle unprocessed movies to get movie info");
    
        boolean hasMore = true;

        // Step 1: Check existing favorites and handle them
        collectionProcessService.handleExistingFavorites(pages);

        try {
            // Step 2: Process new batch (non-transactional coordination method)
            // Continue processing batches until no more movies are found
            while (hasMore) {
                logger.infof("Still have movies to process");

                // Find movies that need processing
                Set<Integer> moviesToProcess = collectionProcessService.findMoviesForProcessingNonTransactional();
                if (moviesToProcess.isEmpty()) {
                    logger.info("No more movies found for processing, stopping");
                    hasMore = false;
                    break;
                }  
                
                if (moviesToProcess.size() > FAVOURITE_MOVIE_LIMIT) {
                    logger.infof("Found %d movies to process, but only processing %d", moviesToProcess.size(), FAVOURITE_MOVIE_LIMIT);
                    moviesToProcess = moviesToProcess.stream().limit(FAVOURITE_MOVIE_LIMIT).collect(Collectors.toSet());
                }

                logger.infof("Found %d movies to process", moviesToProcess.size());
                
                // Convert Set to List for easier batch processing
                List<Integer> movieIdList = new ArrayList<>(moviesToProcess);
                 
                try {
                    collectionProcessService.processMovieGroupNonTransactional(movieIdList);
                    logger.infof("Successfully processed %d movies in group", 
                            movieIdList.size());
                } catch (Exception e) {
                    logger.errorf("Error processing movie group: %s", e.getMessage());
                }

                // Optional: Add a short pause between batches to avoid overloading the system
                try {
                    Thread.sleep(1000); // 1 second pause between batches
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
            
            logger.infof("Continuous processing completed");
            
        } catch (Exception e) {
            logger.errorf("Error in continuous processing: %s", e.getMessage());
            logger.error("Stack trace:", e);
        }
    }
    
    /**
     * Categorize movie IDs into new and existing based on database records
     * 
     * @param movieIds Set of movie IDs to categorize
     * @return Map with "new" and "existing" sets of movie IDs
     */
    private Map<String, Set<Integer>> categorizeMovieIds(Set<Integer> movieIds) {
        Set<Integer> newIds = new HashSet<>(movieIds);
        Set<Integer> existingIds = new HashSet<>();
        
        try {
            // Find existing movie IDs in database
            List<WatchUrl> existingUrls = WatchUrl.list("movieId in ?1", movieIds);
            List<Integer> existingMovieIds = existingUrls.stream()
                    .map(WatchUrl::getMovieId)
                    .collect(Collectors.toList());
            
            // Remove existing IDs from new IDs set
            newIds.removeAll(existingMovieIds);
            
            // Add existing IDs to existingIds set
            existingIds.addAll(existingMovieIds);
            
            logger.infof("Categorized movies: %d new, %d existing", newIds.size(), existingIds.size());
        } catch (Exception e) {
            logger.errorf("Error categorizing movie IDs: %s", e.getMessage());
        }
        
        return Map.of(
            "new", newIds,
            "existing", existingIds
        );
    }
}
