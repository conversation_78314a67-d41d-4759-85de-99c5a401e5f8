package org.acme.service.proxy;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import org.acme.config.ProxyConfig;
import org.jboss.logging.Logger;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * Service for fetching proxy information from external API and updating the proxy pool.
 * Uses the API at https://proxy.010438.xyz/api/get to fetch international proxies.
 */
@ApplicationScoped
public class ProxyFetcherService {
    private static final Logger LOG = Logger.getLogger(ProxyFetcherService.class);
    private static final String PROXY_API_URL = "https://proxy.010438.xyz/api/get";
    private static final ObjectMapper MAPPER = new ObjectMapper();
    
    @Inject
    ProxyConfig proxyConfig;
    
    private final OkHttpClient client = new OkHttpClient.Builder()
        .connectTimeout(10, TimeUnit.SECONDS)
        .readTimeout(10, TimeUnit.SECONDS)
        .build();
    
    /**
     * Fetches a new international proxy (excluding China) and adds it to the proxy pool.
     * 
     * @return true if a proxy was successfully fetched and added, false otherwise
     */
    public boolean fetchAndAddInternationalProxy() {
        try {
            // Build the request URL with parameters
            HttpUrl url = HttpUrl.parse(PROXY_API_URL).newBuilder()
                .addQueryParameter("type", "1") // HTTP proxy
                .addQueryParameter("country", "外国") // Non-China countries
                .addQueryParameter("anonymous", "3") // High anonymity
                .addQueryParameter("supportHttps", "1") // Support HTTPS
                .addQueryParameter("supportPost", "1") // Support POST
                .build();
            
            Request request = new Request.Builder()
                .url(url)
                .build();
            
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    LOG.errorf("Failed to fetch proxy: HTTP %d", response.code());
                    return false;
                }
                
                String responseBody = response.body().string();
                JsonNode jsonNode = MAPPER.readTree(responseBody);
                
                if (jsonNode.has("code") && jsonNode.get("code").asInt() == 200 && jsonNode.has("data")) {
                    JsonNode data = jsonNode.get("data");
                    
                    String ip = data.get("ip").asText();
                    int port = data.get("port").asInt();
                    String country = data.get("country").asText();
                    String type = data.get("type").asText();
                    int anonymous = data.get("anonymous").asInt();
                    
                    LOG.infof("Fetched proxy: %s:%d (Country: %s, Type: %s, Anonymous: %d)", 
                        ip, port, country, type, anonymous);
                    
                    // Add the proxy to the pool
                    // Group is set to "api_fetched" and priority to 5 (medium priority)
                    proxyConfig.addProxy(ip, port, null, null, "api_fetched", 5);
                    
                    return true;
                } else {
                    LOG.errorf("Invalid response from proxy API: %s", responseBody);
                    return false;
                }
            }
        } catch (IOException e) {
            LOG.errorf("Error fetching proxy: %s", e.getMessage());
            return false;
        }
    }
    
    /**
     * Fetches multiple international proxies and adds them to the proxy pool.
     * 
     * @param count Number of proxies to fetch
     * @return Number of proxies successfully fetched and added
     */
    public int fetchMultipleProxies(int count) {
        int successCount = 0;
        
        for (int i = 0; i < count; i++) {
            if (fetchAndAddInternationalProxy()) {
                successCount++;
                
                // Add a small delay between requests to avoid rate limiting
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    LOG.warn("Interrupted while waiting between proxy fetch requests");
                    break;
                }
            }
        }
        
        LOG.infof("Successfully fetched and added %d/%d proxies", successCount, count);
        return successCount;
    }
    
    /**
     * Refreshes the proxy pool by fetching new proxies.
     * This method will fetch new proxies up to the specified limit.
     * 
     * @param minProxies Minimum number of proxies to maintain in the pool
     * @param maxProxies Maximum number of proxies to have in the pool
     * @return Number of new proxies added
     */
    public int refreshProxyPool(int minProxies, int maxProxies) {
        int currentSize = proxyConfig.size();
        
        // If we already have enough proxies, don't fetch more
        if (currentSize >= minProxies) {
            LOG.infof("Proxy pool already has %d proxies (min: %d), no need to refresh", 
                currentSize, minProxies);
            return 0;
        }
        
        // Calculate how many proxies to fetch
        int fetchCount = Math.min(maxProxies - currentSize, minProxies - currentSize);
        
        LOG.infof("Refreshing proxy pool: current size=%d, fetching %d more proxies", 
            currentSize, fetchCount);
        
        return fetchMultipleProxies(fetchCount);
    }
}
