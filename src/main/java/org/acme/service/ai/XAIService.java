package org.acme.service.ai;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.jboss.logging.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Service for interacting with XAI API.
 * Provides higher-level functionality on top of the XAIClient.
 */
@ApplicationScoped
public class XAIService {
    private static final Logger logger = Logger.getLogger(XAIService.class);

    @Inject
    XAIClient xaiClient;

    /**
     * Generate text based on a prompt
     *
     * @param prompt The input prompt
     * @return Generated text response
     */
    public String generateText(String prompt) {
        return generateText(prompt, 0.7, 1000);
    }

    /**
     * Generate text based on a prompt with custom parameters
     *
     * @param prompt The input prompt
     * @param temperature Temperature setting (0.0-1.0)
     * @param maxTokens Maximum tokens to generate
     * @return Generated text response
     */
    public String generateText(String prompt, double temperature, int maxTokens) {
        try {
            logger.info("Generating text with XAI");
            return xaiClient.generateText(prompt, temperature, maxTokens);
        } catch (Exception e) {
            logger.error("Error generating text with XAI", e);
            return "Error: " + e.getMessage();
        }
    }

    /**
     * Generate chat completion based on a conversation
     *
     * @param systemPrompt System message to set context
     * @param userPrompt User's message
     * @return Generated assistant response
     */
    public String generateChatResponse(String systemPrompt, String userPrompt) {
        try {
            List<Map<String, String>> messages = new ArrayList<>();
            
            // Add system message
            Map<String, String> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");
            systemMessage.put("content", systemPrompt);
            messages.add(systemMessage);
            
            // Add user message
            Map<String, String> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", userPrompt);
            messages.add(userMessage);
            
            logger.info("Generating chat completion with XAI");
            return xaiClient.generateChatCompletion(messages, 0.7, 1000);
        } catch (Exception e) {
            logger.error("Error generating chat completion with XAI", e);
            return "Error: " + e.getMessage();
        }
    }

    /**
     * Generate chat completion based on a full conversation history
     *
     * @param messages List of message maps with "role" and "content" keys
     * @return Generated assistant response
     */
    public String generateChatResponse(List<Map<String, String>> messages) {
        try {
            logger.info("Generating chat completion with XAI from conversation history");
            return xaiClient.generateChatCompletion(messages, 0.7, 1000);
        } catch (Exception e) {
            logger.error("Error generating chat completion with XAI", e);
            return "Error: " + e.getMessage();
        }
    }

    /**
     * Generate embeddings for given text
     *
     * @param text Text to generate embeddings for
     * @return List of floating point numbers representing the embedding
     */
    public List<Float> generateEmbedding(String text) {
        try {
            logger.info("Generating embeddings with XAI");
            return xaiClient.generateEmbedding(text);
        } catch (Exception e) {
            logger.error("Error generating embeddings with XAI", e);
            return new ArrayList<>();
        }
    }

    /**
     * Classify text into categories using XAI
     *
     * @param text Text to classify
     * @param categories List of possible categories
     * @return The most likely category
     */
    public String classifyText(String text, List<String> categories) {
        try {
            // Build prompt for classification
            StringBuilder prompt = new StringBuilder();
            prompt.append("Classify the following text into one of these categories: ");
            prompt.append(String.join(", ", categories));
            prompt.append("\n\nText: \"");
            prompt.append(text);
            prompt.append("\"\n\nCategory:");

            // Generate classification
            String response = generateText(prompt.toString(), 0.3, 100).trim();
            
            // Find the best matching category from the response
            for (String category : categories) {
                if (response.contains(category)) {
                    return category;
                }
            }
            
            // If no exact match, return the raw response
            return response;
        } catch (Exception e) {
            logger.error("Error classifying text with XAI", e);
            return "Error: " + e.getMessage();
        }
    }

    /**
     * Summarize text using XAI
     *
     * @param text Text to summarize
     * @param maxLength Maximum length of summary in tokens
     * @return Summarized text
     */
    public String summarizeText(String text, int maxLength) {
        try {
            // Build prompt for summarization
            String prompt = "Summarize the following text in a concise way:\n\n" + text;
            
            // Generate summary
            return generateText(prompt, 0.7, maxLength);
        } catch (Exception e) {
            logger.error("Error summarizing text with XAI", e);
            return "Error: " + e.getMessage();
        }
    }
}
