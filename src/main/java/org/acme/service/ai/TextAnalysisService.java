package org.acme.service.ai;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.jboss.logging.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Service for text analysis using XAI.
 * Demonstrates how to use XAI for various text analysis tasks.
 */
@ApplicationScoped
public class TextAnalysisService {
    private static final Logger logger = Logger.getLogger(TextAnalysisService.class);

    @Inject
    XAIService xaiService;

    /**
     * Analyze sentiment of text
     * 
     * @param text Text to analyze
     * @return Sentiment analysis result (positive, negative, neutral)
     */
    public String analyzeSentiment(String text) {
        try {
            logger.info("Analyzing sentiment of text");
            
            // Create system prompt for sentiment analysis
            String systemPrompt = "You are a sentiment analysis expert. " +
                "Analyze the sentiment of the given text and respond with exactly one word: " +
                "POSITIVE, NEGATIVE, or NEUTRAL. Do not include any explanation or additional text.";
            
            // Get response from XAI
            String response = xaiService.generateChatResponse(systemPrompt, text);
            
            // Clean up response
            response = response.trim().toUpperCase();
            
            // Validate response
            if (response.contains("POSITIVE")) {
                return "POSITIVE";
            } else if (response.contains("NEGATIVE")) {
                return "NEGATIVE";
            } else {
                return "NEUTRAL";
            }
        } catch (Exception e) {
            logger.error("Error analyzing sentiment", e);
            return "ERROR";
        }
    }
    
    /**
     * Extract key information from text
     * 
     * @param text Text to analyze
     * @param fields List of fields to extract (e.g., "name", "date", "location")
     * @return Map of extracted fields and their values
     */
    public Map<String, String> extractInformation(String text, List<String> fields) {
        try {
            logger.info("Extracting information from text");
            
            // Create system prompt for information extraction
            StringBuilder systemPrompt = new StringBuilder();
            systemPrompt.append("You are an information extraction expert. ");
            systemPrompt.append("Extract the following fields from the given text: ");
            systemPrompt.append(String.join(", ", fields));
            systemPrompt.append(". ");
            systemPrompt.append("Respond in JSON format with the field names as keys and the extracted values as values. ");
            systemPrompt.append("If a field cannot be found, set its value to null. ");
            systemPrompt.append("Do not include any explanation or additional text outside the JSON structure.");
            
            // Get response from XAI
            String response = xaiService.generateChatResponse(systemPrompt.toString(), text);
            
            // Parse JSON response
            Map<String, String> result = new HashMap<>();
            
            // Simple JSON parsing (in a real implementation, use a proper JSON parser)
            for (String field : fields) {
                String pattern = "\"" + field + "\"\\s*:\\s*\"([^\"]*)\"";
                java.util.regex.Pattern r = java.util.regex.Pattern.compile(pattern);
                java.util.regex.Matcher m = r.matcher(response);
                if (m.find()) {
                    result.put(field, m.group(1));
                } else {
                    result.put(field, null);
                }
            }
            
            return result;
        } catch (Exception e) {
            logger.error("Error extracting information", e);
            return new HashMap<>();
        }
    }
    
    /**
     * Categorize text into predefined categories
     * 
     * @param text Text to categorize
     * @param categories List of possible categories
     * @return The most appropriate category
     */
    public String categorizeText(String text, List<String> categories) {
        try {
            logger.info("Categorizing text");
            return xaiService.classifyText(text, categories);
        } catch (Exception e) {
            logger.error("Error categorizing text", e);
            return "ERROR";
        }
    }
    
    /**
     * Generate keywords from text
     * 
     * @param text Text to analyze
     * @param count Number of keywords to generate
     * @return List of keywords
     */
    public List<String> generateKeywords(String text, int count) {
        try {
            logger.info("Generating keywords from text");
            
            // Create system prompt for keyword extraction
            String systemPrompt = "You are a keyword extraction expert. " +
                "Extract the " + count + " most important keywords from the given text. " +
                "Respond with only the keywords, separated by commas. " +
                "Do not include any explanation or additional text.";
            
            // Get response from XAI
            String response = xaiService.generateChatResponse(systemPrompt, text);
            
            // Parse response
            List<String> keywords = new ArrayList<>();
            for (String keyword : response.split(",")) {
                keyword = keyword.trim();
                if (!keyword.isEmpty()) {
                    keywords.add(keyword);
                }
            }
            
            return keywords;
        } catch (Exception e) {
            logger.error("Error generating keywords", e);
            return new ArrayList<>();
        }
    }
}
