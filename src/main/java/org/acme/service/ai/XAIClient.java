package org.acme.service.ai;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import okhttp3.*;
import org.acme.config.XAIConfig;
import org.acme.config.XAIConfig.ClientConfig;
import org.jboss.logging.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Direct client for XAI API.
 * This provides a reliable implementation for XAI integration.
 */
@ApplicationScoped
public class XAIClient {

    private static final Logger logger = Logger.getLogger(XAIClient.class);
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    private static final ObjectMapper mapper = new ObjectMapper();

    private final OkHttpClient client = new OkHttpClient.Builder()
        .connectTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
        .writeTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
        .readTimeout(120, java.util.concurrent.TimeUnit.SECONDS) // Longer read timeout for large responses
        .build();

    // Client configuration and selection
    @Inject
    XAIConfig config;

    // Track client usage for rotation
    private final AtomicInteger requestCounter = new AtomicInteger(0);
    private ClientConfig currentClient = null;

    /**
     * Generate text using XAI model
     * 
     * @param prompt The prompt to send to the model
     * @param temperature Temperature setting (0.0-1.0)
     * @param maxTokens Maximum tokens to generate
     * @return Generated text response
     */
    public String generateText(String prompt, double temperature, int maxTokens) {
        try {
            // Select client configuration
            ClientConfig clientConfig = selectClient();
            
            // Build URL for XAI API
            String url = String.format("%s/completions", 
                clientConfig.getEndpoint());
            
            // Create request body
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", clientConfig.getModelId());
            requestBody.put("prompt", prompt);
            requestBody.put("temperature", temperature);
            requestBody.put("max_tokens", Math.min(maxTokens, clientConfig.getOutputMaxTokens()));
            
            // Convert request body to JSON
            String jsonBody = mapper.writeValueAsString(requestBody);
            
            // Create request
            Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(jsonBody, JSON))
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + clientConfig.getApiKey())
                .build();
            
            // Execute request
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "No response body";
                    logger.errorf("XAI API request failed with code %d: %s", response.code(), errorBody);
                    return "Error: " + response.code() + " - " + errorBody;
                }
                
                // Parse response
                String responseBody = response.body().string();
                JsonNode jsonResponse = mapper.readTree(responseBody);
                
                // Extract generated text
                if (jsonResponse.has("choices") && jsonResponse.get("choices").isArray() && 
                    jsonResponse.get("choices").size() > 0) {
                    return jsonResponse.get("choices").get(0).get("text").asText();
                } else {
                    logger.error("Unexpected response format from XAI API");
                    return "Error: Unexpected response format";
                }
            }
        } catch (Exception e) {
            logger.error("Error calling XAI API", e);
            return "Error: " + e.getMessage();
        }
    }
    
    /**
     * Generate chat completion using XAI model
     * 
     * @param messages List of message maps with "role" and "content" keys
     * @param temperature Temperature setting (0.0-1.0)
     * @param maxTokens Maximum tokens to generate
     * @return Generated assistant response
     */
    public String generateChatCompletion(List<Map<String, String>> messages, double temperature, int maxTokens) {
        try {
            // Select client configuration
            ClientConfig clientConfig = selectClient();
            
            // Build URL for XAI API
            String url = String.format("%s/chat/completions", 
                clientConfig.getEndpoint());
            
            // Create request body
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", clientConfig.getModelId());
            requestBody.put("messages", messages);
            requestBody.put("temperature", temperature);
            requestBody.put("max_tokens", Math.min(maxTokens, clientConfig.getOutputMaxTokens()));
            
            // Convert request body to JSON
            String jsonBody = mapper.writeValueAsString(requestBody);
            
            // Create request
            Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(jsonBody, JSON))
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + clientConfig.getApiKey())
                .build();
            
            // Execute request
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "No response body";
                    logger.errorf("XAI API request failed with code %d: %s", response.code(), errorBody);
                    return "Error: " + response.code() + " - " + errorBody;
                }
                
                // Parse response
                String responseBody = response.body().string();
                JsonNode jsonResponse = mapper.readTree(responseBody);
                
                // Extract generated text
                if (jsonResponse.has("choices") && jsonResponse.get("choices").isArray() && 
                    jsonResponse.get("choices").size() > 0 && 
                    jsonResponse.get("choices").get(0).has("message") &&
                    jsonResponse.get("choices").get(0).get("message").has("content")) {
                    return jsonResponse.get("choices").get(0).get("message").get("content").asText();
                } else {
                    logger.error("Unexpected response format from XAI API");
                    return "Error: Unexpected response format";
                }
            }
        } catch (Exception e) {
            logger.error("Error calling XAI API", e);
            return "Error: " + e.getMessage();
        }
    }
    
    /**
     * Generate embeddings for given text
     * 
     * @param text Text to generate embeddings for
     * @return List of floating point numbers representing the embedding
     */
    public List<Float> generateEmbedding(String text) {
        try {
            // Select client configuration
            ClientConfig clientConfig = selectClient();
            
            // Build URL for XAI API
            String url = String.format("%s/embeddings", 
                clientConfig.getEndpoint());
            
            // Create request body
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", clientConfig.getModelId());
            requestBody.put("input", text);
            
            // Convert request body to JSON
            String jsonBody = mapper.writeValueAsString(requestBody);
            
            // Create request
            Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(jsonBody, JSON))
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + clientConfig.getApiKey())
                .build();
            
            // Execute request
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "No response body";
                    logger.errorf("XAI API request failed with code %d: %s", response.code(), errorBody);
                    return new ArrayList<>();
                }
                
                // Parse response
                String responseBody = response.body().string();
                JsonNode jsonResponse = mapper.readTree(responseBody);
                
                // Extract embedding
                List<Float> embeddings = new ArrayList<>();
                if (jsonResponse.has("data") && jsonResponse.get("data").isArray() && 
                    jsonResponse.get("data").size() > 0 && 
                    jsonResponse.get("data").get(0).has("embedding") &&
                    jsonResponse.get("data").get(0).get("embedding").isArray()) {
                    
                    ArrayNode embeddingArray = (ArrayNode) jsonResponse.get("data").get(0).get("embedding");
                    for (JsonNode value : embeddingArray) {
                        embeddings.add(value.floatValue());
                    }
                    return embeddings;
                } else {
                    logger.error("Unexpected response format from XAI API");
                    return new ArrayList<>();
                }
            }
        } catch (Exception e) {
            logger.error("Error calling XAI API", e);
            return new ArrayList<>();
        }
    }

    /**
     * Select a client for the current request based on priority and rotation
     * @return Selected client configuration
     */
    private synchronized ClientConfig selectClient() {
        // Increment request counter for rotation
        int requestNum = requestCounter.incrementAndGet();

        // Get all available clients
        List<ClientConfig> clients = config.getAllClients();

        // If we only have one client, return it
        if (clients.size() == 1) {
            currentClient = clients.get(0);
            return currentClient;
        }

        // For now, use a simple round-robin selection among clients with the highest priority
        int index = (requestNum - 1) % clients.size();
        currentClient = clients.get(index);

        logger.infof("Selected client %s (group: %s, priority: %d) for request %d",
            currentClient.getId(), currentClient.getGroup(), currentClient.getPriority(), requestNum);

        return currentClient;
    }
}
