package org.acme.service;

import java.util.Date;
import java.util.List;

import org.acme.entity.Movie;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;

@ApplicationScoped // 或者其他合适的 scope
@Transactional
public class MovieService {

    public void saveOrUpdateMovies(List<Movie> movies) {
        for (Movie movie : movies) {
            if (movie.getOriginalId() != null) {
                Movie existingMovie = Movie.find("originalId = ?1", movie.getOriginalId()).firstResult();
                if (existingMovie != null) {
                    existingMovie.setCode(movie.getCode());
                    existingMovie.setCoverImageUrl(movie.getCoverImageUrl());
                    existingMovie.setDescription(movie.getDescription());
                    existingMovie.setDirector(movie.getDirector());
                    existingMovie.setDownloadUrlsInfo(movie.getDownloadUrlsInfo());
                    existingMovie.setDuration(movie.getDuration());
                    existingMovie.setLikes(movie.getLikes());
                    existingMovie.setLink(movie.getLink());
                    existingMovie.setMagnets(movie.getMagnets());
                    existingMovie.setMaker(movie.getMaker());
                    existingMovie.setOriginalId(movie.getOriginalId());
                    existingMovie.setPreviewVideoUrl(movie.getPreviewVideoUrl());
                    existingMovie.setReleaseDate(movie.getReleaseDate());
                    existingMovie.setSeries(movie.getSeries());
                    existingMovie.setStatus(movie.getStatus());
                    existingMovie.setThumbnail(movie.getThumbnail());
                    existingMovie.setTitle(movie.getTitle());
                    existingMovie.setUpdatedAt(new Date().toInstant());
                    existingMovie.setWatchUrlsInfo(movie.getWatchUrlsInfo());
                    existingMovie.persist();
                } else {
                    movie.persist();
                }
            }
        }
    }
}
