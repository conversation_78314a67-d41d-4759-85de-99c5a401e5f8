package org.acme.service.extractor;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.acme.config.CrawlerConfig;
import org.acme.enums.MissAvLanguageCode;
import org.acme.util.MovieInfoExtractorUtil;
import org.jboss.logging.Logger;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

/**
 * Movie Information Extractor
 * Extracts movie information from a website in multiple languages
 * Supports both regular HTTP requests and headless browser extraction
 */
@ApplicationScoped
public class MovieInfoExtractor {

    private static final Logger logger = Logger.getLogger(MovieInfoExtractor.class);

    @Inject
    CrawlerConfig crawlerConfig;

    @Inject
    MovieInfoExtractorUtil movieInfoExtractorUtil;

    @Inject
    HeadlessBrowserExtractor headlessBrowserExtractor;

    /**
     * Extracts movie information for a specific language.
     *
     * @param url The URL to extract information from
     * @return A map containing the extracted movie information
     * @throws IOException If an error occurs during extraction
     */
    public Map<String, Object> extractMissMovieInfoForLanguage(MissAvLanguageCode languageCode, String url) throws IOException {
        if (languageCode.equals(MissAvLanguageCode.HINDI)) {
            return null;
        }
        return extractMovieInfoForLanguage(url, null);
    }

    /**
     * Extracts movie information for a specific language with optional pre-fetched m3u8 info.
     *
     * @param url The URL to extract information from
     * @param existingM3u8Info Optional pre-fetched m3u8 info (can be null)
     * @return A map containing the extracted movie information
     * @throws IOException If an error occurs during extraction
     */
    public Map<String, Object> extractMovieInfoForLanguage(String url, List<String> existingM3u8Info) throws IOException {
        Boolean useHeadlessBrowser = crawlerConfig.useHeadlessBrowser();
        useHeadlessBrowser = false;
        // 根据配置决定使用无头浏览器还是HTTP请求
        if (useHeadlessBrowser) {
            logger.info("Using headless browser to extract movie info from: " + url);
            return extractWithHeadlessBrowser(url, existingM3u8Info);
        } else {
            logger.info("Using HTTP client to extract movie info from: " + url);
            return extractWithHttpClient(url, existingM3u8Info);
        }
    }

    /**
     * 使用无头浏览器提取电影信息
     *
     * @param url 要提取信息的URL
     * @param existingM3u8Info 可选的预先获取的m3u8信息
     * @return 包含电影信息的映射
     */
    private Map<String, Object> extractWithHeadlessBrowser(String url, List<String> existingM3u8Info) {
        // 增加重试次数，最多5次
        for (int i = 0; i < 3; i++) {
            try {
                Map<String, Object> result = headlessBrowserExtractor.extractMovieInfoForLanguage(url, existingM3u8Info);
                if (result != null) {
                    return result;
                }
                logger.infof("Headless browser extraction attempt %d failed for URL %s, retrying...", i + 1, url);
            } catch (Exception e) {
                logger.errorf("Error during headless browser extraction (attempt %d) for URL %s: %s", i + 1, url, e.getMessage());
            }
            // 重试前等待一下
            try {
                Thread.sleep(2000);
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
            }
        }

        // 如果无头浏览器全部失败，尝试退回到HTTP客户端
        logger.warn("All headless browser extraction attempts failed. Falling back to HTTP client for URL: " + url);
        return extractWithHttpClient(url, existingM3u8Info);
    }

    /**
     * 使用HTTP客户端提取电影信息
     *
     * @param url 要提取信息的URL
     * @param existingM3u8Info 可选的预先获取的m3u8信息
     * @return 包含电影信息的映射
     */
    private Map<String, Object> extractWithHttpClient(String url, List<String> existingM3u8Info) {
        // 增加重试次数，最多5次
        for (int i = 0; i < 5; i++) {
            try {
                // 使用代理池中的代理进行请求
                Map<String, Object> result = movieInfoExtractorUtil.extractMovieInfoFromUrl(url, null, existingM3u8Info, true);
                if (result != null) {
                    return result;
                }
                logger.infof("HTTP client extraction attempt %d failed for URL %s, retrying with different proxy...", i + 1, url);
            } catch (Exception e) {
                logger.errorf("Error during HTTP client extraction (attempt %d) for URL %s: %s", i + 1, url, e.getMessage());
            }
            // 重试前等待一下
            try {
                Thread.sleep(1000);
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
            }
        }

        logger.error("All extraction attempts failed for URL: " + url);
        return null;
    }

}
