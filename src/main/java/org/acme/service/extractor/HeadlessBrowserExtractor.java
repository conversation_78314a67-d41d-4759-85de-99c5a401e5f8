package org.acme.service.extractor;

import java.util.List;
import java.util.Map;

import org.acme.service.browser.PlaywrightBrowserService;
import org.acme.util.MovieInfoExtractorUtil;
import org.jboss.logging.Logger;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class HeadlessBrowserExtractor {
    private static final Logger logger = Logger.getLogger(HeadlessBrowserExtractor.class);

    @Inject
    PlaywrightBrowserService browserService;

    /**
     * 从URL抓取和解析HTML内容
     * 设计为将长时间运行的浏览器操作与数据库事务分离
     *
     * @param url 要抓取的URL
     * @param existingM3u8Info 可选的现有m3u8信息
     * @return 包含电影信息的映射
     */
    public Map<String, Object> extractMovieInfoForLanguage(String url, List<String> existingM3u8Info) {
        logger.infof("Extracting movie info with headless browser: %s", url);

        try {
            // 第一步：在事务之外获取HTML内容（这是最耗时的操作）
            String html = fetchHtmlOutsideTransaction(url);

            if (html == null || html.isEmpty()) {
                logger.errorf("Failed to get HTML content from URL: %s", url);
                return null;
            }

            // 第二步：解析HTML内容（可以在事务内完成，速度很快）
            return parseHtmlContent(html, url, existingM3u8Info);
        } catch (Exception e) {
            logger.errorf("Failed to extract movie info with headless browser: %s", e.getMessage());
            return null;
        }
    }

    /**
     * 使用无头浏览器获取HTML内容
     * 这是分离出来的可能长时间运行的操作
     *
     * @param url 要抓取的URL
     * @return 抓取的HTML内容
     */
    public String fetchHtmlOutsideTransaction(String url) {
        try {
            logger.info("Starting headless browser fetch outside transaction: " + url);
            String html = browserService.fetchHtml(url);
            logger.info("Completed headless browser fetch outside transaction");
            return html;
        } catch (Exception e) {
            logger.error("Error fetching HTML outside transaction: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 解析HTML内容并提取电影信息
     * 这是一个快速操作，适合在事务内执行
     *
     * @param html HTML内容
     * @param url 原始URL
     * @param existingM3u8Info 可选的现有m3u8信息
     * @return 包含电影信息的映射
     */
    private Map<String, Object> parseHtmlContent(String html, String url, List<String> existingM3u8Info) {
        try {
            // 使用JSoup解析HTML
            Document doc = Jsoup.parse(html, url);

            // 调用现有的解析逻辑，传入解析后的文档
            return MovieInfoExtractorUtil.extractInfoFromDocument(doc, url, null, existingM3u8Info);
        } catch (Exception e) {
            logger.error("Error parsing HTML content: " + e.getMessage(), e);
            return null;
        }
    }
}
