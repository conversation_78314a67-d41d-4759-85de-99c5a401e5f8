package org.acme.service;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.context.control.ActivateRequestContext;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import org.acme.entity.Movie;
import org.acme.entity.MovieInfo;
import org.acme.repository.MovieInfoRepository;
import org.acme.util.ThreadPoolUtil;
import org.acme.util.MovieInfoMetrics;
import org.jboss.logging.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 电影多语言信息服务
 * 处理电影多语言信息的创建、更新和检索
 */
@ApplicationScoped
public class MovieInfoService {
    
    private static final Logger logger = Logger.getLogger(MovieInfoService.class);
    private static final String[] SUPPORTED_LANGUAGES = {"zh", "en", "ja"}; // 支持的语言列表

    @Inject
    MovieParser movieParser;

    @Inject
    MovieInfoExtractionService movieInfoExtractionService;

    @Inject
    MovieInfoService self; // 用于事务处理

    @Inject
    MovieInfoMetrics metrics; // 性能监控

    /**
     * 根据电影UUID和语言获取电影信息
     * 
     * @param movieUuid 电影UUID
     * @param language 语言代码
     * @return 指定语言的电影信息
     */
    public MovieInfo getMovieInfoByLanguage(UUID movieUuid, String language) {
        return MovieInfo.find("movieUuid = ?1 AND language = ?2", movieUuid, language).firstResult();
    }

    /**
     * 获取电影的所有语言版本信息
     * 
     * @param movieUuid 电影UUID
     * @return 所有语言版本的电影信息列表
     */
    public List<MovieInfo> getAllLanguageVersions(UUID movieUuid) {
        return MovieInfo.list("movieUuid", movieUuid);
    }

    /**
     * 保存电影的多语言信息
     * 
     * @param movie 电影对象
     * @param language 语言代码
     * @param info 电影多语言信息
     * @return 保存后的MovieInfo对象
     */
    @Transactional
    public MovieInfo saveMovieInfo(Movie movie, String language, MovieInfo info) {
        if (info == null) {
            info = MovieInfo.fromMovie(movie, language);
        } else {
            info.movieUuid = movie.movieUuid;
            info.language = language;
        }
        
        // 查找已存在的信息
        MovieInfo existingInfo = getMovieInfoByLanguage(movie.movieUuid, language);
        
        if (existingInfo != null) {
            // 更新已存在的记录
            existingInfo.title = info.title;
            existingInfo.description = info.description;
            existingInfo.tags = info.tags;
            existingInfo.genres = info.genres;
            existingInfo.director = info.director;
            existingInfo.maker = info.maker;
            existingInfo.actresses = info.actresses;
            existingInfo.series = info.series;
            existingInfo.persist();
            return existingInfo;
        } else {
            // 创建新记录
            info.persist();
            return info;
        }
    }

    /**
     * 从原始电影对象创建所有支持语言的电影信息
     * 现在只是复制相同信息，实际应用中应通过翻译API或其他方式获取多语言内容
     * 
     * @param movie 电影对象
     * @return 创建的电影信息列表
     */
    @Transactional
    public List<MovieInfo> createAllLanguageVersions(Movie movie) {
        List<MovieInfo> results = new ArrayList<>();
        
        for (String language : SUPPORTED_LANGUAGES) {
            // 使用不同语言抓取电影信息
            try {
                // 在实际应用中，这里可以根据language来修改请求路径
                // 现在先简单地从原Movie复制数据
                MovieInfo info = MovieInfo.fromMovie(movie, language);
                results.add(saveMovieInfo(movie, language, info));
                logger.infof("Created %s language version for movie %s", language, movie.code);
            } catch (Exception e) {
                logger.errorf("Error creating %s language version for movie %s: %s", 
                        language, movie.code, e.getMessage());
            }
        }
        
        return results;
    }

    /**
     * 更新电影在爬取过程中使用的默认语言
     * 用于修改现有爬虫接口以支持多语言
     * 
     * @param movieUrl 电影URL
     * @param language 目标语言代码
     * @return 修改后的URL
     */
    public String updateUrlWithLanguage(String movieUrl, String language) {
        if (movieUrl == null || movieUrl.isEmpty()) {
            return movieUrl;
        }
        
        // 假设URL格式为: https://example.com/zh/movie/123456
        // 我们需要替换语言部分 "/zh/" 为 "/{language}/"
        
        // 查找当前语言代码位置
        for (String lang : SUPPORTED_LANGUAGES) {
            String langPattern = "/" + lang + "/";
            if (movieUrl.contains(langPattern)) {
                // 替换为新的语言代码
                return movieUrl.replace(langPattern, "/" + language + "/");
            }
        }
        
        // 如果没有找到语言代码，尝试在域名后添加语言
        // 例如：https://example.com/movie/123456 → https://example.com/zh/movie/123456
        if (movieUrl.matches("https?://[^/]+/.*")) {
            int index = movieUrl.indexOf("/", 8); // 跳过"https://"或"http://"
            if (index > 0) {
                return movieUrl.substring(0, index) + "/" + language + movieUrl.substring(index);
            }
        }
        
        // 如果无法修改URL，返回原始URL
        return movieUrl;
    }


    @Inject
    MovieInfoRepository movieInfoRepository; // 访问movie_info表

    /**
     * 处理缺失的电影信息 - 多线程优化版本
     * 避免长事务，每个电影信息处理完立即保存
     *
     * @param limit 处理的电影数量限制
     */
    public void handleMissingMovieInfo(int limit) {
        long startTime = System.currentTimeMillis();
        logger.infof("开始处理缺失的电影信息，限制数量: %d", limit);

        // 1. 从movie_info获取缺失的code
        List<String> missingCodes = movieInfoRepository.findMissingMovieCodes(limit);

        if (missingCodes.isEmpty()) {
            logger.info("没有找到缺失的电影代码");
            return;
        }

        logger.infof("找到 %d 个缺失的电影代码，开始多线程处理", missingCodes.size());

        // 2. 创建线程池进行并发处理
        int threadCount = Math.min(4, missingCodes.size()); // 最多4个线程
        ThreadPoolExecutor movieInfoPool = ThreadPoolUtil.createThreadPool(
            "movie-info-processor",
            threadCount,
            threadCount * 2,
            100
        );

        try {
            // 3. 提交所有任务到线程池
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (String code : missingCodes) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    processSingleMovieCode(code);
                }, movieInfoPool);

                futures.add(future);
            }

            // 4. 等待所有任务完成
            CompletableFuture<Void> allTasks = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
            );

            try {
                // 等待所有任务完成，最多等待30分钟
                allTasks.get(30, TimeUnit.MINUTES);
                logger.infof("所有电影信息处理任务完成");
            } catch (Exception e) {
                logger.errorf("等待任务完成时出错: %s", e.getMessage());
            }

        } finally {
            // 5. 关闭线程池
            ThreadPoolUtil.shutdownThreadPool(movieInfoPool, 60);
        }

        long endTime = System.currentTimeMillis();
        logger.infof("电影信息处理完成，总耗时: %d ms", endTime - startTime);

        // 打印性能监控报告
        metrics.printReport();
    }

    /**
     * 处理单个电影代码 - 短事务处理
     *
     * @param code 电影代码
     */
    private void processSingleMovieCode(String code) {
        try {
            logger.debugf("开始处理电影代码: %s", code);

            // 查找电影对象
            Movie currentMovie = Movie.find("code", code).firstResult();
            if (currentMovie == null) {
                logger.warnf("未找到电影代码对应的电影: %s", code);
                return;
            }

            // 使用短事务处理单个电影信息
            self.extractAndSaveSingleMovieInfo(currentMovie);

        } catch (Exception e) {
            logger.errorf("处理电影代码 %s 时出错: %s", code, e.getMessage(), e);
        }
    }

    /**
     * 提取并保存单个电影信息 - 短事务方法
     *
     * @param movie 电影对象
     */
    @ActivateRequestContext
    @Transactional
    public void extractAndSaveSingleMovieInfo(Movie movie) {
        try {
            logger.debugf("开始提取电影信息: %s", movie.getCode());

            // 调用提取服务
            movieInfoExtractionService.extractJAMovieInfoAndSave(movie);

            logger.infof("成功处理电影信息: %s", movie.getCode());

        } catch (Exception e) {
            logger.errorf("提取电影信息失败 %s: %s", movie.getCode(), e.getMessage(), e);
            throw e; // 重新抛出异常以便上层处理
        }
    }
}
