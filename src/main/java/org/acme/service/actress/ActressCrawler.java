package org.acme.service.actress;

import java.io.IOException;

import org.acme.service.extractor.HeadlessBrowserExtractor;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class ActressCrawler {

    @Inject
    HeadlessBrowserExtractor headlessBrowserExtractor;

    private static final String BASE_URL = "https://missav.ai/cn/actresses?page=";
    private static int MAX_PAGES = 1372; // 根据实际页面获取

    public void main(String[] args) {
        for (int page = 1; page <= MAX_PAGES; page++) {
            try {
                String html = fetchPage(page);
                parseHtml(html);
                Thread.sleep(1500); // 降低请求频率
            } catch (IOException | InterruptedException e) {
                System.err.println("页码 "+page+" 抓取失败: " + e.getMessage());
            }
        }
    }

    private String fetchPage(int page) throws IOException {
        return headlessBrowserExtractor.fetchHtmlOutsideTransaction(BASE_URL + page);
    }

    private static void parseHtml(String html) {
        Document doc = Jsoup.parse(html);
        Elements items = doc.select("div.space-y-4"); // 演员条目选择器

        for (Element item : items) {
            String name = item.select("h4.text-nord13").text();
            String works = item.select("p.text-nord10:contains(条影片)").text().replaceAll("\\D", "");
            String debut = item.select("p.text-nord10:contains(出道)").text().replaceAll("\\D", "");
            String avatar = item.select("img").attr("src");

            System.out.printf("姓名: %-20s 作品数: %-4s 出道年份: %-4s 头像: %s%n",
                    name, works, debut, avatar);
        }
    }
}
