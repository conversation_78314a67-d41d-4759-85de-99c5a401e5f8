package org.acme.service.favourite;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import io.quarkus.arc.Arc;
import io.quarkus.arc.ArcContainer;
import io.quarkus.arc.ManagedContext;

import org.acme.config.RefreshResult;

import org.acme.entity.Movie;
import org.acme.entity.WatchUrl;
import org.acme.enums.CrawlerStatus;
import org.acme.enums.MovieStatus;
import org.acme.model.FavouriteResponse;
import org.acme.config.AuthCookieConfig;
import org.acme.util.FileUtils;
import org.acme.util.HttpClientUtils;
import org.acme.util.ThreadPoolUtil;
import org.jboss.logging.Logger;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import dev.langchain4j.internal.Json;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

import jakarta.transaction.Transactional;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * Service for processing user collections
 * This service handles:
 * 1. Adding movies to favourites using the favourite API
 * 2. Fetching collection pages to extract movie information
 * 3. Saving movie information to the database
 * 4. Removing movies from favourites
 *
 * The service supports both multi-threaded and single-threaded processing approaches
 */
@ApplicationScoped
public class CollectionProcessService {

    private static final Logger logger = Logger.getLogger(CollectionProcessService.class);
    private static final String COLLECTION_BASE_URL = "https://123av.com/ja/user/collection?sort=recent_update";
    private static final int MAX_RETRIES = 3;
    private static final int COLLECTION_THREAD_COUNT = 3; // Number of threads for processing collection pages
    private final Set<String> processedPageKeys = Collections.synchronizedSet(new HashSet<>()); // Track processed pages to prevent loops

    private final OkHttpClient httpClient;
    
    // 跟踪所有创建的线程池，以便在应用关闭时正确关闭它们
    private final Set<ThreadPoolExecutor> activeThreadPools = Collections.synchronizedSet(new HashSet<>());

    @Inject
    FavouriteService favouriteService;
    
    @Inject
    AuthCookieConfig authCookieConfig;


    public CollectionProcessService() {
        this.httpClient = HttpClientUtils.createHttpClient();
    }
    
    /**
     * Handle 401 Unauthorized error by refreshing authentication cookie
     * Uses the new RefreshResult enum to only retry immediately if an actual refresh occurred
     * 
     * @return true if the request should be retried immediately, false otherwise
     */
    protected boolean handle401Error() {
        logger.info("Received 401 Unauthorized error, refreshing authentication cookie...");
        RefreshResult refreshResult = authCookieConfig.refreshCookieWithResult();
        
        // Only retry immediately if a real refresh was performed (not on cooldown/in-progress)
        if (refreshResult == RefreshResult.REFRESHED) {
            logger.info("Authentication cookie actually refreshed with new value, retrying request immediately");
            return true; // Retry immediately with new cookie
        } else if (refreshResult == RefreshResult.ON_COOLDOWN || refreshResult == RefreshResult.IN_PROGRESS) {
            logger.info("Authentication cookie refresh was skipped due to cooldown or in-progress status: " + refreshResult);
            return false; // Continue with normal retry loop with backoff
        } else {
            logger.warn("Failed to refresh authentication cookie: " + refreshResult);
            return false;
        }
    }
    
    /**
     * 创建线程池并跟踪它，以便在应用关闭时正确关闭
     * @param poolName 线程池名称
     * @param nThreads 线程数量
     * @return 创建的线程池
     */
    private ThreadPoolExecutor createTrackedThreadPool(String poolName, int nThreads) {
        // 使用ThreadPoolUtil创建线程池，核心线程数和最大线程数设为相同值，队列容量设为1000
        ThreadPoolExecutor threadPool = ThreadPoolUtil.createThreadPool(poolName, nThreads, nThreads, 1000);
        activeThreadPools.add(threadPool);
        return threadPool;
    }
    
    /**
     * 安全地关闭线程池并从跟踪列表中移除
     * @param threadPool 要关闭的线程池
     */
    private void shutdownTrackedThreadPool(ThreadPoolExecutor threadPool) {
        if (threadPool != null) {
            try {
                ThreadPoolUtil.shutdownThreadPool(threadPool, 30); // 使用30秒超时
            } finally {
                activeThreadPools.remove(threadPool);
            }
        }
    }
    
    /**
     * 在应用关闭时优雅地关闭所有活动的线程池
     * 由Quarkus在应用关闭时自动调用
     */
    @jakarta.annotation.PreDestroy
    public void cleanup() {
        logger.info("应用关闭中，关闭所有活动线程池: " + activeThreadPools.size() + " 个");
        
        // 创建线程池的副本，避免并发修改异常
        Set<ThreadPoolExecutor> poolsToShutdown = new HashSet<>(activeThreadPools);
        
        // 关闭所有活动线程池
        for (ThreadPoolExecutor pool : poolsToShutdown) {
            shutdownTrackedThreadPool(pool);
        }
        
        logger.info("所有线程池已关闭");
    }

    /**
     * Handle existing favorites - separate transaction
     */
    public void handleExistingFavorites(Integer pages) {
        try {
            // Get current movie IDs from favorites API
            int totalCollectionPages = getTotalCollectionPages();
            Integer totalPages = pages == null ? totalCollectionPages : Math.min(pages, totalCollectionPages);

            if (totalPages <= 0) {
                logger.infof("No favorites found in collection - skipping initial clear");
                return;
            }

            // There are items in the favorites list, get their IDs
            logger.infof("Found %d pages in favorites list", totalPages);

            // Process pages to get movie IDs - limit to first 10 pages for efficiency
            List<Movie> favoritedMovies = getFavoritedMovies(totalPages);
            Set<Integer> favoritedIds = favoritedMovies.stream().map(Movie::getOriginalId).collect(Collectors.toSet());

            if (favoritedIds.isEmpty()) {
                logger.infof("No movie IDs found in favorites list");
                return;
            }

            saveMoviesInSingleTransaction(favoritedMovies);
            removeExistingMoviesFromFavorites(favoritedIds);
        } catch (Exception e) {
            logger.errorf("Error handling existing favorites: %s", e.getMessage());
            throw e; // Let the transaction system handle the rollback
        }
    }

    /**
     * Get favorited movie IDs from collection pages using multiple threads
     *
     * @param pagesToProcess Number of pages to process
     * @return List of movies extracted from the pages
     */
    protected List<Movie> getFavoritedMovies(int pagesToProcess) {
        List<Movie> favoritedMovieList = new ArrayList<>();

        try {
            // Determine the actual number of threads to use based on pages to process
            int threadsToUse = Math.min(COLLECTION_THREAD_COUNT, pagesToProcess);
            logger.infof("Processing %d collection pages using %d threads", pagesToProcess, threadsToUse);
            
            // 创建一个被跟踪的线程池，确保在应用关闭时能正确清理
            ThreadPoolExecutor threadPool = createTrackedThreadPool("collection-processor", threadsToUse);
            
            try {
                // Create completable futures for all tasks
                List<CompletableFuture<List<Movie>>> futures = new ArrayList<>();
                
                // Ensure even distribution of pages with no overlaps
                // Each page needs to be processed exactly once
                if (pagesToProcess <= threadsToUse) {
                    // If we have fewer or equal pages than threads, assign one page per thread
                    for (int page = 1; page <= pagesToProcess; page++) {
                        final int pageToProcess = page;
                        CompletableFuture<List<Movie>> future = CompletableFuture.supplyAsync(() -> {
                            logger.infof("Thread processing single page: %d", pageToProcess);
                            return processPageRange(pageToProcess, pageToProcess);
                        }, threadPool);
                        futures.add(future);
                    }
                } else {
                    // For larger number of pages, distribute them evenly
                    int pagesPerThread = pagesToProcess / threadsToUse;
                    int remainingPages = pagesToProcess % threadsToUse;
                    
                    int currentPage = 1;
                    for (int i = 0; i < threadsToUse; i++) {
                        // Distribute remaining pages one per thread until exhausted
                        int threadPageCount = pagesPerThread + (i < remainingPages ? 1 : 0);
                        
                        if (threadPageCount > 0) {
                            int startPage = currentPage;
                            int endPage = currentPage + threadPageCount - 1;
                            
                            logger.infof("Assigning thread %d: pages %d to %d", i+1, startPage, endPage);
                            
                            CompletableFuture<List<Movie>> future = CompletableFuture.supplyAsync(() -> {
                                return processPageRange(startPage, endPage);
                            }, threadPool);
                            
                            futures.add(future);
                            currentPage = endPage + 1;
                        }
                    }
                }
                
                // Wait for all futures to complete and collect results
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
                
                // Wait for all tasks to complete
                allFutures.join();
                
                // Collect results from all futures
                for (CompletableFuture<List<Movie>> future : futures) {
                    List<Movie> threadResult = future.get(); // Safe to call get() after join()
                    favoritedMovieList.addAll(threadResult);
                }

            } finally {
                // 使用跟踪方法安全关闭线程池
                shutdownTrackedThreadPool(threadPool);
            }

            logger.infof("Found %d movie IDs in favorites list", favoritedMovieList.size());
            if (!favoritedMovieList.isEmpty()) {
                logger.debug("Movie titles: " + favoritedMovieList.stream().map(Movie::getTitle).collect(Collectors.joining(", ")));
            }
        } catch (Exception e) {
            logger.errorf("Error getting favorited movie IDs: %s", e.getMessage());
        }

        return favoritedMovieList;
    }

    /**
     * Process a range of collection pages
     * This method must not be private to ensure CDI interceptors work properly if @Transactional is used
     *
     * @param startPage The first page to process
     * @param endPage The last page to process (inclusive)
     * @return List of movies extracted from the pages
     */
    protected List<Movie> processPageRange(int startPage, int endPage) {
        List<Movie> pageMovies = new ArrayList<>();
        
        try {
            logger.infof("Thread processing page range %d to %d", startPage, endPage);
            
            for (int page = startPage; page <= endPage; page++) {
                List<Movie> movies = processCollectionPage(page);
                pageMovies.addAll(movies);
                
                // Add a small delay between pages to avoid rate limiting
                if (page < endPage) {
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
            
            logger.infof("Thread completed processing pages %d to %d, found %d movies", 
                    startPage, endPage, pageMovies.size());
        } catch (Exception e) {
            logger.errorf("Error processing page range %d to %d: %s", 
                    startPage, endPage, e.getMessage());
        }
        
        return pageMovies;
    }

    /**
     * Categorize movie IDs into existing and new
     */
    @Transactional
    protected Map<String, Set<Integer>> categorizeMovieIds(Set<Integer> movieIds) {
        Set<Integer> existingIds = new HashSet<>();
        Set<Integer> newIds = new HashSet<>();
        int nullOrInvalidIds = 0;
        int validIds = 0;
        
        logger.infof("Categorizing %d movie IDs", movieIds.size());
        
        // Log the first few IDs to debug
        int debugCount = 0;
        for (Integer id : movieIds) {
            if (debugCount < 20) { // Log first 20 IDs
                logger.debugf("Movie ID to categorize: %s", id);
                debugCount++;
            }
            
            if (id == null || id <= 0) {
                nullOrInvalidIds++;
                continue; // Skip null or invalid IDs
            }
            
            validIds++;
            
            // Check if movie already exists in database
            long count = Movie.find("originalId = ?1", id).count();
            if (count > 0) {
                existingIds.add(id);
                if (debugCount <= 20) {
                    logger.debugf("ID %d exists in database", id);
                }
            } else {
                newIds.add(id);
                if (debugCount <= 20) {
                    logger.debugf("ID %d is new", id);
                }
            }
        }

        logger.infof("Categorization results: Total=%d, Valid=%d, Null/Invalid=%d, Existing=%d, New=%d", 
                movieIds.size(), validIds, nullOrInvalidIds, existingIds.size(), newIds.size());
        
        Map<String, Set<Integer>> result = new HashMap<>();
        result.put("existing", existingIds);
        result.put("new", newIds);
        return result;
    }

    /**
     * Remove existing movies from favorites - separate transaction
     */
    protected void removeExistingMoviesFromFavorites(Set<Integer> existingIds) {
        try {
            logger.infof("Removing %d existing movies from favorites", existingIds.size());
            removeIdsFromFavourites(existingIds);

            logger.infof("Removed %d existing movies from favorites", existingIds.size());
        } catch (Exception e) {
            logger.errorf("Error removing existing movies from favorites: %s", e.getMessage());
            throw e; // Let the transaction system handle the rollback
        }
    }

    /**
     * Find movies that need processing
     */
    
    @Transactional
    protected Set<Integer> findMoviesForProcessing(int batchSize) {
        List<WatchUrl> watchUrls = WatchUrl.find("status = ?1 and likeStatus is null order by id desc",
                                                MovieStatus.ONLINE.getValue())
                .page(0, batchSize)
                .list();
        return watchUrls.stream()
                .map(WatchUrl::getMovieId)
                .collect(Collectors.toSet());
    }


   

    public void addIdsToFavourites(Set<Integer> ids) {
        logger.info("Step 1: Adding movies to favourites");
        favouriteService.processFavouritesForList(ids, "add");
    }

    /**
     * Add a range of movie IDs to favourites
     *
     * @param startId The starting movie ID
     * @param endId The ending movie ID
     * @return Map of movie IDs to favourite response objects
     */
    public Map<Long, FavouriteResponse> addToFavourites(Long startId, Long endId) {
        logger.info("Step 1: Adding movies to favourites");
        Map<Long, FavouriteResponse> addResults = favouriteService.processFavouritesForRange(startId, endId, "add");
        int successCount = (int) addResults.values().stream().filter(FavouriteResponse::isSuccess).count();
        logger.infof("Added %d movies to favourites", successCount);
        return addResults;
    }

    
    /**
     * Save all fetched movies to the database in multiple smaller transactions
     * This prevents transaction timeouts when dealing with large numbers of movies
     *
     * @param movies List of movies to save
     */
    protected void saveMoviesInSingleTransaction(List<Movie> movies) {
        saveMovieBatch(movies);
    }

    /**
     * Save a batch of movies in a single transaction
     * Method must be protected or public (not private) for CDI interceptors to work with @Transactional
     *
     * @param movies List of movies in the current batch to save
     */
    @Transactional
    protected void saveMovieBatch(List<Movie> movies) {
        for (Movie movie : movies) {
            try {
                // Check if movie already exists by code before saving
                Movie existingMovie = Movie.find("code", movie.getCode()).firstResult();

                if (existingMovie != null) {
                    logger.debugf("Found existing movie with code %s, updating instead of creating new", movie.getCode());
                    // Update fields on existing movie instead of creating new
                    updateExistingMovie(existingMovie, movie);
                    existingMovie.persist();
                } else {
                    // This is a new movie, save it
                    movie.persist();
                }
            } catch (Exception e) {
                logger.errorf("Error saving movie %s: %s", movie.getCode(), e.getMessage());
            }
        }
    }
    

    /**
     * Update existing movie with new data
     */
    private void updateExistingMovie(Movie existingMovie, Movie newMovie) {
        // Only update fields that are not null or empty in the new movie
        if (newMovie.getTitle() != null && !newMovie.getTitle().isEmpty()) {
            existingMovie.setTitle(newMovie.getTitle());
        }

        if (newMovie.getThumbnail() != null && !newMovie.getThumbnail().isEmpty()) {
            existingMovie.setThumbnail(newMovie.getThumbnail());
        }

        if (newMovie.getLink() != null && !newMovie.getLink().isEmpty()) {
            existingMovie.setLink(newMovie.getLink());
        }

        if (newMovie.getDuration() != null && !newMovie.getDuration().isEmpty()) {
            existingMovie.setDuration(newMovie.getDuration());
        }

        if (newMovie.getOriginalId() != null) {
            existingMovie.setOriginalId(newMovie.getOriginalId());
        }

        // Update status if needed
        if (newMovie.getStatus() != null) {
            existingMovie.setStatus(newMovie.getStatus());
        }
    }

    /**
     * Get the total number of pages in the collection
     *
     * @return The total number of pages, or 0 if unable to determine
     */
    private int getTotalCollectionPages() {
        final int MAX_RETRIES = 3;
        final long RETRY_DELAY_MS = 2000; // 2 seconds between retries
        int retryCount = 0;

        while (retryCount < MAX_RETRIES) {
            try {
                String url = COLLECTION_BASE_URL;
                logger.infof("Fetching collection page to determine total pages (attempt %d/%d): %s", 
                        retryCount + 1, MAX_RETRIES, url);

                Request request = HttpClientUtils.createRequestBuilderWithReferer(url, authCookieConfig.getAuthCookie(), 
                        "https://123av.com/ja/user/feed?sort=recent_update", false)
                        .build();

                try (Response response = httpClient.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        int statusCode = response.code();
                        logger.warnf("Failed to fetch collection page: HTTP %d (attempt %d/%d)", 
                                statusCode, retryCount + 1, MAX_RETRIES);
                        
                        // Check if we got a 401 unauthorized error and try to refresh the cookie
                        if (statusCode == 401) {
                            logger.info("Received 401 Unauthorized error, refreshing authentication cookie...");
                            RefreshResult refreshResult = authCookieConfig.refreshCookieWithResult();
                            if (refreshResult == RefreshResult.REFRESHED) {
                                logger.info("Authentication cookie refreshed successfully, retrying request immediately");
                                continue; // Retry immediately with new cookie without counting as a retry
                            } else {
                                logger.warn("Failed to refresh authentication cookie");
                            }
                        }
                        
                        retryCount++;
                        if (retryCount < MAX_RETRIES) {
                            logger.infof("Retrying in %d ms...", RETRY_DELAY_MS);
                            Thread.sleep(RETRY_DELAY_MS);
                            continue;
                        } else {
                            logger.errorf("Failed to fetch collection page after %d attempts", MAX_RETRIES);
                            return 0;
                        }
                    }

                    String htmlContent = response.body().string();

                    if (htmlContent.contains("0 動画")) {
                        logger.info("No movies found for processing");
                        return 0;
                    }

                    // Parse the HTML to extract the total pages
                    Document doc = Jsoup.parse(htmlContent);

                    // Look for the navigation element with the lastPage attribute
                    // <div v-scope="Navigation({ lastPage: 465 })" @vue:mounted="init($el)" class="mt-5">
                    Element navigationElement = doc.selectFirst("div[v-scope~=Navigation]");

                    if (navigationElement != null) {
                        String vScope = navigationElement.attr("v-scope");
                        Pattern pattern = Pattern.compile("Navigation\\(\\{\\s*lastPage:\\s*(\\d+)\\s*\\}\\)");
                        Matcher matcher = pattern.matcher(vScope);

                        if (matcher.find()) {
                            String lastPageStr = matcher.group(1);
                            try {
                                int lastPage = Integer.parseInt(lastPageStr);
                                logger.infof("Found total pages: %d", lastPage);
                                return lastPage;
                            } catch (NumberFormatException e) {
                                logger.warnf("Could not parse lastPage value: %s", lastPageStr);
                            }
                        } else {
                            logger.warn("Could not extract lastPage value from navigation element");
                        }
                    } else {
                        logger.warn("Could not find navigation element with lastPage attribute");
                    }
                    
                    // If we got here but couldn't parse the pages, retry
                    retryCount++;
                    if (retryCount < MAX_RETRIES) {
                        logger.infof("Failed to extract page count, retrying in %d ms... (attempt %d/%d)", 
                                RETRY_DELAY_MS, retryCount + 1, MAX_RETRIES);
                           Thread.sleep(RETRY_DELAY_MS);
                    } else {
                        logger.errorf("Failed to extract page count after %d attempts", MAX_RETRIES);
                        return 1;
                    }
                }
            } catch (Exception e) {
                logger.errorf("Error determining total collection pages (attempt %d/%d): %s", 
                        retryCount + 1, MAX_RETRIES, e.getMessage());
                retryCount++;
                
                if (retryCount < MAX_RETRIES) {
                    try {
                        logger.infof("Retrying in %d ms...", RETRY_DELAY_MS);
                        Thread.sleep(RETRY_DELAY_MS);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        logger.error("Retry sleep interrupted", ie);
                        return 1;
                    }
                } else {
                    logger.errorf("Failed after %d attempts, giving up", MAX_RETRIES);
                    return 1;
                }
            }
        }
        
        return 0;
    }

    public void removeIdsFromFavourites(Set<Integer> ids) {
        logger.info("Step 4: Removing movies from favourites");
        favouriteService.processFavouritesForList(ids, "remove");
    }

    /**
     * Remove a range of movie IDs from favourites
     *
     * @param startId The starting movie ID
     * @param endId The ending movie ID
     */
    public void removeFromFavourites(Long startId, Long endId) {
        logger.info("Step 4: Removing movies from favourites");
        favouriteService.processFavouritesForRange(startId, endId, "remove");
    }


    /**
     * Save or update a movie in the database
     *
     * @param movie The movie to save
     * @return The saved movie
     */
    
    protected Movie saveMovie(Movie movie) {
        if (movie == null || movie.getCode() == null) {
            throw new IllegalArgumentException("Movie or movie code cannot be null");
        }

        try {
            // 使用事务内查询确保最新状态
            Movie existingMovie = Movie.find("code", movie.getCode()).firstResult();

            if (existingMovie != null) {
                logger.infof("Found existing movie with code %s, updating instead of creating new", movie.getCode());
                // 更新现有电影的字段，只更新非空值
                if (movie.getTitle() != null) {
                    existingMovie.setTitle(movie.getTitle());
                }
                if (movie.getThumbnail() != null) {
                    existingMovie.setThumbnail(movie.getThumbnail());
                }
                if (movie.getLink() != null) {
                    existingMovie.setLink(movie.getLink());
                }
                if (movie.getOriginalId() != null) {
                    existingMovie.setOriginalId(movie.getOriginalId());
                }
                if (movie.getCoverImageUrl() != null) {
                    existingMovie.setCoverImageUrl(movie.getCoverImageUrl());
                }
                if (movie.getDescription() != null) {
                    existingMovie.setDescription(movie.getDescription());
                }
                if (movie.getDuration() != null) {
                    existingMovie.setDuration(movie.getDuration());
                }
                if (movie.getDirector() != null) {
                    existingMovie.setDirector(movie.getDirector());
                }
                if (movie.getMaker() != null) {
                    existingMovie.setMaker(movie.getMaker());
                }
                if (movie.getSeries() != null) {
                    existingMovie.setSeries(movie.getSeries());
                }
                if (movie.getReleaseDate() != null) {
                    existingMovie.setReleaseDate(movie.getReleaseDate());
                }
                if (movie.getPreviewVideoUrl() != null) {
                    existingMovie.setPreviewVideoUrl(movie.getPreviewVideoUrl());
                }
                if (movie.getActresses() != null && !movie.getActresses().isEmpty()) {
                    existingMovie.setActresses(movie.getActresses());
                }
                if (movie.getGenres() != null && !movie.getGenres().isEmpty()) {
                    existingMovie.setGenres(movie.getGenres());
                }
                if (movie.getTags() != null && !movie.getTags().isEmpty()) {
                    existingMovie.setTags(movie.getTags());
                }

                // 更新状态相关字段
                if (movie.getStatus() != null) {
                    existingMovie.setStatus(movie.getStatus());
                }

                // 确保更新时间是最新的
                existingMovie.setUpdatedAt(java.time.Instant.now());

                // 使用merge而不是persist来确保更新
                existingMovie.persist();

                // 更新相关联的 WatchUrl 记录的 like_status 字段为 succeed
                updateWatchUrlLikeStatus(existingMovie);

                return existingMovie;
            } else {
                // 创建新电影记录
                logger.infof("Creating new movie with code %s", movie.getCode());

                // 设置必填字段的默认值
                if (movie.getDuration() == null) {
                    movie.setDuration("未知");
                }

                // 设置创建和更新时间
                java.time.Instant now = java.time.Instant.now();
                movie.setCreatedAt(now);
                movie.setUpdatedAt(now);

                // 使用persist保存新记录
                movie.persist();

                // 对于新创建的电影，也更新相关联的 WatchUrl 记录
                updateWatchUrlLikeStatus(movie);

                return movie;
            }
        } catch (Exception e) {
            logger.errorf("Error saving movie %s: %s", movie.getCode(), e.getMessage());

            // 尝试再次查询以确认是否已存在记录
            if (e.getMessage() != null && e.getMessage().contains("duplicate key value violates unique constraint")) {
                logger.infof("Duplicate key detected for movie %s, attempting to update existing record", movie.getCode());

                try {
                    // 如果是重复键错误，尝试再次查询并更新
                    Movie existingMovie = Movie.find("code", movie.getCode()).firstResult();
                    if (existingMovie != null) {
                        // 更新现有记录
                        logger.infof("Found existing movie with code %s after duplicate key error, updating", movie.getCode());
                        return saveMovie(movie); // 递归调用，但这次会走更新路径
                    }
                } catch (Exception retryEx) {
                    logger.errorf("Error during retry for movie %s: %s", movie.getCode(), retryEx.getMessage());
                }
            }

            throw e; // 如果无法处理，重新抛出异常
        }
    }

    /**
     * 更新电影关联的所有 WatchUrl 记录的 like_status 字段为 succeed
     *
     * @param movie 要处理的电影对象
     */
    private void updateWatchUrlLikeStatus(Movie movie) {
        if (movie == null || movie.originalId == null) {
            return;
        }

        try {
            // 查找与当前电影关联的所有 WatchUrl 记录
            List<WatchUrl> watchUrls = WatchUrl.list("movieId", movie.originalId);

            if (watchUrls == null || watchUrls.isEmpty()) {
                // 没有关联的 WatchUrl 记录，无需处理
                return;
            }

            // 批量更新所有关联的 WatchUrl 记录
            for (WatchUrl watchUrl : watchUrls) {
                // 添加 like_status 字段并设置为 succeed
                // 如果该字段已存在则会更新，否则会自动在数据库中添加该字段
                watchUrl.likeStatus = "succeed";
                watchUrl.code = movie.code;
                watchUrl.persist();
            }

            logger.infof("Updated like_status to 'succeed' for %d WatchUrl records of movie %s",
                    watchUrls.size(), movie.getCode());

        } catch (Exception e) {
            logger.errorf("Error updating WatchUrl like_status for movie %s: %s",
                    movie.getCode(), e.getMessage());
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * Process a collection page and extract movie information
     *
     * @param page The page number to process
     * @return List of movies extracted from the page
     */
    protected List<Movie> processCollectionPage(int page) {
        List<Movie> movies = new ArrayList<>();
        
        int retryCount = 0;

        while (retryCount < MAX_RETRIES) {
            try {
                String url = String.format("%s&page=%d", COLLECTION_BASE_URL, page);
                logger.infof("Fetching collection page: %s", url);

                Request request = HttpClientUtils.createRequestBuilderWithReferer(url, authCookieConfig.getAuthCookie(), "https://123av.com/ja/user/feed?sort=recent_update", false)
                        .build();

                try (Response response = httpClient.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        int statusCode = response.code();
                        logger.errorf("Failed to fetch collection page %d: HTTP %d", page, statusCode);
                        
                        // Check if we got a 401 unauthorized error and try to refresh the cookie
                        if (statusCode == 401) {
                            logger.info("Received 401 Unauthorized error, refreshing authentication cookie...");
                            RefreshResult refreshResult = authCookieConfig.refreshCookieWithResult();
                            if (refreshResult == RefreshResult.REFRESHED) {
                                logger.info("Authentication cookie refreshed successfully, retrying request immediately");
                                continue; // Retry immediately with new cookie without counting as a retry
                            } else {
                                logger.warn("Failed to refresh authentication cookie");
                            }
                        }
                        
                        retryCount++;
                        if (retryCount < MAX_RETRIES) {
                            try {
                                Thread.sleep(1000 * retryCount);
                            } catch (InterruptedException ie) {
                                Thread.currentThread().interrupt();
                            }
                        }
                        continue;
                    }

                    String htmlContent = response.body().string();

                    // Extract movies from the collection page
                    List<Movie> pageMovies = extractMovieFromElement(htmlContent);
                    if (pageMovies != null && !pageMovies.isEmpty()) {
                        movies.addAll(pageMovies);
                    }
                    
                    logger.info(String.format("Successfully extracted %d movies from page %d", movies.size(), page));
                    
                    return movies; // Return immediately after successful processing
                }

            } catch (Exception e) {
                logger.errorf("Error processing collection page %d (attempt %d): %s", page, retryCount + 1, e.getMessage());
                retryCount++;

                // Add delay before retry
                try {
                    Thread.sleep(1000 * retryCount);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }
            }
        }

        logger.errorf("Failed to process collection page %d after %d attempts", page, MAX_RETRIES);
        return movies;
    }

    public List<Movie> extractMovieFromElement(String htmlContent) {
        List<Movie> movies = new ArrayList<>();
        try {
            Document doc = Jsoup.parse(htmlContent);
            Elements movieElements = doc.select(".box-item");
            
            logger.infof("Found %d movie elements on page", movieElements.size());
            
            for (Element element : movieElements) {
                try {
                    Movie movie = new Movie();

                    // 1. 从img标签提取缩略图和代码
                    Element imgElement = element.selectFirst("img.lazyload");
                    if (imgElement != null) {
                        // 缩略图
                        String thumbnail = imgElement.attr("data-src");
                        if (!thumbnail.isEmpty()) {
                            movie.setThumbnail(thumbnail);
                        }

                        // 从title属性获取代码
                        String code = imgElement.attr("title");
                        if (!code.isEmpty()) {
                            movie.setCode(code.trim());
                            // 先用代码作为基础标题
                            movie.setTitle(code.trim());
                        }
                    }

                    // 2. 如果从img标签没找到代码，从favourite按钮获取
                    if (movie.getCode() == null || movie.getCode().isEmpty()) {
                        Element favouriteButton = element.selectFirst(".favourite");
                        if (favouriteButton != null) {
                            String code = favouriteButton.attr("data-code");
                            if (!code.isEmpty()) {
                                movie.setCode(code.trim());
                                if (movie.getTitle() == null || movie.getTitle().isEmpty()) {
                                    movie.setTitle(code.trim());
                                }
                            }
                        }
                    }

                    // 3. 从detail链接获取完整标题
                    Element detailLink = element.selectFirst(".detail a");
                    if (detailLink != null) {
                        String fullText = detailLink.text().trim();

                        int firstDashIndex = fullText.indexOf(" - ");
                        if (firstDashIndex != -1) {
                            int secondDashIndex = fullText.indexOf(" - ", firstDashIndex + 3);

                            if (secondDashIndex != -1) {
                                // 提取第一个和第二个" - "之间的内容作为主标题
                                String mainTitle = fullText.substring(firstDashIndex + 3, secondDashIndex).trim();
                                if (!mainTitle.isEmpty()) {
                                    movie.setTitle(mainTitle);
                                }
                            } else {
                                // 如果只有一个" - "，提取其后的所有内容
                                String mainTitle = fullText.substring(firstDashIndex + 3).trim();
                                if (!mainTitle.isEmpty()) {
                                    movie.setTitle(mainTitle);
                                }
                            }
                        } else {
                            // 如果没有" - "，使用整个文本作为标题
                            movie.setTitle(fullText);
                        }
                    }

                    // 4. 提取原始ID和点赞数
                    Element favouriteButton = element.selectFirst(".favourite");
                    if (favouriteButton != null) {
                        String vScope = favouriteButton.attr("v-scope");
                        
                        // 调试: 打印前5个电影的v-scope属性
                        if (movies.size() < 5) {
                            logger.infof("Movie code: %s, v-scope attribute: [%s]", movie.getCode(), vScope);
                        }
                        
                        // 匹配Favourite('movie', 数字, 数字)格式
                        // 第2个数字是originalId，第3个数字是点赞数
                        Pattern pattern = Pattern.compile("Favourite\\('movie',\\s*(\\d+),\\s*(\\d+)\\)");
                        Matcher matcher = pattern.matcher(vScope);
                        if (matcher.find()) {
                            // 获取第一个数字作为originalId
                            String originalIdStr = matcher.group(1);
                            try {
                                int originalId = Integer.parseInt(originalIdStr);
                                movie.setOriginalId(originalId);
                                if (movies.size() < 5) {
                                    logger.infof("Successfully parsed originalId: %d for movie: %s", originalId, movie.getCode());
                                }
                            } catch (NumberFormatException e) {
                                logger.warnf("Failed to parse originalId: %s", originalIdStr);
                            }

                            // 获取第二个数字作为点赞数
                            String likesStr = matcher.group(2);
                            try {
                                movie.setLikes(Integer.parseInt(likesStr));
                            } catch (NumberFormatException e) {
                                logger.warnf("Failed to parse likes: %s", likesStr);
                            }
                        } else {
                            if (movies.size() < 5) {
                                logger.warnf("Failed to match v-scope pattern for movie: %s", movie.getCode());
                            }
                        }
                    }

                    // 5. 提取时长
                    Element durationElement = element.selectFirst(".duration");
                    if (durationElement != null) {
                        String duration = durationElement.text().trim();
                        if (!duration.isEmpty()) {
                            movie.setDuration(duration);
                        }
                    }

                    // 6. 提取链接
                    Element linkElement = element.selectFirst(".thumb a");
                    if (linkElement != null) {
                        String link = linkElement.attr("href");
                        if (!link.isEmpty()) {
                            movie.setLink(link);
                        }
                    }

                    // 最后检查：确保至少有代码和标题
                    if (movie.getCode() != null && !movie.getCode().isEmpty() &&
                        movie.getTitle() != null && !movie.getTitle().isEmpty()) {
                        movie.setStatus(CrawlerStatus.NEW.getValue());
                        movies.add(movie);
                    } else {
                        logger.warnf("跳过缺少代码或标题的电影. 代码: %s, 标题: %s", movie.getCode(), movie.getTitle());
                    }
                } catch (Exception e) {
                    logger.errorf("从元素提取电影详情时出错: %s", e.getMessage());
                }
            }
            return movies;
        } catch (Exception e) {
            logger.errorf("解析HTML内容时出错: %s", e.getMessage());
            return movies;
        }
    }


   


    /**
     * Get total collection pages in a separate transaction
     *
     * @return Number of collection pages
     */
    
    public int getCollectionPagesInSeparateTransaction() {
        try {
            return getTotalCollectionPages();
        } catch (Exception e) {
            logger.errorf("Error getting collection pages: %s", e.getMessage());
            return 0;
        }
    }

    /**
     * Get favorited movies in a separate transaction
     *
     * @param totalPages Total number of pages to process
     * @return List of favorited movies
     */
    
    public List<Movie> getFavoritedMoviesInSeparateTransaction(int totalPages) {
        try {
            return getFavoritedMovies(totalPages);
        } catch (Exception e) {
            logger.errorf("Error getting favorited movies: %s", e.getMessage());
            return Collections.emptyList();
        }
    }

    /**
     * Save movies in a separate transaction
     *
     * @param movies List of movies to save
     * @param savedCount Counter for saved movies
     */
    
    @Transactional
    public void saveMoviesInSeparateTransaction(List<Movie> movies, AtomicInteger savedCount) {
        try {
            logger.infof("Saving %d movies in separate transaction", movies.size());
            for (Movie movie : movies) {
                if (movie.getOriginalId() != null) {
                    // Check if movie already exists in database
                    Movie existingMovie = Movie.find("originalId = ?1", movie.getOriginalId()).firstResult();

                    if (existingMovie == null) {
                        // Save new movie
                        movie.persist();
                        savedCount.incrementAndGet();
                    } else {
                        // Update existing movie if needed
                        boolean updated = false;

                        if (movie.getTitle() != null && !movie.getTitle().isEmpty() &&
                            (existingMovie.getTitle() == null || existingMovie.getTitle().isEmpty())) {
                            existingMovie.setTitle(movie.getTitle());
                            updated = true;
                        }

                        if (movie.getCode() != null && !movie.getCode().isEmpty() &&
                            (existingMovie.getCode() == null || existingMovie.getCode().isEmpty())) {
                            existingMovie.setCode(movie.getCode());
                            updated = true;
                        }

                        if (movie.getThumbnail() != null && !movie.getThumbnail().isEmpty() &&
                            (existingMovie.getThumbnail() == null || existingMovie.getThumbnail().isEmpty())) {
                            existingMovie.setThumbnail(movie.getThumbnail());
                            updated = true;
                        }

                        if (updated) {
                            existingMovie.persist();
                            savedCount.incrementAndGet();
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.errorf("Error saving movies: %s", e.getMessage());
            logger.error("Stack trace:", e);
        }
    }

    /**
     * Find movies for processing in a new transaction
     * This method uses REQUIRES_NEW to isolate this transaction from any calling transaction
     *
     * @return Set of movie IDs to process
     */
    public Set<Integer> findMoviesForProcessingNonTransactional() {
        try {
            // Use direct JDBC or entity manager operations without transaction annotations
            String nativeQuery =
                "SELECT w.movie_id FROM watch_urls w " +
                "LEFT JOIN movies m ON w.movie_id = m.original_id " +
                // 1. 将 '?' 修改为命名参数, 例如 ':statuses'
                "WHERE w.status not in (:statuses) " +
                "AND m.original_id IS NULL " +
                "AND w.url is not null " +
                "ORDER BY w.movie_id ASC";

            // 创建一个包含状态值的 List
            List<String> statusValues = Arrays.asList(
                MovieStatus.NO_RESULT.getValue(),
                MovieStatus.FAILED.getValue(),
                MovieStatus.OFFLINE.getValue()
            );
            @SuppressWarnings("unchecked")
            List<Number> movieIdNumbers = WatchUrl.getEntityManager()
                    .createNativeQuery(nativeQuery)
                    .setParameter("statuses", statusValues)
                    .getResultList();

            if (!movieIdNumbers.isEmpty()) {
                // Convert the Number objects to Integer
                Set<Integer> movieIds = movieIdNumbers.stream()
                        .map(Number::intValue)
                        .collect(Collectors.toSet());

                logger.infof("Found %d movies without movie records to process", movieIds.size());

                return movieIds;
            } else {
                logger.info("No new movies found to process");
                return Collections.emptySet();
            }
        } catch (Exception e) {
            logger.errorf("Error finding movies for processing: %s", e.getMessage());
            logger.error("Stack trace:", e);
            return Collections.emptySet();
        }
    }

    /**
     * Process a group of movies in batch to optimize HTTP requests
     * This method adds up to 12 movies to favorites at once, processes them, then removes them
     * Uses REQUIRES_NEW to start a fresh transaction independent of any caller's transaction
     *
     * @param movieIds List of movie IDs to process as a group
     * @return Number of successfully processed movies
     */
    public int processMovieGroupNonTransactional(List<Integer> movieIds) {
        if (movieIds == null || movieIds.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        Set<Integer> movieIdSet = new HashSet<>(movieIds);

        try {
            logger.infof("Processing group of %d movies", movieIdSet.size());

            // Step 1: Add movies to favorites with retry mechanism
            logger.info("Adding movies to favorites with retry mechanism");
            addIdsToFavourites(movieIdSet);

            // Step 2 & 3: Process and save movie information for each movie in the group
            logger.info("Processing and saving movie information for each movie in the group");
            processAndSaveMovies(movieIdSet);

            // Step 4: Remove movies from favorites with retry mechanism
            logger.info("Removing movies from favorites with retry mechanism");
            removeIdsFromFavourites(movieIdSet);

            return successCount;
        } catch (Exception e) {
            logger.errorf("Error processing movie group: %s", e.getMessage());
            logger.error("Stack trace:", e);
            return successCount; // Return any movies that were successfully processed before the error
        }
    }

    /**
     * Add a single movie to favorites by ID
     *
     * @param movieId The ID of the movie to add to favorites
     */
    
    public void addMovieToFavoritesById(Integer movieId) {
        try {
            Set<Integer> ids = Collections.singleton(movieId);
            addIdsToFavourites(ids);
            logger.infof("Added movie ID %d to favorites", movieId);
        } catch (Exception e) {
            logger.errorf("Error adding movie ID %d to favorites: %s", movieId, e.getMessage());
        }
    }

    /**
     * Process and save movies in background threads with proper transaction handling
     * This method ensures a proper request context is activated for database operations
     * when called from background threads via CompletableFuture
     */
    public void processAndSaveMovies(Set<Integer> movieIds) {
        try {
            // Get Arc container to manually activate request context for database operations
            ArcContainer container = Arc.container();
            ManagedContext requestContext = container.requestContext();
            
            if (requestContext.isActive()) {
                // Context already active, proceed directly
                doProcessAndSaveMoviesWithTransaction(movieIds);
            } else {
                // Activate context for this thread
                try {
                    requestContext.activate();
                    doProcessAndSaveMoviesWithTransaction(movieIds);
                } finally {
                    requestContext.terminate();
                }
            }
        } catch (Exception e) {
            logger.errorf("Error in processAndSaveMovies: %s", e.getMessage());
            logger.error("Stack trace:", e);
        }
    }
    
    /**
     * Actual transactional implementation of movie processing and saving
     */
    @Transactional
    protected void doProcessAndSaveMoviesWithTransaction(Set<Integer> movieIds) {
        try {
            int totalPages = getTotalCollectionPages();
            if (totalPages == 0) {
                // 说明这几个电影其实没有对应 movies 信息，把对应 watchUrl 状态设置为 NO_RESULT
                for (Integer movieId : movieIds) {
                    WatchUrl.update("set status = ?2 where movieId = ?1", movieId, MovieStatus.NO_RESULT.getValue());
                }
                logger.infof("No collection pages found for movieIds: %s", movieIds);
                return;
            }
            logger.infof("Found %d collection pages", totalPages);

            // Get all collection pages - we only need a few since we're just looking for this specific movie
            List<Movie> movies = getFavoritedMovies(totalPages);
            logger.infof("Found %d movies in favorites collection", movies.size());

            for (Movie movie : movies) {

                // Check if movie exists
                Movie existingMovie = Movie.find("originalId = ?1", movie.getOriginalId()).firstResult();

                if (existingMovie == null) {
                    // Save new movie
                    movie.persist();
                } else {
                    // Update existing movie if needed
                    boolean updated = false;

                    if (movie.getTitle() != null && !movie.getTitle().isEmpty() &&
                        (existingMovie.getTitle() == null || existingMovie.getTitle().isEmpty())) {
                        existingMovie.setTitle(movie.getTitle());
                        updated = true;
                    }

                    if (movie.getCode() != null && !movie.getCode().isEmpty() &&
                        (existingMovie.getCode() == null || existingMovie.getCode().isEmpty())) {
                        existingMovie.setCode(movie.getCode());
                        updated = true;
                    }

                    if (movie.getThumbnail() != null && !movie.getThumbnail().isEmpty() &&
                        (existingMovie.getThumbnail() == null || existingMovie.getThumbnail().isEmpty())) {
                        existingMovie.setThumbnail(movie.getThumbnail());
                        updated = true;
                    }

                    if (updated) {
                        existingMovie.persist();
                    }
                }
            }
        } catch (Exception e) {
            logger.errorf("Error processing and saving movies: %s", e.getMessage());
        }
    }

    /**
     * Remove a single movie from favorites by ID
     *
     * @param movieId The ID of the movie to remove from favorites
     */
    
    public void removeMovieFromFavoritesById(Integer movieId) {
        try {
            Set<Integer> ids = Collections.singleton(movieId);
            removeIdsFromFavourites(ids);
            logger.infof("Removed movie ID %d from favorites", movieId);
        } catch (Exception e) {
            logger.errorf("Error removing movie ID %d from favorites: %s", movieId, e.getMessage());
        }
    }
}
