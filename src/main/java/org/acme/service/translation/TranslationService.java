package org.acme.service.translation;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.acme.entity.MovieInfo;
import org.acme.entity.MovieInfoTranslation;
import org.acme.enums.AzureAILanguageCode;
import org.acme.util.FileUtils;
import org.acme.util.ThreadPoolUtil;
import org.jboss.logging.Logger;

import com.cronutils.utils.StringUtils;

import io.quarkus.panache.common.Page;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.context.control.ActivateRequestContext;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class TranslationService {

    private static final Logger LOG = Logger.getLogger(TranslationService.class);

    // 所有支持的目标语言
    List<String> supportedLanguages = AzureAILanguageCode.getSupportedLanguageNames();

    @Inject
    AzureOpenAITranslationClient translationClient;

    @Inject
    TranslationService self;

    private final ThreadPoolExecutor executor = ThreadPoolUtil.createThreadPool(
                "translation-pool",
                5,    // 核心线程数
                10,   // 最大线程数
                100000   // 队列容量
    );

    // 移除@Transactional注解，因为该方法包含长时间运行的循环，不适合放入单个事务中
    public void triggerTranslation(int batchSize) {
        // 添加超时控制，检查超过10分钟未更新且依然处于translating状态的任务
        // 将事务操作封装在单独的方法中执行
        processTimeoutTasks();
        
        LOG.info("开始处理未翻译内容并补充缺失的目标语言");
        
        try {
            // 持续翻译直到没有新的内容需要翻译
            int totalTitlesProcessed = 0;
            int totalDescsProcessed = 0;
            int currentBatch = 0;
            int maxBatches = 200000; // 防止无限循环，设置最大批次数
            
            while (currentBatch < maxBatches) {
                currentBatch++;
                LOG.info("正在处理批次 " + currentBatch + "...");
                
                // 首先检测并补充缺失的语言翻译
                int missingLangsProcessed = supplementMissingLanguageTranslations(batchSize);
                
                // 然后正常处理标题和描述翻译
                int titlesProcessed = translateTitles(batchSize);
                int descsProcessed = translateDescriptions(batchSize);
                
                totalTitlesProcessed += titlesProcessed;
                totalDescsProcessed += descsProcessed;
                
                // 如果没有新的内容需要翻译，则结束循环
                if (missingLangsProcessed == 0 && titlesProcessed == 0 && descsProcessed == 0) {
                    LOG.info("所有翻译已完成，没有新的内容需要翻译");
                    break;
                }
                
                // 在批次之间等待一段时间，降低系统负载
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    LOG.error("翻译批次等待被中断", e);
                    break;
                }
            }
            
            LOG.info("翻译过程完成。共处理了 " + totalTitlesProcessed + " 个标题和 " + totalDescsProcessed + " 个描述");
            
            if (currentBatch >= maxBatches) {
                LOG.warn("达到最大批次数限制（" + maxBatches + "）。可能仍有内容需要翻译。");
            }
            
        } catch (Exception e) {
            LOG.error("翻译过程中发生错误", e);
            throw e;
        }
    }

    @Transactional
    public int translateTitles(int batchSize) {
        // 1. 查询只关心 titleStatus 的记录
        List<MovieInfo> movieInfos = MovieInfo.find(
                "language = 'ja' and title is not null and (titleStatus != 'translated' and titleStatus != 'translating' and titleStatus != 'failed')"
            ).page(Page.ofSize(batchSize)).list();

        if (movieInfos.isEmpty()) {
            // LOG.info("没有需要翻译的标题"); // 注释掉，避免日志刷屏
            return 0;
        }

        // 2. 只锁定 titleStatus
        List<Long> ids = movieInfos.stream().map(m -> m.id).collect(Collectors.toList());
        MovieInfo.update("titleStatus = 'translating' where id in (?1)", ids);

        Map<String, MovieInfo> titleToMovieMap = movieInfos.stream()
                .collect(Collectors.toMap(
                        mi -> mi.title,
                        mi -> mi,
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));

        List<String> titles = new ArrayList<>(titleToMovieMap.keySet());

        // 3. 异步执行翻译
        executeTranslationTasks(titles, titleToMovieMap, "title", ids);
        return movieInfos.size();
    }

    @Transactional
    public int translateDescriptions(int batchSize) {
        // 1. 获取 movie_info_translation 中已有标题翻译但无描述翻译的记录
        // 注意：解决查询逻辑问题，不仅查日语记录，改进查询条件
        LOG.info("查询需要进行描述翻译的记录...");
        List<MovieInfo> movieInfos = MovieInfo.find(
                "SELECT DISTINCT mi FROM MovieInfo mi WHERE EXISTS (" +
                "    SELECT 1 FROM MovieInfoTranslation mit " +
                "    WHERE mit.code = mi.code AND mit.title IS NOT NULL AND " +
                "    (mit.description IS NULL OR mit.description = '') AND " +
                "    mi.description IS NOT NULL" +
                ") AND mi.descriptionStatus != 'translated' AND mi.descriptionStatus != 'translating' AND mi.descriptionStatus != 'failed'").page(Page.ofSize(batchSize)).list();
        
        LOG.info("找到 " + movieInfos.size() + " 条需要进行描述翻译的记录");
    
        if (movieInfos.isEmpty()) {
            return 0;
        }

        // 2. 只锁定 descriptionStatus
        List<Long> ids = movieInfos.stream().map(m -> m.id).collect(Collectors.toList());
        MovieInfo.update("descriptionStatus = 'translating' where id in (?1)", ids);

        Map<String, String> descToMd5Map = movieInfos.stream()
                .filter(mi -> mi.description != null)
                .collect(Collectors.toMap(
                    mi -> mi.description,
                    mi -> FileUtils.getMD5(mi.description),
                    (existing, replacement) -> existing,
                    LinkedHashMap::new
                ));
        
        Map<String, MovieInfo> md5ToMovieMap = movieInfos.stream()
            .filter(mi -> mi.description != null)
            .collect(Collectors.toMap(
                mi -> FileUtils.getMD5(mi.description),
                mi -> mi,
                (existing, replacement) -> existing,
                LinkedHashMap::new
            ));

        List<String> uniqueDescriptions = new ArrayList<>(descToMd5Map.keySet());

        // 3. 异步执行翻译
        executeTranslationTasks(uniqueDescriptions, md5ToMovieMap, "description", ids);
        return movieInfos.size();
    }

    /**
     * 补充缺失的语言翻译
     * 检测每个已有翻译的记录，确保它们在所有支持的语言中都有翻译
     * 
     * @param batchSize 处理的批次大小
     * @return 补充翻译的记录数量
     */
    @Transactional
    public int supplementMissingLanguageTranslations(int batchSize) {
        LOG.info("检测并补充缺失的语言翻译...");
        
        // 1. 查询所有已经有部分翻译的MovieInfo记录
        List<MovieInfo> partiallyTranslatedMovies = MovieInfo.find(
                "SELECT DISTINCT mi FROM MovieInfo mi WHERE EXISTS (" +
                "    SELECT 1 FROM MovieInfoTranslation mit WHERE mit.code = mi.code" +
                ") AND (mi.titleStatus = 'translated' OR mi.descriptionStatus = 'translated')").page(Page.ofSize(batchSize)).list();
        
        if (partiallyTranslatedMovies.isEmpty()) {
            LOG.info("没有找到需要补充语言翻译的记录");
            return 0;
        }
        
        int processedCount = 0;
        
        for (MovieInfo movie : partiallyTranslatedMovies) {
            // 2. 获取这个movie已经有的翻译语言
            List<String> existingLanguages = MovieInfoTranslation.find(
                    "SELECT DISTINCT language FROM MovieInfoTranslation WHERE code = ?1",
                    movie.code).project(String.class).list();
            
            // 3. 找出缺失的语言
            List<String> missingLanguages = new ArrayList<>(supportedLanguages);
            
            // 将所有已经存在翻译的语言从缺失列表中移除
            for (String existingLang : existingLanguages) {
                // 将已存在的语言代码转换为显示名称，以与支持的语言列表进行匹配
                AzureAILanguageCode langCode = AzureAILanguageCode.fromLanguageCode(existingLang);
                if (langCode != null) {
                    String displayName = langCode.getDisplayName();
                    missingLanguages.remove(displayName);
                }
            }
            
            if (!missingLanguages.isEmpty()) {
                LOG.info("发现记录 " + movie.code + " 缺少 " + missingLanguages.size() + " 种语言翻译");
                
                // 4. 如果有缺失的语言，调用现有翻译部分进行补充翻译
                // 先补充标题翻译
                if (movie.titleStatus.equals("translated") && movie.title != null && !movie.title.trim().isEmpty()) {
                    supplementMissingTitleTranslations(movie, missingLanguages);
                    processedCount++;
                }
                
                // 然后补充描述翻译
                if (movie.descriptionStatus.equals("translated") && movie.description != null && !movie.description.trim().isEmpty()) {
                    supplementMissingDescriptionTranslations(movie, missingLanguages);
                    processedCount++;
                }
            }
        }
        
        LOG.info("已处理 " + processedCount + " 条需要补充语言翻译的记录");
        return processedCount;
    }
    
    /**
     * 补充缺失的标题翻译
     * 
     * @param movie 需要补充翻译的电影信息
     * @param missingLanguages 缺失的语言列表
     */
    private void supplementMissingTitleTranslations(MovieInfo movie, List<String> missingLanguages) {
        if (movie.title == null || movie.title.trim().isEmpty()) {
            LOG.warn("记录 " + movie.code + " 的标题为空，无法补充翻译");
            return;
        }
        
        LOG.info("正在补充记录 " + movie.code + " 的标题翻译，缺失语言: " + String.join(", ", missingLanguages));
        
        // 创建一个新的翻译任务请求，指定需要翻译的语言
        String originalTitle = movie.title;
        Map<String, MovieInfo> titleToMovieMap = new HashMap<>();
        titleToMovieMap.put(originalTitle, movie);
        
        try {
            // 调用翻译客户端翻译到缺失的语言
            String result = translationClient.translateBatch(List.of(originalTitle), "Japanese", missingLanguages, false, true);
            
            // 处理翻译结果并保存
            if (!result.startsWith("Translation failed:")) {
                List<MovieInfoTranslation> results = processResult(result, List.of(originalTitle), titleToMovieMap, "title");
                self.persistTranslationResults(results);
                
                LOG.info("成功补充记录 " + movie.code + " 的标题翻译，新增 " + results.size() + " 种语言");
            } else {
                LOG.error("补充标题翻译失败: " + result);
            }
            
        } catch (Exception e) {
            LOG.error("补充标题翻译过程中出现错误: " + e.getMessage(), e);
        }
    }
    
    /**
     * 补充缺失的描述翻译
     * 
     * @param movie 需要补充翻译的电影信息
     * @param missingLanguages 缺失的语言列表
     */
    private void supplementMissingDescriptionTranslations(MovieInfo movie, List<String> missingLanguages) {
        if (movie.description == null || movie.description.trim().isEmpty()) {
            LOG.warn("记录 " + movie.code + " 的描述为空，无法补充翻译");
            return;
        }
        
        LOG.info("正在补充记录 " + movie.code + " 的描述翻译，缺失语言: " + String.join(", ", missingLanguages));
        
        // 创建一个新的翻译任务请求，指定需要翻译的语言
        String originalDescription = movie.description;
        Map<String, MovieInfo> descToMovieMap = new HashMap<>();
        descToMovieMap.put(originalDescription, movie);
        
        try {
            // 调用翻译客户端翻译到缺失的语言
            String result = translationClient.translateBatch(List.of(originalDescription), "Japanese", missingLanguages, false, true);
            
            // 处理翻译结果并保存
            if (!result.startsWith("Translation failed:")) {
                List<MovieInfoTranslation> results = processResult(result, List.of(originalDescription), descToMovieMap, "description");
                self.persistTranslationResults(results);
                
                LOG.info("成功补充记录 " + movie.code + " 的描述翻译，新增 " + results.size() + " 种语言");
            } else {
                LOG.error("补充描述翻译失败: " + result);
            }
            
        } catch (Exception e) {
            LOG.error("补充描述翻译过程中出现错误: " + e.getMessage(), e);
        }
    }

    private void executeTranslationTasks(List<String> items,
                                         Map<String, MovieInfo> itemMap,
                                         String taskType,
                                         List<Long> ids) {
        long startTime = System.currentTimeMillis();

        CompletableFuture.runAsync(() -> {
            AtomicInteger successCount = new AtomicInteger(0);
            AtomicInteger failCount = new AtomicInteger(0);
            String finalStatus = "failed"; // 默认失败
            try {
                String result = translationClient.translateBatch(items, "Japanese", supportedLanguages, false, true);

                if (!result.startsWith("Translation failed:")) {
                    List<MovieInfoTranslation> results = processResult(result, items, itemMap, taskType);
                    self.persistTranslationResults(results);
                    finalStatus = "translated"; // 成功后标记为translated
                    successCount.incrementAndGet();
                } else {
                    LOG.errorf("Translation API failed for '%s': %s", items, result);
                    failCount.incrementAndGet();
                }
            } catch (Exception e) {
                LOG.error("Error processing item: " + items, e);
                failCount.incrementAndGet();
            } finally {
                // 无论成功失败，都更新源记录的状态
                self.updateSourceStatus(ids, taskType, finalStatus);
                logStatistics(startTime, items.size(), successCount.get(), failCount.get());
            }
        }, executor);
    }

    @ActivateRequestContext
    @Transactional
    public void persistTranslationResults(List<MovieInfoTranslation> results) {
        if (results == null || results.isEmpty()) {
            return;
        }
        for (MovieInfoTranslation translated : results) {
            MovieInfoTranslation existing = MovieInfoTranslation.find(
                    "code = ?1 and language = ?2", translated.code, translated.language
            ).firstResult();

            if (existing != null) {
                boolean updated = false;
                if (translated.title != null && !Objects.equals(existing.title, translated.title)) {
                    existing.title = translated.title;
                    updated = true;
                }
                if (translated.description != null && !Objects.equals(existing.description, translated.description)) {
                    existing.description = translated.description;
                    updated = true;
                }
                if (updated) {
                    existing.updatedAt = Instant.now();
                }
            } else {
                MovieInfoTranslation.persist(translated);
            }
        }
    }

    @ActivateRequestContext
    @Transactional
    public void updateSourceStatus(List<Long> ids, String taskType, String finalStatus) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        String statusField = "title".equals(taskType) ? "titleStatus" : "descriptionStatus";
        MovieInfo.update(statusField + " = ?1 where id in (?2)", finalStatus, ids);
    }

    // 用于检测包含多语言翻译结果模式的正则表达式
    private static final Pattern MULTIPLE_LANGUAGES_PATTERN = Pattern.compile(
            "(Russian|Turkish|Arabic|Dutch|Swedish|Polish|Danish|Norwegian|Finnish|Traditional Chinese|Simplified Chinese|English|Korean|Malay|Thai|German|French|Vietnamese|Indonesian|Filipino|Portuguese|Hindi)\\s+-\\s+\\[.*?\\]");

    /**
     * 检查文本是否包含多个语言标签对
     * 
     * @param text 要检查的文本
     * @return 如果包含多个语言标签对，返回true；否则返回false
     */
    private boolean containsMultipleLanguagePairs(String text) {
        if (text == null) return false;
        
        // 如果文本包含多个语言标签匹配项，判定为包含多语言
        int matchCount = 0;
        java.util.regex.Matcher matcher = MULTIPLE_LANGUAGES_PATTERN.matcher(text);
        while (matcher.find()) {
            matchCount++;
            if (matchCount > 1) return true; // 找到多个匹配项就返回true
        }
        
        return false; // 没有找到多个匹配项
    }
    
    private List<MovieInfoTranslation> processResult(String result, List<String> items,
                               Map<String, MovieInfo> itemMap, String taskType) {
        
        List<MovieInfoTranslation> results = Collections.synchronizedList(new ArrayList<>());
        List<Map<String, String>> translations = translationClient.parseBatchTranslationResponse(result, items.size());

        for (int i = 0; i < translations.size(); i++) {
            Map<String, String> translation = translations.get(i);
            String originalItem = items.get(i);
            // 对于描述，itemMap的key是md5，需要转换
            MovieInfo sourceInfo = "description".equals(taskType) 
                ? itemMap.get(FileUtils.getMD5(originalItem))
                : itemMap.get(originalItem);


            if (sourceInfo == null) continue;

            translation.forEach((lang, text) -> {
                if (AzureAILanguageCode.fromDisplayName(lang) != null && !StringUtils.isEmpty(text)) {
                    // 检查翻译结果中是否含有多语言标记
                    if (containsMultipleLanguagePairs(text)) {
                        LOG.errorf("检测到翻译结果包含多语言标记，可能为错误格式: %s = %s", lang, text.substring(0, Math.min(text.length(), 100)) + (text.length() > 100 ? "..." : ""));
                        return; // 跳过这个翻译结果
                    }
                    
                    MovieInfoTranslation translated = new MovieInfoTranslation();
                    translated.code = sourceInfo.code;
                    
                    if ("title".equals(taskType)) {
                        translated.title = text;
                    } else {
                        translated.description = text;
                    }
                    translated.language = AzureAILanguageCode.fromDisplayName(lang).getLanguageCode();
                    translated.createdAt = Instant.now();
                    translated.updatedAt = Instant.now();
                    results.add(translated);
                }
            });
        }
        return results;
    }

    private void logStatistics(long startTime, int total, int success, int fail) {
        long endTime = System.currentTimeMillis();
        LOG.info("\n=== Translation Summary ===");
        LOG.infof("Total items: %d", total);
        LOG.infof("Success: %d", success);
        LOG.infof("Failed: %d", fail);
        LOG.infof("Total time: %d seconds", (endTime - startTime) / 1000);
        if (total > 0) {
            LOG.infof("Average time per item: %.2f seconds", (endTime - startTime) / 1000.0 / total);
        }
    }

    /**
     * 检查超时的翻译任务并更新其状态
     * 对于movie_info_translation中updatedAt时间大于10分钟，并且title或者description为空，
     * 对应的movieinfo的status为translating的任务，将其状态修改为timeout状态
     */
    @Transactional
    public void processTimeoutTasks() {
        LOG.info("检查超时的翻译任务...");
        
        // 计算10分钟前的时间点
        Instant tenMinutesAgo = Instant.now().minus(Duration.ofMinutes(10));
        
        // 1. 查找超时的title翻译任务
        List<MovieInfoTranslation> timedOutTitleTasks = MovieInfoTranslation.find(
                "updatedAt < ?1 AND (title IS NULL OR title = '') AND EXISTS " +
                "(SELECT 1 FROM MovieInfo mi WHERE mi.code = code AND mi.titleStatus = 'translating')", 
                tenMinutesAgo).list();
        
        // 2. 查找超时的description翻译任务
        List<MovieInfoTranslation> timedOutDescTasks = MovieInfoTranslation.find(
                "updatedAt < ?1 AND (description IS NULL OR description = '') AND EXISTS " +
                "(SELECT 1 FROM MovieInfo mi WHERE mi.code = code AND mi.descriptionStatus = 'translating')", 
                tenMinutesAgo).list();
        
        int titleTimeoutsCount = updateTimeoutStatus(timedOutTitleTasks, "title");
        int descTimeoutsCount = updateTimeoutStatus(timedOutDescTasks, "description");
        
        if (titleTimeoutsCount > 0 || descTimeoutsCount > 0) {
            LOG.infof("已更新 %d 个超时的标题翻译任务和 %d 个超时的描述翻译任务为timeout状态", 
                    titleTimeoutsCount, descTimeoutsCount);
        }
    }
    
    /**
     * 更新超时任务的状态
     * 
     * @param timedOutTasks 超时任务列表
     * @param taskType 任务类型 ("title" 或 "description")
     * @return 更新的任务数量
     */
    @Transactional
    protected int updateTimeoutStatus(List<MovieInfoTranslation> timedOutTasks, String taskType) {
        if (timedOutTasks.isEmpty()) {
            return 0;
        }
        
        List<String> codes = timedOutTasks.stream()
                .map(task -> task.code)
                .collect(Collectors.toList());
        
        // 暂时不在超时任务处理中补充缺失翻译
        // supplementMissingLanguageTranslations(batchSize, codes);
        
        // 更新对应的 MovieInfo 状态为 "timeout"
        String statusField = "title".equals(taskType) ? "titleStatus" : "descriptionStatus";
        MovieInfo.update(statusField + " = 'timeout' WHERE code IN (?1) AND " + 
                        statusField + " = 'translating'", codes);
        
        return codes.size();
    }

    public void shutdown() {
        ThreadPoolUtil.shutdownThreadPool(executor, 30); // 安全关闭线程池
    }
}