package org.acme.service.translation;

import io.quarkus.runtime.StartupEvent;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import org.acme.entity.MovieInfo;
import org.acme.entity.MovieInfoTranslation;
import org.acme.entity.TranslationTask;
import org.acme.entity.TranslationTask.ContentType;
import org.acme.entity.TranslationTask.TaskStatus;
import org.acme.enums.AzureAILanguageCode;
import org.jboss.logging.Logger;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 翻译系统迁移工具
 * 负责从旧版翻译系统迁移数据到新的任务驱动模式
 */
@ApplicationScoped
public class TranslationSystemMigrator {

    private static final Logger LOG = Logger.getLogger(TranslationSystemMigrator.class);
    
    @Inject
    TranslationTaskManager taskManager;
    
    /**
     * 系统启动时检查并初始化任务表
     */
    void onStart(@Observes StartupEvent ev) {
        LOG.info("检查翻译任务表...");
        
        // 如果任务表为空，则执行初始化
        if (TranslationTask.count() == 0) {
            LOG.info("翻译任务表为空，开始初始化...");
            initializeTasksFromExistingData();
        } else {
            LOG.info("翻译任务表已存在数据，跳过初始化");
        }
    }
    
    /**
     * 从现有数据初始化任务表
     */
    @Transactional
    public void initializeTasksFromExistingData() {
        try {
            LOG.info("开始从现有数据初始化翻译任务...");
            
            // 1. 处理所有已有内容的标题翻译任务
            List<MovieInfo> moviesWithTitle = MovieInfo.find(
                "title IS NOT NULL AND title != ''"
            ).list();
            
            AtomicInteger titleTasksCount = new AtomicInteger(0);
            LOG.infof("找到 %d 个有标题的记录", moviesWithTitle.size());
            
            for (MovieInfo movie : moviesWithTitle) {
                // 创建标题翻译任务记录
                List<String> supportedLanguages = AzureAILanguageCode.getSupportedLanguageCodes();
                for (String targetLangCode : supportedLanguages) {
                    // 跳过源语言
                    if ("ja".equals(targetLangCode)) {
                        continue;
                    }
                    
                    // 检查是否已经有对应的翻译
                    MovieInfoTranslation existingTranslation = MovieInfoTranslation.findByCodeAndLanguage(
                        movie.code, targetLangCode
                    );
                    
                    // 根据已有翻译状态创建任务
                    TranslationTask task = new TranslationTask();
                    task.contentId = movie.code;
                    task.contentType = ContentType.TITLE;
                    task.sourceLanguage = "ja";
                    task.targetLanguage = targetLangCode;
                    
                    // 如果已有翻译且有标题，则标记为已完成
                    if (existingTranslation != null && existingTranslation.title != null && !existingTranslation.title.trim().isEmpty()) {
                        task.status = TaskStatus.COMPLETED;
                    } else {
                        task.status = TaskStatus.PENDING;
                    }
                    
                    // 保存任务
                    task.persist();
                    titleTasksCount.incrementAndGet();
                }
            }
            
            // 2. 处理所有已有内容的描述翻译任务
            List<MovieInfo> moviesWithDescription = MovieInfo.find(
                "description IS NOT NULL AND description != ''"
            ).list();
            
            AtomicInteger descTasksCount = new AtomicInteger(0);
            LOG.infof("找到 %d 个有描述的记录", moviesWithDescription.size());
            
            for (MovieInfo movie : moviesWithDescription) {
                // 创建描述翻译任务记录
                List<String> supportedLanguages = AzureAILanguageCode.getSupportedLanguageCodes();
                for (String targetLangCode : supportedLanguages) {
                    // 跳过源语言
                    if ("ja".equals(targetLangCode)) {
                        continue;
                    }
                    
                    // 检查是否已经有对应的翻译
                    MovieInfoTranslation existingTranslation = MovieInfoTranslation.findByCodeAndLanguage(
                        movie.code, targetLangCode
                    );
                    
                    // 根据已有翻译状态创建任务
                    TranslationTask task = new TranslationTask();
                    task.contentId = movie.code;
                    task.contentType = ContentType.DESCRIPTION;
                    task.sourceLanguage = "ja";
                    task.targetLanguage = targetLangCode;
                    
                    // 如果已有翻译且有描述，则标记为已完成
                    if (existingTranslation != null && existingTranslation.description != null && !existingTranslation.description.trim().isEmpty()) {
                        task.status = TaskStatus.COMPLETED;
                    } else {
                        task.status = TaskStatus.PENDING;
                    }
                    
                    // 保存任务
                    task.persist();
                    descTasksCount.incrementAndGet();
                }
            }
            
            LOG.infof("初始化完成: 创建了 %d 个标题翻译任务和 %d 个描述翻译任务", 
                      titleTasksCount.get(), descTasksCount.get());
            
        } catch (Exception e) {
            LOG.error("初始化翻译任务失败", e);
            throw e;
        }
    }
    
    /**
     * 手动触发迁移
     */
    @Transactional
    public void migrateTranslationSystem() {
        // 1. 删除所有现有任务
        long deleted = TranslationTask.deleteAll();
        LOG.infof("已删除 %d 个现有任务记录", deleted);
        
        // 2. 从头初始化
        initializeTasksFromExistingData();
    }
}
