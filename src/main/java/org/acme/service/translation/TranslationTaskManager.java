package org.acme.service.translation;

import io.quarkus.panache.common.Page;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import org.acme.entity.MovieInfo;
import org.acme.entity.TranslationTask;
import org.acme.entity.TranslationTask.ContentType;
import org.acme.entity.TranslationTask.TaskStatus;

import java.util.HashMap;
import org.acme.enums.AzureAILanguageCode;
import org.jboss.logging.Logger;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 翻译任务管理器
 * 负责创建、查询和更新翻译任务
 */
@ApplicationScoped
public class TranslationTaskManager {

    private static final Logger LOG = Logger.getLogger(TranslationTaskManager.class);
    
    /**
     * 为一个MovieInfo创建所有目标语言的翻译任务
     * 
     * @param movie 需要翻译的电影信息
     * @return 创建的任务数量
     */
    @Transactional
    public int createTasksForContent(MovieInfo movie) {
        if (movie == null) {
            return 0;
        }
        
        int taskCount = 0;
        List<AzureAILanguageCode> supportedLanguages = AzureAILanguageCode.getSupportedLanguages();
        List<TranslationTask> taskList = new ArrayList<>();
        
        // 只为有内容的字段创建翻译任务
        boolean hasTitle = movie.title != null && !movie.title.trim().isEmpty();
        boolean hasDescription = movie.description != null && !movie.description.trim().isEmpty();
        
        for (AzureAILanguageCode targetLang : supportedLanguages) {
            // 跳过源语言 (假设源语言是日语)
            if (targetLang == AzureAILanguageCode.JAPANESE) {
                continue;
            }
            
            // 为标题创建翻译任务
            if (hasTitle) {
                // 检查是否已存在
                if (!taskExists(movie.code, ContentType.TITLE, targetLang.getLanguageCode())) {
                    TranslationTask titleTask = TranslationTask.createFromMovieInfo(movie, targetLang, ContentType.TITLE);
                    taskList.add(titleTask);
                    taskCount++;
                }
            }
            
            // 为描述创建翻译任务
            // if (hasDescription) {
            //     // 检查是否已存在
            //     if (!taskExists(movie.code, ContentType.DESCRIPTION, targetLang.getLanguageCode())) {
            //         TranslationTask descTask = TranslationTask.createFromMovieInfo(movie, targetLang, ContentType.DESCRIPTION);
            //         taskList.add(descTask);
            //         taskCount++;
            //     }
            // }
        }
        
        // 批量保存任务
        if (!taskList.isEmpty()) {
            TranslationTask.persist(taskList);
            LOG.infof("为内容 %s 创建了 %d 个翻译任务", movie.code, taskCount);
        }
        
        return taskCount;
    }
    
    /**
     * 检查特定内容和目标语言的任务是否已存在
     */
    private boolean taskExists(String contentId, String contentType, String targetLanguage) {
        return TranslationTask.count(
            "contentId = ?1 AND contentType = ?2 AND targetLanguage = ?3", 
            contentId, contentType, targetLanguage
        ) > 0;
    }
    
    /**
     * 创建所有待翻译 MovieInfo 的翻译任务
     * 
     * @param batchSize 每次处理的MovieInfo数量
     * @return 创建的任务总数
     */
    @Transactional
    public int createTasksForAllPendingContent(int batchSize) {
        List<MovieInfo> pendingMovies = MovieInfo.find(
            "title IS NOT NULL AND title != '' AND (titleStatus IS NULL OR titleStatus NOT IN ('translated', 'translating'))"
        ).page(Page.ofSize(batchSize)).list();
        
        int totalTasks = 0;
        for (MovieInfo movie : pendingMovies) {
            totalTasks += createTasksForContent(movie);
        }
        
        // 描述翻译任务
        // List<MovieInfo> pendingDescMovies = MovieInfo.find(
        //     "description IS NOT NULL AND description != '' AND (descriptionStatus IS NULL OR descriptionStatus NOT IN ('translated', 'translating'))"
        // ).page(Page.ofSize(batchSize)).list();
        
        // for (MovieInfo movie : pendingDescMovies) {
        //     totalTasks += createTasksForContent(movie);
        // }
        
        return totalTasks;
    }
    
    /**
     * 获取一批待处理的任务
     * 
     * @param batchSize 批次大小
     * @return 待处理的任务列表
     */
    @Transactional
    public List<TranslationTask> getNextPendingTasks(int batchSize) {
        // 按优先级和创建时间排序
        return TranslationTask.find(
            "status = ?1 ORDER BY priority DESC, createdAt ASC", 
            TaskStatus.PENDING
        ).page(Page.ofSize(batchSize)).list();
    }
    
    /**
     * 按内容类型获取待处理任务
     * 
     * @param contentType 内容类型（标题或描述）
     * @param batchSize 批次大小
     * @return 待处理的任务列表
     */
    @Transactional
    public List<TranslationTask> getPendingTasksByType(String contentType, int batchSize) {
        return TranslationTask.find(
            "status = ?1 AND contentType = ?2 ORDER BY priority DESC, createdAt ASC",
            TaskStatus.PENDING, contentType
        ).page(Page.ofSize(batchSize)).list();
    }
    
    /**
     * 获取特定内容所有待处理的翻译任务
     */
    public List<TranslationTask> getPendingTasksForContent(String contentId) {
        if (contentId == null || contentId.isEmpty()) {
            return Collections.emptyList();
        }
        
        return TranslationTask.find(
            "contentId = ?1 AND status = ?2",
            contentId, TaskStatus.PENDING
        ).list();
    }
    
    /**
     * 更新任务状态
     */
    @Transactional
    public void updateTaskStatus(Long taskId, String status) {
        TranslationTask task = TranslationTask.findById(taskId);
        if (task != null) {
            task.updateStatus(status);
        }
    }
    
    /**
     * 更新任务状态并记录错误
     */
    @Transactional
    public void markTaskAsFailed(Long taskId, String errorMessage) {
        TranslationTask task = TranslationTask.findById(taskId);
        if (task != null) {
            task.markAsFailed(errorMessage);
        }
    }
    
    /**
     * 批量更新任务状态
     * 使用 ActivateRequestContext 确保在异步线程中有活跃的请求上下文
     */
    @jakarta.enterprise.context.control.ActivateRequestContext
    @Transactional
    public int updateTasksStatus(List<Long> taskIds, String status) {
        if (taskIds == null || taskIds.isEmpty()) {
            return 0;
        }
        
        int updated = TranslationTask.update(
            "status = ?1, updatedAt = ?2 WHERE id IN (?3)",
            status, LocalDateTime.now(), taskIds
        );
        
        return updated;
    }
    
    /**
     * 检查和处理超时任务
     * 
     * @param timeoutMinutes 超时分钟数
     * @return 处理的超时任务数量
     */
    @Transactional
    public int processTimeoutTasks(int timeoutMinutes) {
        LocalDateTime timeoutThreshold = LocalDateTime.now().minus(Duration.ofMinutes(timeoutMinutes));
        
        List<TranslationTask> timeoutTasks = TranslationTask.find(
            "status = ?1 AND updatedAt < ?2",
            TaskStatus.PROCESSING, timeoutThreshold
        ).list();
        
        for (TranslationTask task : timeoutTasks) {
            task.markAsTimeout();
        }
        
        return timeoutTasks.size();
    }
    
    /**
     * 重置失败或超时的任务以便重试
     * 
     * @param maxRetries 最大重试次数
     * @return 重置的任务数量
     */
    @Transactional
    public int resetFailedTasks(int maxRetries) {
        List<TranslationTask> failedTasks = TranslationTask.find(
            "status IN (?1, ?2) AND retryCount < ?3",
            TaskStatus.FAILED, TaskStatus.TIMEOUT, maxRetries
        ).list();
        
        for (TranslationTask task : failedTasks) {
            task.resetForRetry();
        }
        
        return failedTasks.size();
    }
    
    /**
     * 获取翻译任务统计信息
     */
    public Map<String, Long> getTaskStatistics() {
        List<?> rawResults = TranslationTask.find("SELECT t.status, COUNT(t) FROM TranslationTask t GROUP BY t.status").list();
        List<Object[]> results = new ArrayList<>();
        
        for (Object rawRow : rawResults) {
            if (rawRow instanceof Object[]) {
                results.add((Object[]) rawRow);
            }
        }
        Map<String, Long> resultMap = new HashMap<>();
        
        for (Object[] row : results) {
            String status = (String) row[0];
            Long count = (Long) row[1];
            resultMap.put(status, count);
        }
        
        return resultMap;
    }
    
    /**
     * 获取特定内容的翻译任务完成百分比
     */
    public double getCompletionPercentage(String contentId) {
        long total = TranslationTask.count("contentId = ?1", contentId);
        if (total == 0) {
            return 0.0;
        }
        
        long completed = TranslationTask.count("contentId = ?1 AND status = ?2", 
                                              contentId, TaskStatus.COMPLETED);
        
        return (double)completed / total * 100.0;
    }
}
