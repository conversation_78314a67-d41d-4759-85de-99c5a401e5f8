package org.acme.service.translation;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.acme.config.ProxyConfig;
import org.acme.config.ProxyConfig.ProxyEntry;
import org.jboss.logging.Logger;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

/**
 * Manages the proxy pool for Azure OpenAI translation services.
 * Provides functionality for monitoring proxy health, usage statistics, and dynamic proxy management.
 */
@ApplicationScoped
public class ProxyPoolManager {
    private static final Logger LOG = Logger.getLogger(ProxyPoolManager.class);
    
    @Inject
    ProxyConfig proxyConfig;
    
    @Inject
    @ProxyEnabled
    ProxyAwareAzureOpenAIClient proxyClient;
    
    // Track proxy usage statistics
    private final Map<String, ProxyStats> proxyStats = new ConcurrentHashMap<>();
    
    /**
     * Initializes the proxy pool with a list of proxies.
     * 
     * @param proxyList List of proxy specifications in format host:port:username:password:group:priority
     *                  (username and password can be empty)
     * @return Number of successfully added proxies
     */
    public int initializeProxyPool(List<String> proxyList) {
        int addedCount = 0;
        
        for (String proxySpec : proxyList) {
            try {
                String[] parts = proxySpec.split(":");
                if (parts.length < 2) {
                    LOG.warnf("Invalid proxy specification: %s", proxySpec);
                    continue;
                }
                
                String host = parts[0];
                int port = Integer.parseInt(parts[1]);
                
                // Optional username and password
                String username = (parts.length > 2 && !parts[2].isEmpty()) ? parts[2] : null;
                String password = (parts.length > 3 && !parts[3].isEmpty()) ? parts[3] : null;
                
                // Optional group and priority
                String group = (parts.length > 4 && !parts[4].isEmpty()) ? parts[4] : "default";
                int priority = (parts.length > 5 && !parts[5].isEmpty()) ? Integer.parseInt(parts[5]) : 1;
                
                // Add the proxy
                proxyConfig.addProxy(host, port, username, password, group, priority);
                
                // Initialize stats for this proxy
                String proxyKey = host + ":" + port;
                proxyStats.put(proxyKey, new ProxyStats());
                
                addedCount++;
                LOG.infof("Added proxy %s:%d to pool (group: %s, priority: %d)", host, port, group, priority);
            } catch (Exception e) {
                LOG.warnf("Failed to add proxy from specification '%s': %s", proxySpec, e.getMessage());
            }
        }
        
        LOG.infof("Initialized proxy pool with %d proxies", addedCount);
        return addedCount;
    }
    
    /**
     * Tests all proxies in the pool and returns their status.
     * 
     * @return Map of proxy identifiers to their test results
     */
    public Map<String, Boolean> testAllProxies() {
        Map<String, Boolean> results = new HashMap<>();
        
        for (ProxyEntry proxy : proxyConfig.getAllProxies()) {
            String proxyId = proxy.getHost() + ":" + proxy.getPort();
            boolean success = proxyClient.testProxyConnection(
                proxy.getHost(), proxy.getPort(), proxy.getUsername(), proxy.getPassword());
            
            results.put(proxyId, success);
            
            // Update proxy stats
            ProxyStats stats = proxyStats.computeIfAbsent(proxyId, k -> new ProxyStats());
            if (success) {
                stats.successfulTests.incrementAndGet();
            } else {
                stats.failedTests.incrementAndGet();
            }
        }
        
        return results;
    }
    
    /**
     * Records a successful request for a proxy.
     * 
     * @param proxy The proxy used for the request
     */
    public void recordSuccessfulRequest(ProxyEntry proxy) {
        if (proxy == null) return;
        
        String proxyId = proxy.getHost() + ":" + proxy.getPort();
        ProxyStats stats = proxyStats.computeIfAbsent(proxyId, k -> new ProxyStats());
        stats.successfulRequests.incrementAndGet();
    }
    
    /**
     * Records a failed request for a proxy.
     * 
     * @param proxy The proxy used for the request
     * @param errorMessage The error message
     */
    public void recordFailedRequest(ProxyEntry proxy, String errorMessage) {
        if (proxy == null) return;
        
        String proxyId = proxy.getHost() + ":" + proxy.getPort();
        ProxyStats stats = proxyStats.computeIfAbsent(proxyId, k -> new ProxyStats());
        stats.failedRequests.incrementAndGet();
        
        // Store the last error message
        stats.lastErrorMessage = errorMessage;
    }
    
    /**
     * Gets statistics for all proxies in the pool.
     * 
     * @return Map of proxy identifiers to their statistics
     */
    public Map<String, Map<String, Object>> getProxyStatistics() {
        Map<String, Map<String, Object>> statistics = new HashMap<>();
        
        for (ProxyEntry proxy : proxyConfig.getAllProxies()) {
            String proxyId = proxy.getHost() + ":" + proxy.getPort();
            ProxyStats stats = proxyStats.getOrDefault(proxyId, new ProxyStats());
            
            Map<String, Object> proxyStats = new HashMap<>();
            proxyStats.put("host", proxy.getHost());
            proxyStats.put("port", proxy.getPort());
            proxyStats.put("group", proxy.getGroup());
            proxyStats.put("priority", proxy.getPriority());
            proxyStats.put("hasAuth", proxy.hasAuthentication());
            proxyStats.put("successfulRequests", stats.successfulRequests.get());
            proxyStats.put("failedRequests", stats.failedRequests.get());
            proxyStats.put("successfulTests", stats.successfulTests.get());
            proxyStats.put("failedTests", stats.failedTests.get());
            proxyStats.put("lastErrorMessage", stats.lastErrorMessage);
            
            statistics.put(proxyId, proxyStats);
        }
        
        return statistics;
    }
    
    /**
     * Gets a summary of proxy pool health.
     * 
     * @return Summary statistics about the proxy pool
     */
    public Map<String, Object> getProxyPoolSummary() {
        Map<String, Object> summary = new HashMap<>();
        
        int totalProxies = proxyConfig.size();
        int activeProxies = (int) proxyStats.values().stream()
            .filter(stats -> stats.successfulRequests.get() > 0)
            .count();
        
        long totalSuccessfulRequests = proxyStats.values().stream()
            .mapToLong(stats -> stats.successfulRequests.get())
            .sum();
        
        long totalFailedRequests = proxyStats.values().stream()
            .mapToLong(stats -> stats.failedRequests.get())
            .sum();
        
        // Calculate success rate
        double successRate = 0;
        if (totalSuccessfulRequests + totalFailedRequests > 0) {
            successRate = (double) totalSuccessfulRequests / (totalSuccessfulRequests + totalFailedRequests);
        }
        
        // Group proxies by group
        Map<String, Long> proxiesByGroup = proxyConfig.getAllProxies().stream()
            .collect(Collectors.groupingBy(ProxyEntry::getGroup, Collectors.counting()));
        
        summary.put("totalProxies", totalProxies);
        summary.put("activeProxies", activeProxies);
        summary.put("totalSuccessfulRequests", totalSuccessfulRequests);
        summary.put("totalFailedRequests", totalFailedRequests);
        summary.put("successRate", successRate);
        summary.put("proxiesByGroup", proxiesByGroup);
        
        return summary;
    }
    
    /**
     * Adds a new proxy to the pool.
     * 
     * @param host Proxy host
     * @param port Proxy port
     * @param username Username for authentication (optional)
     * @param password Password for authentication (optional)
     * @param group Group name for categorization
     * @param priority Priority (lower number means higher priority)
     * @return true if the proxy was added successfully, false otherwise
     */
    public boolean addProxy(String host, int port, String username, String password, String group, int priority) {
        try {
            proxyConfig.addProxy(host, port, username, password, group, priority);
            
            // Initialize stats for this proxy
            String proxyKey = host + ":" + port;
            proxyStats.put(proxyKey, new ProxyStats());
            
            LOG.infof("Added proxy %s:%d to pool (group: %s, priority: %d)", host, port, group, priority);
            return true;
        } catch (Exception e) {
            LOG.errorf("Failed to add proxy %s:%d: %s", host, port, e.getMessage());
            return false;
        }
    }
    
    /**
     * Tests a proxy connection before adding it to the pool.
     * 
     * @param host Proxy host
     * @param port Proxy port
     * @param username Username for authentication (optional)
     * @param password Password for authentication (optional)
     * @return true if the connection is successful, false otherwise
     */
    public boolean testProxy(String host, int port, String username, String password) {
        return proxyClient.testProxyConnection(host, port, username, password);
    }
    
    /**
     * Class to track statistics for a proxy.
     */
    private static class ProxyStats {
        final AtomicInteger successfulRequests = new AtomicInteger(0);
        final AtomicInteger failedRequests = new AtomicInteger(0);
        final AtomicInteger successfulTests = new AtomicInteger(0);
        final AtomicInteger failedTests = new AtomicInteger(0);
        String lastErrorMessage = "";
    }
}
