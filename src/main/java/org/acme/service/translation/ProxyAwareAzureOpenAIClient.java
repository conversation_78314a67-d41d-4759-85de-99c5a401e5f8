package org.acme.service.translation;

import java.io.IOException;
import java.util.Optional;

import org.acme.config.AzureOpenAIConfig;
import org.acme.config.AzureOpenAIConfig.ClientConfig;
import org.acme.config.ProxyConfig;
import org.acme.config.ProxyConfig.ProxyEntry;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.jboss.logging.Logger;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * A decorator for AzureOpenAITranslationClient that adds proxy support.
 * This class extends AzureOpenAITranslationClient to provide seamless proxy functionality.
 */
@ApplicationScoped
@ProxyEnabled
public class ProxyAwareAzureOpenAIClient extends AzureOpenAITranslationClient {
    private static final Logger LOG = Logger.getLogger(ProxyAwareAzureOpenAIClient.class);
    
    @Inject
    ProxyConfig proxyConfig;
    
    @Inject
    ProxyEnabledHttpClient httpClientFactory;
    
    @Inject
    AzureOpenAIConfig azureConfig;
    
    @PostConstruct
    void onStart() {
        LOG.info("Initializing ProxyAwareAzureOpenAIClient");
        LOG.infof("Azure OpenAI Config: %s", azureConfig);
        LOG.infof("Proxy Config: %s", proxyConfig);
        
        // If proxy pool is not empty, set a proxy for the client
        if (!proxyConfig.isEmpty()) {
            Optional<ProxyEntry> proxyOpt = proxyConfig.getNextProxy();
            if (proxyOpt.isPresent()) {
                setCurrentProxy(proxyOpt.get());
            }
        }
    }
    
    /**
     * Translates text using proxy support.
     * 
     * @param text Text to translate
     * @param sourceLanguage Source language
     * @param targetLanguage Target language
     * @param isSubtitle Whether this is a subtitle translation
     * @param isAVContext Whether this is an adult video context
     * @return Translated text
     */
    @Override
    public String translate(String text, String sourceLanguage, String targetLanguage, 
                           boolean isSubtitle, boolean isAVContext) {
        // If proxy pool is empty, use the parent implementation directly
        if (proxyConfig.isEmpty()) {
            LOG.info("No proxies available, using direct connection");
            return super.translate(text, sourceLanguage, targetLanguage, isSubtitle, isAVContext);
        }
        
        // Use proxy-enabled client for translation
        return executeWithProxy(() -> 
            super.translate(text, sourceLanguage, targetLanguage, isSubtitle, isAVContext));
    }
    
    /**
     * Translates a batch of texts using proxy support.
     * 
     * @param textsToTranslate List of texts to translate
     * @param sourceLanguage Source language
     * @param targetLanguages List of target languages
     * @param isSubtitle Whether this is a subtitle translation
     * @param isAVContext Whether this is an adult video context
     * @return Translated text
     */
    @Override
    public String translateBatch(java.util.List<String> textsToTranslate, String sourceLanguage, 
                               java.util.List<String> targetLanguages, boolean isSubtitle, boolean isAVContext) {
        // If proxy pool is empty, use the parent implementation directly
        if (proxyConfig.isEmpty()) {
            LOG.info("No proxies available, using direct connection for batch translation");
            return super.translateBatch(textsToTranslate, sourceLanguage, targetLanguages, isSubtitle, isAVContext);
        }
        
        // Use proxy-enabled client for batch translation
        return executeWithProxy(() -> 
            super.translateBatch(textsToTranslate, sourceLanguage, targetLanguages, isSubtitle, isAVContext));
    }
    
    /**
     * Translates subtitle text using proxy support.
     * 
     * @param text Text to translate
     * @param sourceLanguage Source language
     * @param targetLanguage Target language
     * @return Translated text
     */
    @Override
    public String translateSubtitle(String text, String sourceLanguage, String targetLanguage) {
        // If proxy pool is empty, use the parent implementation directly
        if (proxyConfig.isEmpty()) {
            LOG.info("No proxies available, using direct connection for subtitle translation");
            return super.translateSubtitle(text, sourceLanguage, targetLanguage);
        }
        
        // Use proxy-enabled client for subtitle translation
        return executeWithProxy(() -> 
            super.translateSubtitle(text, sourceLanguage, targetLanguage));
    }
    
    /**
     * Translates large subtitle text using proxy support.
     * This method handles large subtitle files by breaking them into chunks.
     * 
     * @param text Text to translate
     * @param sourceLanguage Source language
     * @param targetLanguage Target language
     * @return Translated text
     */
    public String translateLargeSubtitle(String text, String sourceLanguage, String targetLanguage) {
        // If proxy pool is empty, use direct connection
        if (proxyConfig.isEmpty()) {
            LOG.info("No proxies available, using direct connection for large subtitle translation");
            // Use translate with subtitle flag set to true
            return super.translate(text, sourceLanguage, targetLanguage, true, true);
        }
        
        // Use proxy-enabled client for large subtitle translation
        return executeWithProxy(() -> 
            super.translate(text, sourceLanguage, targetLanguage, true, true));
    }
    
    /**
     * Translates normal text using proxy support.
     * 
     * @param text Text to translate
     * @param sourceLanguage Source language
     * @param targetLanguage Target language
     * @return Translated text
     */
    @Override
    public String translateText(String text, String sourceLanguage, String targetLanguage) {
        // If proxy pool is empty, use the parent implementation directly
        if (proxyConfig.isEmpty()) {
            LOG.info("No proxies available, using direct connection for text translation");
            return super.translateText(text, sourceLanguage, targetLanguage);
        }
        
        // Use proxy-enabled client for text translation
        return executeWithProxy(() -> 
            super.translateText(text, sourceLanguage, targetLanguage));
    }
    
    /**
     * Executes a translation operation with proxy support.
     * If the operation fails with the first proxy, it will retry with different proxies.
     * 
     * @param operation The translation operation to execute
     * @return The result of the translation operation
     */
    private <T> T executeWithProxy(TranslationOperation<T> operation) {
        // Get all available proxies for potential retries
        java.util.List<ProxyEntry> proxies = proxyConfig.getAllProxies();
        
        // Track which proxies we've tried
        java.util.Set<String> triedProxies = new java.util.HashSet<>();
        
        // Get the first proxy
        Optional<ProxyEntry> proxyOpt = proxyConfig.getNextProxy();
        if (proxyOpt.isEmpty()) {
            // This shouldn't happen since we checked isEmpty() earlier, but just in case
            LOG.warn("No proxies available despite earlier check, using direct connection");
            return operation.execute();
        }
        
        ProxyEntry currentProxy = proxyOpt.get();
        int maxRetries = Math.min(3, proxies.size());
        int attempts = 0;
        
        while (attempts < maxRetries) {
            attempts++;
            triedProxies.add(currentProxy.getHost() + ":" + currentProxy.getPort());
            
            try {
                LOG.infof("Attempting translation with proxy %s (attempt %d/%d)", currentProxy, attempts, maxRetries);
                
                // Set the current proxy for the base client to use
                setCurrentProxy(currentProxy);
                
                // Execute the operation
                T result = operation.execute();
                
                // If successful, return the result
                LOG.infof("Translation successful with proxy %s", currentProxy);
                return result;
            } catch (Exception e) {
                LOG.warnf("Translation failed with proxy %s: %s", currentProxy, e.getMessage());
                
                // If we have more proxies to try, get the next one
                if (attempts < maxRetries) {
                    // Find a proxy we haven't tried yet
                    Optional<ProxyEntry> nextProxyOpt = proxies.stream()
                        .filter(p -> !triedProxies.contains(p.getHost() + ":" + p.getPort()))
                        .findFirst();
                    
                    if (nextProxyOpt.isPresent()) {
                        currentProxy = nextProxyOpt.get();
                        LOG.infof("Retrying with different proxy: %s", currentProxy);
                    } else {
                        LOG.warn("No more untried proxies available");
                        break;
                    }
                }
            }
        }
        
        // If all proxies failed, try one last time with direct connection
        LOG.warn("All proxy attempts failed, trying direct connection as last resort");
        resetProxy();
        return operation.execute();
    }
    
    /**
     * Sets the current proxy for this client to use.
     * This method uses reflection to replace the OkHttpClient in this client.
     * 
     * @param proxy The proxy to use
     */
    private void setCurrentProxy(ProxyEntry proxy) {
        try {
            // Create a proxy-enabled HTTP client
            OkHttpClient proxyClient = httpClientFactory.createClientWithProxy(proxy);
            
            // Use reflection to set the client field in this client
            java.lang.reflect.Field clientField = AzureOpenAITranslationClient.class.getDeclaredField("client");
            clientField.setAccessible(true);
            clientField.set(this, proxyClient);
            
            LOG.infof("Set proxy %s for client", proxy);
        } catch (Exception e) {
            LOG.errorf("Failed to set proxy for client: %s", e.getMessage());
        }
    }
    
    /**
     * Resets the proxy configuration to use direct connection.
     */
    private void resetProxy() {
        try {
            // Create a default HTTP client without proxy
            OkHttpClient defaultClient = httpClientFactory.createDefaultClient();
            
            // Use reflection to set the client field in this client
            java.lang.reflect.Field clientField = AzureOpenAITranslationClient.class.getDeclaredField("client");
            clientField.setAccessible(true);
            clientField.set(this, defaultClient);
            
            LOG.info("Reset client to use direct connection");
        } catch (Exception e) {
            LOG.errorf("Failed to reset proxy for client: %s", e.getMessage());
        }
    }
    
    /**
     * Functional interface for translation operations.
     */
    @FunctionalInterface
    private interface TranslationOperation<T> {
        T execute();
    }
    
    /**
     * Adds a proxy to the proxy pool.
     * 
     * @param host Proxy host
     * @param port Proxy port
     * @param username Username for authentication (optional)
     * @param password Password for authentication (optional)
     * @param group Group name for categorization
     * @param priority Priority (lower number means higher priority)
     */
    public void addProxy(String host, int port, String username, String password, String group, int priority) {
        proxyConfig.addProxy(host, port, username, password, group, priority);
    }
    
    /**
     * Tests a proxy connection to the Azure OpenAI API.
     * 
     * @param host Proxy host
     * @param port Proxy port
     * @param username Username for authentication (optional)
     * @param password Password for authentication (optional)
     * @return true if the connection is successful, false otherwise
     */
    public boolean testProxyConnection(String host, int port, String username, String password) {
        try {
            // Create a temporary proxy entry
            ProxyEntry proxyEntry = new ProxyEntry(host, port, username, password, "test", 1);
            
            // Create a client with this proxy
            OkHttpClient client = httpClientFactory.createClientWithProxy(proxyEntry);
            
            // Get a client config to test with
            ClientConfig clientConfig = azureConfig.getBestClient();
            
            // Build a simple test request to the Azure OpenAI API
            String testUrl = clientConfig.getEndpoint() + "/openai/deployments?api-version=" + clientConfig.getApiVersion();
            
            Request request = new Request.Builder()
                .url(testUrl)
                .header("api-key", clientConfig.getApiKey())
                .build();
            
            // Execute the request
            try (Response response = client.newCall(request).execute()) {
                boolean success = response.isSuccessful();
                LOG.infof("Proxy test to %s: %s (status: %d)", 
                    testUrl, success ? "SUCCESS" : "FAILED", response.code());
                return success;
            }
        } catch (IOException e) {
            LOG.errorf("Proxy test failed with exception: %s", e.getMessage());
            return false;
        }
    }
}
