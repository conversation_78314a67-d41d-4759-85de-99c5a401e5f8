package org.acme.service.translation;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import jakarta.inject.Inject;
import org.acme.config.AzureOpenAIConfig;
import org.acme.config.AzureOpenAIConfig.ClientConfig;
import org.jboss.logging.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import jakarta.enterprise.context.ApplicationScoped;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * Direct client for Azure OpenAI API for translation purposes.
 * This provides a reliable implementation for Azure OpenAI integration.
 */
@ApplicationScoped
public class AzureOpenAITranslationClient {

    private static final Logger logger = Logger.getLogger(AzureOpenAITranslationClient.class);
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    private static final ObjectMapper mapper = new ObjectMapper();
    private static final org.slf4j.Logger log = LoggerFactory.getLogger(AzureOpenAITranslationClient.class);

    private final OkHttpClient client = new OkHttpClient.Builder()
        .connectTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
        .writeTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
        .readTimeout(120, java.util.concurrent.TimeUnit.SECONDS) // Longer read timeout for large responses
        .build();
        
    // Client configuration and selection
    @Inject
    AzureOpenAIConfig config;
    
    // Track client usage for rotation
    private final AtomicInteger requestCounter = new AtomicInteger(0);
    private ClientConfig currentClient = null;

    // Constants for batch processing
    private static final String BATCH_TEXT_START_DELIMITER_PREFIX = "---TEXT_";
    private static final String BATCH_TEXT_START_DELIMITER_SUFFIX = "_TRANSLATIONS_START---";
    private static final String BATCH_TEXT_END_DELIMITER_PREFIX = "---TEXT_";
    private static final String BATCH_TEXT_END_DELIMITER_SUFFIX = "_TRANSLATIONS_END---";

    public String translateBatch(List<String> textsToTranslate, String sourceLanguage, List<String> targetLanguages,
                                 boolean isSubtitle, boolean isAVContext) {

        if (textsToTranslate == null || textsToTranslate.isEmpty()) {
            logger.error("Texts to translate list cannot be null or empty.");
            return "Translation failed: No source texts specified.";
        }
        if (targetLanguages == null || targetLanguages.isEmpty()) {
            logger.error("Target languages list cannot be null or empty.");
            return "Translation failed: No target languages specified.";
        }

        try {
            List<Map<String, String>> messages = new ArrayList<>();
            Map<String, String> systemMessageMap = new HashMap<>();
            systemMessageMap.put("role", "system");

            String targetLanguagesString = targetLanguages.stream().collect(Collectors.joining(", "));

            // Core Preamble - largely unchanged but implies multiple texts
            String multiLangCorePreamble =
                "You are a top-tier multilingual translation expert, renowned for producing outstanding translations that are not only accurate but also exceptionally natural, idiomatic, and culturally resonant across diverse languages and contexts.\n\n" +
                "Your Task:\n" +
                "For EACH of the [Source Text] entries provided by the user (originally in " + sourceLanguage + "), translate it into EACH of the specified [Target Languages] (" + targetLanguagesString + ").\n\n" +
                "For each target language, for each source text, the translation must strictly adhere to these core principles:\n" +
                "1. Accuracy and Fidelity: Completely and precisely convey the original core meaning, all nuances, overall tone, and deeper intent of the given [Source Text] entry.\n" +
                "2. Naturalness and Idiomaticity: The translation must achieve ultimate fluency and naturalness, perfectly aligning with the everyday idiomatic expressions of a native speaker of that target language. It should read and sound as if originally crafted by a native speaker of that language who has a profound understanding of the [Source Text] entry's specific subject matter, context, and intended audience. [SPECIFIC_GENRE_INSTRUCTION_PLACEHOLDER]\n" +
                "3. Cultural Relevance and Adaptation: Appropriately handle and adapt any culturally specific references, allusions, humor, or potential sensitivities from the original text entry, ensuring the translation is easily understandable, suitable, and resonant for the audience of each target language.\n\n";

            // MODIFIED Output Format Instruction for BATCHES
            StringBuilder outputFormatInstructionBuilder = new StringBuilder(
                "Output Format Requirement:\n" +
                "You MUST process EACH [Source Text] provided by the user sequentially. For EACH [Source Text], you will provide its translations into all [Target Languages].\n" +
                "Clearly demarcate the translations for EACH source text using the following structure:\n\n"
            );
            for (int i = 0; i < textsToTranslate.size(); i++) {
                final int currentIndex = i;
                outputFormatInstructionBuilder.append(BATCH_TEXT_START_DELIMITER_PREFIX).append(currentIndex + 1).append(BATCH_TEXT_START_DELIMITER_SUFFIX).append("\n");
                outputFormatInstructionBuilder.append(
                    targetLanguages.stream()
                        .map(lang -> lang + " - [Translated text for " + lang + " for Source Text " + (currentIndex + 1) + "]")
                        .collect(Collectors.joining("\n"))
                ).append("\n");
                outputFormatInstructionBuilder.append(BATCH_TEXT_END_DELIMITER_PREFIX).append(i + 1).append(BATCH_TEXT_END_DELIMITER_SUFFIX).append("\n");
                if (i < textsToTranslate.size() - 1) {
                    outputFormatInstructionBuilder.append("\n"); // Add a blank line between text blocks for readability
                }
            }

            outputFormatInstructionBuilder.append(
                "\n(Example: If there are 2 source texts and target languages are Chinese, English, the output structure would be:\n" +
                BATCH_TEXT_START_DELIMITER_PREFIX + "1" + BATCH_TEXT_START_DELIMITER_SUFFIX + "\n" +
                "Chinese - [中文翻译文本 for source text 1]\n" +
                "English - [English translated text for source text 1]\n" +
                BATCH_TEXT_END_DELIMITER_PREFIX + "1" + BATCH_TEXT_END_DELIMITER_SUFFIX + "\n\n" +
                BATCH_TEXT_START_DELIMITER_PREFIX + "2" + BATCH_TEXT_START_DELIMITER_SUFFIX + "\n" +
                "Chinese - [中文翻译文本 for source text 2]\n" +
                "English - [English translated text for source text 2]\n" +
                BATCH_TEXT_END_DELIMITER_PREFIX + "2" + BATCH_TEXT_END_DELIMITER_SUFFIX + "\n" +
                ")\n\n"
            );
            String outputFormatInstruction = outputFormatInstructionBuilder.toString();


            String coreFinalSystemInstructions =
                "\nImportant Instructions:\n" +
                "Do NOT add any form of extra comments, personal explanations, introductions, apologies, annotations, or any non-translation text outside of the structured multilingual translation list for each source text.\n" +
                "Strictly return only the structured multilingual translation list in the format specified above for ALL provided source texts.";

            String systemContent;
            String specificGenreInstruction;
            String additionalFormattingInfo;

            // --- Specific Genre Instructions (same as your original code) ---
            if (isSubtitle) {
                logger.warn("Subtitle translation path called, but user focus is on AV metadata. Ensure this path is intended or review prompt for subtitle specifics.");
                additionalFormattingInfo = "Ensure all original subtitle formatting (timestamps, tags, line breaks) is meticulously preserved within each language's translated subtitle block. [Detailed subtitle rules would go here if this path were primary]";
                if (isAVContext) {
                    specificGenreInstruction = "This is an AV subtitle. Translate with appropriate tone, slang, and nuance for adult content in each target language, suitable for subtitles. [Detailed AV subtitle content rules would go here]";
                } else {
                    specificGenreInstruction = "This is a standard subtitle. Ensure clarity and conciseness appropriate for subtitles in each target language.";
                }
            } else { // NOT a subtitle
                additionalFormattingInfo = "If any [Source Text] entry has specific formatting (like Markdown, multiple paragraphs for an intro, significant whitespace), preserve this original structure within the translated text for each language where appropriate for the content type.";

                if (isAVContext) {
                    specificGenreInstruction =
                        "The [Source Text] entries are metadata (e.g., video title, actress name/profile, introduction/description) for Adult Video (AV) content. Your role is not just a translator, but an **expert AV marketing copywriter and SEO specialist** for each target language.\n" +
                        "Your goal is to make the translated metadata for EACH entry **irresistibly enticing, highly clickable, and optimized for search engines** within the AV niche of each target language and culture.\n\n" +
                        "For each target language, EACH translation MUST:\n" +
                        "1. Be **extremely idiomatic, provocative, and 'insider' quality**, using the specific slang, jargon, euphemisms, and suggestive language that resonates deeply with AV consumers in that culture. The tone should be exciting, titillating, and promise an intense, unforgettable experience.\n" +
                        "2. **Maximize allure and click-through potential.** Transform even mundane source descriptions into compelling, can't-miss teasers. Every word should contribute to making the user want to see the content.\n" +
                        "3. **Strategically incorporate relevant SEO keywords and phrases** naturally within the translated text. Think about what terms users would specifically search for to find content featuring these actresses, kinks, scenarios, and general AV themes. Weave in common, high-traffic AV search terms appropriate for the target language and the video's content, without making it sound forced.\n\n" +
                        "Detailed guidelines for AV content types (apply to each relevant source text entry):\n" +
                        "- For **AV Titles**: Create **sensational, viral-potential, and keyword-rich titles**. They must be highly suggestive, immediately highlight the core appeal or unique selling proposition (kink, actress type, scenario), and use impactful, enticing language. Do not be literal; act as a creative AV title writer aiming for maximum engagement. If the original text consists of fragmented features or hashtags (like the example '♯有点阴沉？♯但却是夜总会小姐♯潮吹失禁♯'), your task is to **synthesize these into a coherent, punchy, and highly marketable main title or a series of equally compelling sub-headlines/taglines in the target language** that flow well and are SEO-friendly.\n" +
                        "- For **AV Actress Names/Profiles**: Translate names using established fan transliterations or widely known nicknames in the target language if they exist. If not, create appealing, memorable, and contextually appropriate phonetic renderings or descriptive aliases. Profiles must emphasize their most seductive qualities, unique physical attributes, or character archetypes (e.g., 'innocent girl next door with a hidden wild side') in an alluring and marketable way, using keywords fans might search for.\n" +
                        "- For **AV Introductions/Descriptions**: Rewrite these as **highly engaging, vivid, and seductive plot summaries or teasers**. Amplify the excitement, use evocative language to describe key scenes, kinks, and interactions (using appropriate AV terminology of the target language), and build intense anticipation. Focus on what makes this video a 'must-watch' and differentiate it.\n\n" +
                        "The overall style must be that of **top-tier, persuasive, and SEO-conscious AV marketing copy** specifically crafted for each target language's audience, making them desperate to seek out the content. Discard any bland, overly literal, or uninspired phrasing immediately in favor of vivid, enticing, and keyword-optimized language that sells the fantasy.";
                } else { // General text, not AV, not subtitle
                    specificGenreInstruction =
                        "If any [Source Text] entry possesses a distinct genre or style (e.g., news report, technical manual, casual conversation, formal marketing copy, literary prose, poetry, etc.), you must meticulously replicate that specific style using the most authentic and professional expressions appropriate for that genre in each target language.";
                }
            }
            // --- End of Specific Genre Instructions ---

            String tempSystemContent = multiLangCorePreamble.replace("[SPECIFIC_GENRE_INSTRUCTION_PLACEHOLDER]", specificGenreInstruction);
            systemContent = tempSystemContent + outputFormatInstruction + additionalFormattingInfo + coreFinalSystemInstructions;

            systemMessageMap.put("content", systemContent);
            messages.add(systemMessageMap);

            Map<String, String> userMessage = new HashMap<>();
            userMessage.put("role", "user");

            // Construct user message with all texts
            StringBuilder userMessageContentBuilder = new StringBuilder();
            userMessageContentBuilder.append(String.format(
                "Translate EACH of the following [Source Text] entries from their original language '%s' into the listed [Target Languages]. " +
                "Adhere strictly to all system instructions regarding accuracy, idiomatic style for the specific content type (especially making it enticing and SEO-friendly for AV content if applicable), cultural relevance, and the batch output formatting for each language and each text.\n\n" +
                "[Target Languages]: %s\n\n" +
                "Source Texts to Translate:\n",
                sourceLanguage, targetLanguagesString
            ));

            for (int j = 0; j < textsToTranslate.size(); j++) {
                userMessageContentBuilder.append(String.format("%d. [Source Text %d]: %s\n", j + 1, j + 1, textsToTranslate.get(j)));
            }
            userMessage.put("content", userMessageContentBuilder.toString());
            messages.add(userMessage);

            ObjectNode requestBody = mapper.createObjectNode();
            ArrayNode messagesNode = requestBody.putArray("messages");
            for (Map<String, String> message : messages) {
                ObjectNode messageNode = messagesNode.addObject();
                message.forEach(messageNode::put);
            }

            // Consider increasing max_tokens if the combined length of translations for all texts might exceed the default
            // The default max_tokens for gpt-4 models is typically 4096 for the *completion*.
            // If 10 texts * N languages * avg_translation_length is large, this might need adjustment.
            // For now, 4090 is kept, but monitor if translations get truncated.
            requestBody.put("max_tokens", 4090);
            requestBody.put("temperature", 0.3);

            String url = String.format("%s/openai/deployments/%s/chat/completions?api-version=%s",
                    endpoint, deploymentName, apiVersion);

            logger.infof("Sending BATCH multi-language translation request. Number of texts: {}. Target languages: {}. isAVContext: {}, isSubtitle: {}",
                         textsToTranslate.size(), targetLanguagesString, isAVContext, isSubtitle);
            // logger.debug("System Prompt for BATCH: {}", systemContent); // Extremely long

            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("api-key", apiKey)
                    .post(RequestBody.create(requestBody.toString(), JSON))
                    .build();

            try (Response response = client.newCall(request).execute()) {
                String responseBodyString = response.body() != null ? response.body().string() : "No response body";

                if (!response.isSuccessful()) {
                    logger.errorf("BATCH Multi-language translation API request failed with code {}: {}",
                            response.code(), responseBodyString);
                    return "Translation failed with error code: " + response.code() + ", details: " + responseBodyString;
                }

                JsonNode rootNode = mapper.readTree(responseBodyString);
                if (rootNode.has("choices") && rootNode.get("choices").isArray() && !rootNode.get("choices").isEmpty()) {
                    JsonNode firstChoice = rootNode.get("choices").get(0);
                    if (firstChoice.has("message") && firstChoice.get("message").has("content")) {
                        return firstChoice.get("message").get("content").asText();
                    }
                }

                logger.errorf("Unexpected response format from BATCH multi-language translation: {}", responseBodyString);
                return "Translation failed: Unexpected response format";
            }

        } catch (IOException e) {
            logger.error("Error during BATCH multi-language translation request", e);
            return "Translation failed: " + e.getMessage();
        }
    }

    /**
     * Parses the batch translation response string.
     *
     * @param rawResponse The raw string response from the translation API.
     * @param numTextsExpected The number of original texts sent for translation.
     * @return A list of maps. Each map corresponds to an original text,
     * mapping target language codes to their translated text.
     * Returns an empty list if parsing fails or the format is incorrect.
     */
    public List<Map<String, String>> parseBatchTranslationResponse(String rawResponse, int numTextsExpected) {
        log.info("------ raw: " + rawResponse);
        List<Map<String, String>> allTextsTranslations = new ArrayList<>();
        if (rawResponse == null || rawResponse.trim().isEmpty()) {
            logger.error("Raw response for batch translation is null or empty.");
            return allTextsTranslations;
        }

        // Pattern to capture each text block
        // It looks for ---TEXT_(\d+)_TRANSLATIONS_START---(.*?)---TEXT_(\d+)_TRANSLATIONS_END---
        // The DOTALL flag (s) allows . to match newlines
        Pattern textBlockPattern = Pattern.compile(
            Pattern.quote(BATCH_TEXT_START_DELIMITER_PREFIX) + "(\\d+)" + Pattern.quote(BATCH_TEXT_START_DELIMITER_SUFFIX) +
            "(.*?)" +
            Pattern.quote(BATCH_TEXT_END_DELIMITER_PREFIX) + "\\1" + Pattern.quote(BATCH_TEXT_END_DELIMITER_SUFFIX),
            Pattern.DOTALL
        );
        Matcher textBlockMatcher = textBlockPattern.matcher(rawResponse);

        // Temporary list to hold found blocks before sorting by index
        List<Map.Entry<Integer, Map<String, String>>> tempTranslations = new ArrayList<>();

        while (textBlockMatcher.find()) {
            try {
                int textIndex = Integer.parseInt(textBlockMatcher.group(1)); // Text number (1-based)
                String translationsForOneTextBlock = textBlockMatcher.group(2).trim();

                Map<String, String> currentTextTranslations = new HashMap<>();
                String[] lines = translationsForOneTextBlock.split("\\r?\\n");
                for (String line : lines) {
                    if (line.trim().isEmpty()) continue;
                    int hyphenIndex = line.indexOf(" - ");
                    if (hyphenIndex > 0) {
                        String lang = line.substring(0, hyphenIndex).trim();
                        String translation = line.substring(hyphenIndex + 3).trim();
                        currentTextTranslations.put(lang, translation);
                    } else {
                        logger.warnf("Could not parse line in translation block for text index {}: '{}'", textIndex, line);
                    }
                }
                if (!currentTextTranslations.isEmpty()) {
                    tempTranslations.add(new HashMap.SimpleEntry<>(textIndex, currentTextTranslations));
                }
            } catch (NumberFormatException e) {
                logger.error("Error parsing text index from delimiter: " + textBlockMatcher.group(0), e);
            } catch (Exception e) {
                logger.error("Error parsing translation block: " + textBlockMatcher.group(0), e);
            }
        }

        // Sort by text index to ensure original order
        tempTranslations.sort(Map.Entry.comparingByKey());

        for(Map.Entry<Integer, Map<String, String>> entry : tempTranslations) {
            allTextsTranslations.add(entry.getValue());
        }


        if (allTextsTranslations.size() != numTextsExpected) {
            logger.warnf("Warning: Expected translations for {} texts, but parsed {}. The LLM might not have followed the format perfectly for all texts. Raw response:\n{}",
                        numTextsExpected, allTextsTranslations.size(), rawResponse.substring(0, Math.min(rawResponse.length(), 1000))); // Log first 1000 chars
        }

        return allTextsTranslations;
    }


    /**
     * Translates text using Azure OpenAI directly
     *
     * @param text Text to translate
     * @param sourceLanguage Source language
     * @param targetLanguage Target language
     * @param isSubtitle Whether this is a subtitle translation (requiring format preservation)
     * @param isAVSubtitle Whether this is an adult video subtitle translation (requiring specialized handling)
     * @return Translated text
     */
    public String translate(String text, String sourceLanguage, String targetLanguage, boolean isSubtitle, boolean isAVSubtitle) {
        try {
            // Build system and user messages
            List<Map<String, String>> messages = new ArrayList<>();

            // System message - instructions for the model
            Map<String, String> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");

            if (isSubtitle) {
                if (isAVSubtitle) {
                    // Specialized system message for adult video subtitles
                    systemMessage.put("content",
                        "You are a professional subtitle translator, specializing in colloquial " + sourceLanguage + " and adult-oriented content. " +
                        "Your primary task is to translate the " + sourceLanguage + " subtitle text from adult videos into " + targetLanguage + ". " +

                        // Strict Formatting Constraints
                        "You MUST strictly maintain the original subtitle format. This includes: " +
                        "1. DO NOT alter any timestamps, subtitle sequence numbers, or other technical formatting cues. " +
                        "2. If the original subtitles contain formatting tags (e.g., <i>italics</i>, <b>bold</b>, <font color=\"#FFFF00\">color tags</font>, positioning tags), replicate these tags around the correspondingly translated text. " +
                        "3. Translate ONLY the actual textual content of the subtitles. " +
                        "4. Preserve the original line breaks and the distribution of text across lines precisely as they appear in the source. Each translated line must correspond to an original line. " +

                        // Content-Specific Translation Requirements for AV
                        "For this adult video content, it is crucial that your translation accurately captures and conveys: " +
                        "A. The original nuance, subtlety, and any suggestive or double meanings in the dialogue. " +
                        "B. The emotional tone (e.g., seductive, playful, demanding, surprised, expressions of pleasure or pain, including moans or other character sounds if they are transcribed as text within the subtitle). " +
                        "C. Any specific slang, colloquialisms, or idiomatic expressions common in adult entertainment contexts, translating them into natural-sounding equivalents in " + targetLanguage + ". " +

                        // Output Style and Fidelity
                        "The " + targetLanguage + " translation must sound natural, befitting the scene's context, and reflect how the dialogue or transcribed sounds would be understood by a native " + targetLanguage + " speaker familiar with such content. " +
                        "Maintain utmost fidelity to the source text's intended meaning and impact. " +

                        // Final Instruction
                        "Return ONLY the translated text for each subtitle segment. Do not add any explanations, notes, headers, or any text whatsoever beyond the direct translation of the subtitle content itself, fitting within the original formatting structure."
                    );
                } else {
                    // Standard subtitle translation system message
                    systemMessage.put("content",
                        "You are a professional subtitle translator. " +
                        "Your task is to translate subtitle text while strictly maintaining the original format. " +
                        "Do not change any timestamps, subtitle numbers, or formatting. " +
                        "Translate only the actual text content. " +
                        "Maintain the same line breaks and text distribution as in the original."
                    );
                }
            } else {
                systemMessage.put("content",
                    "You are a professional translator with deep expertise in multiple languages and a keen understanding of their cultural nuances and idiomatic expressions. " +
                    "Your task is to translate the provided text. Beyond mere accuracy, the translation must:" +
                    "1.  Be **exceptionally natural, fluent, and perfectly idiomatic** for the specific context, subject matter, and intended audience of the original text. It should read as if originally written by a native speaker of the target language who is an expert in that subject matter or deeply familiar with that context." +
                    "2.  Maintain **the highest fidelity** to the original meaning, tone, and intent." +
                    "3.  Preserve the **original formatting** meticulously." +
                    "Do not add any explanations, apologies, or notes—return only the translated text."
                );
            }
            messages.add(systemMessage);

            // User message - the text to translate
            Map<String, String> userMessage = new HashMap<>();
            userMessage.put("role", "user");

            if (isSubtitle) {
                userMessage.put("content",
                    "Here is a subtitle file section in " + sourceLanguage + " that I need translated to " +
                    targetLanguage + ". Translate only the text, keeping all numbers, timestamps, and formatting intact:\n\n" +
                    text
                );
            } else {
                userMessage.put("content",
                    "Translate the following text from " + sourceLanguage + " to " + targetLanguage + ":\n\n" +
                    text
                );
            }
            messages.add(userMessage);

            // Create the request body
            ObjectNode requestBody = mapper.createObjectNode();
            ArrayNode messagesNode = requestBody.putArray("messages");

            for (Map<String, String> message : messages) {
                ObjectNode messageNode = messagesNode.addObject();
                message.forEach(messageNode::put);
            }

            requestBody.put("max_tokens", 4000);
            requestBody.put("temperature", 0.1);

            // Build URL for Azure OpenAI
            String url = String.format("%sopenai/deployments/%s/chat/completions?api-version=%s",
                    endpoint, deploymentName, apiVersion);

            logger.infof("Sending translation request to: %s", url);

            // Create and execute request
            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("api-key", apiKey)
                    .post(RequestBody.create(requestBody.toString(), JSON))
                    .build();

            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String responseBody = response.body() != null ? response.body().string() : "No response body";
                    logger.errorf("Translation API request failed with code %d: %s",
                            response.code(), responseBody);
                    return "Translation failed with error code: " + response.code() + ", details: " + responseBody;
                }

                String responseBody = response.body().string();
                JsonNode rootNode = mapper.readTree(responseBody);

                // Extract the response content
                if (rootNode.has("choices") && rootNode.get("choices").isArray() && rootNode.get("choices").size() > 0) {
                    JsonNode firstChoice = rootNode.get("choices").get(0);
                    if (firstChoice.has("message") && firstChoice.get("message").has("content")) {
                        return firstChoice.get("message").get("content").asText();
                    }
                }

                logger.error("Unexpected response format: " + responseBody);
                return "Translation failed: Unexpected response format";
            }

        } catch (IOException e) {
            logger.error("Error during translation request", e);
            return "Translation failed: " + e.getMessage();
        }
    }



    /**
     * Translates subtitle text preserving formatting
     */
    public String translateSubtitle(String text, String sourceLanguage, String targetLanguage) {
        // Handle large subtitle files by chunking if needed
        if (text.length() > 4000) {
            return translateLargeSubtitle(text, sourceLanguage, targetLanguage);
        }
        return translate(text, sourceLanguage, targetLanguage, true, true);
    }

    // translate av text
    public String translateAvText(String text, String sourceLanguage, String targetLanguage) {

            // Build system and user messages
            List<Map<String, String>> messages = new ArrayList<>();

            // System message - instructions for the model
            Map<String, String> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");

            systemMessage.put("content",
                    "You are a professional translator with expertise in multiple languages. You are a professional adult video konwledge. " +
                    "Translate the text with accuracy while preserving the original formatting. " +
                    "Do not add any explanations or notes - return only the translated text."
                );
            messages.add(systemMessage);

            // User message - the text to translate
            Map<String, String> userMessage = new HashMap<>();
            userMessage.put("role", "user");

            userMessage.put("content",
                    "Translate the following text from " + sourceLanguage + " to " + targetLanguage + ":\n\n" +
                    text
                );
            messages.add(userMessage);

            // Create the request body
            ObjectNode requestBody = mapper.createObjectNode();
            ArrayNode messagesNode = requestBody.putArray("messages");

            for (Map<String, String> message : messages) {
                ObjectNode messageNode = messagesNode.addObject();
                message.forEach(messageNode::put);
            }

            requestBody.put("max_tokens", 4000);
            requestBody.put("temperature", 0.1);

            // Build URL for Azure OpenAI
            String url = String.format("%sopenai/deployments/%s/chat/completions?api-version=%s",
                    endpoint, deploymentName, apiVersion);

            logger.infof("Sending translation request to: %s", url);

            // Create and execute request
            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("api-key", apiKey)
                    .post(RequestBody.create(requestBody.toString(), JSON))
                    .build();

            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String responseBody = response.body() != null ? response.body().string() : "No response body";
                    logger.errorf("Translation API request failed with code %d: %s",
                            response.code(), responseBody);
                    return "Translation failed with error code: " + response.code() + ", details: " + responseBody;
                }

                String responseBody = response.body().string();
                JsonNode rootNode = mapper.readTree(responseBody);

                // Extract the response content
                if (rootNode.has("choices") && rootNode.get("choices").isArray() && rootNode.get("choices").size() > 0) {
                    JsonNode firstChoice = rootNode.get("choices").get(0);
                    if (firstChoice.has("message") && firstChoice.get("message").has("content")) {
                        return firstChoice.get("message").get("content").asText();
                    }
                }

                logger.error("Unexpected response format: " + responseBody);
                return "Translation failed: Unexpected response format";
            } catch (IOException e) {
                logger.error("Error during translation request", e);
                return "Translation failed: " + e.getMessage();
            }
    }


    public String translate(String text, String sourceLanguage, List<String> targetLanguages,
                            boolean isSubtitle, boolean isAVContext) {

        if (targetLanguages == null || targetLanguages.isEmpty()) {
            logger.error("Target languages list cannot be null or empty.");
            return "Translation failed: No target languages specified.";
        }

        try {
            List<Map<String, String>> messages = new ArrayList<>();
            Map<String, String> systemMessageMap = new HashMap<>();
            systemMessageMap.put("role", "system");

            String targetLanguagesString = targetLanguages.stream().collect(Collectors.joining(", "));

            String multiLangCorePreamble =
                "You are a top-tier multilingual translation expert, renowned for producing outstanding translations that are not only accurate but also exceptionally natural, idiomatic, and culturally resonant across diverse languages and contexts.\n\n" +
                "Your Task:\n" +
                "Translate the [Source Text] (provided by the user, originally in " + sourceLanguage + ") into each of the specified [Target Languages] (" + targetLanguagesString + ").\n\n" +
                "For each target language, the translation must strictly adhere to these core principles:\n" +
                "1. Accuracy and Fidelity: Completely and precisely convey the original core meaning, all nuances, overall tone, and deeper intent of the [Source Text].\n" +
                "2. Naturalness and Idiomaticity: The translation must achieve ultimate fluency and naturalness, perfectly aligning with the everyday idiomatic expressions of a native speaker of that target language. It should read and sound as if originally crafted by a native speaker of that language who has a profound understanding of the [Source Text]'s specific subject matter, context, and intended audience. [SPECIFIC_GENRE_INSTRUCTION_PLACEHOLDER]\n" +
                "3. Cultural Relevance and Adaptation: Appropriately handle and adapt any culturally specific references, allusions, humor, or potential sensitivities from the original text, ensuring the translation is easily understandable, suitable, and resonant for the audience of each target language.\n\n";

            String outputFormatInstruction =
                "Output Format Requirement:\n" +
                "You MUST strictly adhere to the following format to clearly present all translation results, with each language's translation on a new line. Each line must start with the language name (exactly as provided in the target languages list) followed by a hyphen and a space, then the translated text for that language:\n\n" +
                targetLanguages.stream().map(lang -> lang + " - [Translated text for " + lang + "]").collect(Collectors.joining("\n")) + "\n" +
                "(Example: If target languages are Chinese, English, then output should be like:\nChinese - [中文翻译文本]\nEnglish - [English translated text])\n\n";

            String coreFinalSystemInstructions =
                "\nImportant Instructions:\n" +
                "Do NOT add any form of extra comments, personal explanations, introductions, apologies, annotations, or any non-translation text outside of the translation results.\n" +
                "Strictly return only the structured multilingual translation list in the format specified above.";

            String systemContent;
            String specificGenreInstruction;
            String additionalFormattingInfo;

            if (isSubtitle) {
                logger.warn("Subtitle translation path called, but user focus is on AV metadata. Ensure this path is intended or review prompt for subtitle specifics.");
                additionalFormattingInfo = "Ensure all original subtitle formatting (timestamps, tags, line breaks) is meticulously preserved within each language's translated subtitle block. [Detailed subtitle rules would go here if this path were primary]";
                if (isAVContext) {
                    specificGenreInstruction = "This is an AV subtitle. Translate with appropriate tone, slang, and nuance for adult content in each target language, suitable for subtitles. [Detailed AV subtitle content rules would go here]";
                } else {
                    specificGenreInstruction = "This is a standard subtitle. Ensure clarity and conciseness appropriate for subtitles in each target language.";
                }
            } else { // NOT a subtitle - User's primary focus (e.g., AV Title, Actress, Intro)
                additionalFormattingInfo = "If the [Source Text] has specific formatting (like Markdown, multiple paragraphs for an intro, significant whitespace), preserve this original structure within the translated text for each language where appropriate for the content type.";

                if (isAVContext) { // AV Metadata (Title, Actress, Intro) - REFINED PROMPT
                    specificGenreInstruction =
                        "You are not merely a translator; you are a **master AV marketing wordsmith and SEO strategist**, specifically for the [Target Language] AV market.\n" +
                        "The [Source Text] is metadata (e.g., video title, actress name/profile, introduction/description) for Adult Video (AV) content.\n" +
                        "Your mission: to transform this metadata into **explosively enticing, supremely clickable, and highly discoverable (SEO-maximized) marketing copy** tailored for AV consumers within the [Target Language] culture.\n\n" +
                        "For the [Target Language], the translation MUST:\n" +
                        "1. **Channel the authentic voice of a native AV connoisseur**: The translation MUST be **hyper-idiomatic, intensely provocative, and possess an 'insider' authenticity**. Employ the precise slang, niche jargon, culturally-specific euphemisms, and powerfully suggestive language that *electrifies* AV consumers in the [Target Language] market. The tone must be **palpably exciting, deeply titillating, and promise an unparalleled, unforgettable encounter**.\n" +
                        "2. **Engineer Maximum Click-Through Rate (CTR) and Desire**: Every syllable must **ooze allure**. Transform even the most vanilla source text into an **unmissable, magnetic teaser**. Your language must ignite an *urgent need* in the user to click and consume.\n" +
                        "3. **Embed High-Impact SEO Keywords Organically**: Seamlessly weave in the most potent, high-traffic AV search terms (for actresses, kinks, scenarios, themes) that [Target Language] users *actually type into search bars*. This keyword integration must feel completely natural, enhancing the provocative copy, not disrupting it. Think discoverability *through* desire.\n\n" +
                        "Detailed guidelines for AV content types in [Target Language]:\n" +
                        "- For **AV Titles**: Craft **explosively viral, keyword-saturated, and utterly irresistible titles**. They must be intensely suggestive, instantly spotlighting the core kink, actress archetype, or unique scenario (USP). Use language that *commands* attention and sparks immediate curiosity. **Abandon literalism; embody a top-tier AV title guru focused on maximum clicks and conversions.** For fragmented inputs (like '♯有点阴沉？♯但却是夜总会小姐♯潮吹失禁♯'), you MUST forge them into a **singular, electrifying main title OR a series of hard-hitting, SEO-optimized sub-headlines/taglines** in the [Target Language] that are both cohesive and magnetically marketable.\n" +
                        "- For **AV Actress Names/Profiles**: Use **established fan transliterations or iconic nicknames** in the [Target Language] where they exist. Otherwise, coin **alluring, unforgettable, and culturally resonant phonetic renderings or descriptive pseudonyms**. Profiles MUST be potent marketing blurbs, spotlighting their most erotic qualities, signature physical attributes, or character archetypes (e.g., 'innocent debutante with a shocking secret lust') in a way that's both intensely seductive and keyword-optimized for fan searches.\n" +
                        "- For **AV Introductions/Descriptions**: Architect these into **gripping, vividly explicit, and overwhelmingly seductive narrative teasers or plot synopses**. Escalate the erotic tension, deploy rich, evocative language (using precise [Target Language] AV terminology) to paint tantalizing pictures of key scenes, kinks, and interactions. Build unbearable anticipation. Pinpoint and amplify the video’s 'must-see' factor and its unique selling proposition, making it utterly distinct and irresistible.\n\n" +
                        "Your output MUST embody **elite, hyper-persuasive, SEO-driven AV ad copy**, meticulously engineered for the [Target Language] audience, compelling them to desperately seek out the content. **Ruthlessly eliminate any bland, literal, or lackluster phrasing.** Every word must be a weapon of seduction and conversion, selling the fantasy with maximum impact.";
                } else { // General text, not AV, not subtitle
                    specificGenreInstruction =
                        "If the [Source Text] possesses a distinct genre or style (e.g., news report, technical manual, casual conversation, formal marketing copy, literary prose, poetry, etc.), you must meticulously replicate that specific style using the most authentic and professional expressions appropriate for that genre in each target language.";
                }
            }

            String tempSystemContent = multiLangCorePreamble.replace("[SPECIFIC_GENRE_INSTRUCTION_PLACEHOLDER]", specificGenreInstruction);
            systemContent = tempSystemContent + outputFormatInstruction + additionalFormattingInfo + coreFinalSystemInstructions;

            systemMessageMap.put("content", systemContent);
            messages.add(systemMessageMap);

            Map<String, String> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            String userMessageContent = String.format(
                "Translate the following [Source Text] from its original language '%s' into the listed [Target Languages]. " +
                "Adhere strictly to all system instructions regarding accuracy, idiomatic style for the specific content type (especially making it enticing and SEO-friendly for AV content if applicable), cultural relevance, and output formatting for each language.\n\n" +
                "[Target Languages]: %s\n\n" +
                "[Source Text]:\n%s",
                sourceLanguage, targetLanguagesString, text
            );
            userMessage.put("content", userMessageContent);
            messages.add(userMessage);

            ObjectNode requestBody = mapper.createObjectNode();
            ArrayNode messagesNode = requestBody.putArray("messages");
            for (Map<String, String> message : messages) {
                ObjectNode messageNode = messagesNode.addObject();
                message.forEach(messageNode::put);
            }

            requestBody.put("max_tokens", 4090);
            requestBody.put("temperature", 0.3); // Increased temperature slightly for more "creative" and enticing marketing copy. Test between 0.2-0.5.

            String url = String.format("%s/openai/deployments/%s/chat/completions?api-version=%s",
                    endpoint, deploymentName, apiVersion);

            logger.infof("Sending multi-language translation request to: {}. Target languages: {}. isAVContext: {}, isSubtitle: {}", url, targetLanguagesString, isAVContext, isSubtitle);
            // logger.debug("System Prompt: {}", systemContent); // Extremely long, use with caution

            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("api-key", apiKey)
                    .post(RequestBody.create(requestBody.toString(), JSON))
                    .build();

            try (Response response = client.newCall(request).execute()) {
                String responseBodyString = response.body() != null ? response.body().string() : "No response body";

                if (!response.isSuccessful()) {
                    logger.errorf("Multi-language translation API request failed with code {}: {}",
                            response.code(), responseBodyString);
                    return "Translation failed with error code: " + response.code() + ", details: " + responseBodyString;
                }

                JsonNode rootNode = mapper.readTree(responseBodyString);
                if (rootNode.has("choices") && rootNode.get("choices").isArray() && !rootNode.get("choices").isEmpty()) {
                    JsonNode firstChoice = rootNode.get("choices").get(0);
                    if (firstChoice.has("message") && firstChoice.get("message").has("content")) {
                        return firstChoice.get("message").get("content").asText();
                    }
                }

                logger.errorf("Unexpected response format from multi-language translation: {}", responseBodyString);
                return "Translation failed: Unexpected response format";
            }

        } catch (IOException e) {
            logger.error("Error during multi-language translation request", e);
            return "Translation failed: " + e.getMessage();
        }
    }


    /**
     * Translates large subtitle files by breaking them into chunks
     * based on subtitle entries, then combining the results
     */
    private String translateLargeSubtitle(String text, String sourceLanguage, String targetLanguage) {
        logger.info("Breaking large subtitle into chunks for translation - Total length: " + text.length());

        // Use a more reliable pattern to split subtitle entries
        // This regex looks for number patterns that typically start subtitle entries
        String[] entries = text.split("(\\n|^)(\\d+)(\\n|$)");

        // Filter out empty entries
        List<String> validEntries = new ArrayList<>();
        for (String entry : entries) {
            if (entry != null && !entry.trim().isEmpty()) {
                validEntries.add(entry.trim());
            }
        }

        logger.infof("Found %d subtitle entries to translate", validEntries.size());
        StringBuilder result = new StringBuilder();

        // Start with smaller chunks for more reliable translation
        int entriesPerChunk = 5;
        int maxChunkSize = 2000; // characters

        for (int i = 0; i < validEntries.size();) {
            StringBuilder chunk = new StringBuilder();
            int startIndex = i;
            int entryCount = 0;

            // Build a chunk that doesn't exceed maxChunkSize
            while (i < validEntries.size() &&
                   chunk.length() < maxChunkSize &&
                   entryCount < entriesPerChunk) {

                // Add the subtitle entry number
                chunk.append(i + 1).append("\n");

                // Add the subtitle content
                chunk.append(validEntries.get(i)).append("\n\n");

                i++;
                entryCount++;
            }

            // If we created a valid chunk
            if (chunk.length() > 0) {
                logger.infof("Translating subtitle chunk %d to %d (of %d) - Size: %d chars",
                         startIndex + 1, i, validEntries.size(), chunk.length());

                // Translate this chunk
                try {
                    String translatedChunk = translate(chunk.toString(), sourceLanguage, targetLanguage, true, true);

                    // Check if translation failed
                    if (translatedChunk.startsWith("Translation failed")) {
                        logger.warn("Translation failed for chunk. Reducing chunk size and retrying.");

                        // If a chunk fails, retry with a smaller size
                        if (entriesPerChunk > 1) {
                            entriesPerChunk = Math.max(1, entriesPerChunk / 2);
                            maxChunkSize = Math.max(500, maxChunkSize / 2);
                            i = startIndex; // Retry from the beginning of this chunk
                            logger.infof("Reduced chunk size to %d entries, max %d chars", entriesPerChunk, maxChunkSize);
                        } else {
                            // If we're already at minimum size and still failing
                            logger.error("Failed to translate even with minimum chunk size");
                            return translatedChunk;
                        }
                    } else {
                        // Successfully translated this chunk
                        result.append(translatedChunk);
                        logger.infof("Successfully translated chunk %d-%d", startIndex + 1, i);
                    }
                } catch (Exception e) {
                    logger.error("Exception during translation: " + e.getMessage(), e);
                    return "Translation failed: " + e.getMessage();
                }
            }
        }

        return result.toString();
    }

    /**
     * Translates normal text
     */
    public String translateText(String text, String sourceLanguage, String targetLanguage) {
        return translate(text, sourceLanguage, targetLanguage, false, true);
    }
    
    /**
     * Select a client for the current request based on priority and rotation
     * @return Selected client configuration
     */
    private synchronized ClientConfig selectClient() {
        // Increment request counter for rotation
        int requestNum = requestCounter.incrementAndGet();
        
        // Get all available clients
        List<ClientConfig> clients = config.getAllClients();
        
        // If we only have one client, return it
        if (clients.size() == 1) {
            currentClient = clients.get(0);
            return currentClient;
        }
        
        // For now, use a simple round-robin selection among clients with the highest priority
        int index = (requestNum - 1) % clients.size();
        currentClient = clients.get(index);
        
        logger.infof("Selected client %s (group: %s, priority: %d) for request %d", 
            currentClient.getId(), currentClient.getGroup(), currentClient.getPriority(), requestNum);
        
        return currentClient;
    }
    
    /**
     * Get the currently selected client
     * @return Current client configuration
     */
    private ClientConfig getCurrentClient() {
        if (currentClient == null) {
            return selectClient();
        }
        return currentClient;
    }
    
    /**
     * Get a client by group name
     * @param group Group name
     * @return Selected client configuration
     */
    public ClientConfig getClientByGroup(String group) {
        return config.getClientByGroup(group)
            .orElseGet(() -> config.getBestClient());
    }
}
