package org.acme.service.translation;

import org.acme.config.ProxyConfig;
import org.jboss.logging.Logger;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.enterprise.event.Observes;
import io.quarkus.runtime.StartupEvent;

/**
 * Monitors proxy availability and logs status on application startup.
 */
@ApplicationScoped
public class AzureOpenAIClientFactory {
    private static final Logger LOG = Logger.getLogger(AzureOpenAIClientFactory.class);
    
    @Inject
    ProxyConfig proxyConfig;
    
    /**
     * Log proxy availability when the application starts.
     */
    void onStart(@Observes StartupEvent ev) {
        // Check if proxy pool is configured and has proxies
        if (proxyConfig != null && proxyConfig.size() > 0) {
            LOG.infof("Proxy support is available for AzureOpenAITranslationClient with %d proxies", 
                proxyConfig.size());
        } else {
            LOG.info("No proxies available, using direct AzureOpenAITranslationClient");
        }
    }
}
