package org.acme.service.translation;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.context.control.ActivateRequestContext;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import org.acme.entity.MovieInfo;
import org.acme.entity.MovieInfoTranslation;
import org.acme.entity.TranslationTask;
import org.acme.entity.TranslationTask.ContentType;
import org.acme.enums.AzureAILanguageCode;
import org.jboss.logging.Logger;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 基于任务表的翻译服务实现
 * 通过任务记录驱动翻译流程，提供更清晰的状态管理和追踪
 */
@ApplicationScoped
public class TaskBasedTranslationService {

    private static final Logger LOG = Logger.getLogger(TaskBasedTranslationService.class);
    
    private static final Pattern LANGUAGE_LABEL_PATTERN = Pattern.compile("\\b[A-Z][a-z]+ - ");
    
    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(
        5,    // 核心线程数
        10,   // 最大线程数
        60L,  // 空闲线程超时时间
        TimeUnit.SECONDS, 
        new ArrayBlockingQueue<>(100000) // 队列容量
    );
    
    @Inject
    TranslationTaskManager taskManager;
    
    @Inject
    AzureOpenAITranslationClient translationClient;
    
    @Inject
    TaskBasedTranslationService self; // 用于事务处理
    
    /**
     * 触发翻译处理
     * 
     * @param batchSize 批次大小
     */
    public void triggerTranslation(int batchSize) {
        LOG.info("开始处理翻译任务...");
        
        try {
            // 1. 检查并处理超时任务
            int timeoutTasks = taskManager.processTimeoutTasks(10); // 10分钟超时
            if (timeoutTasks > 0) {
                LOG.infof("处理了 %d 个超时任务", timeoutTasks);
            }
            
            // 2. 创建所有新内容的翻译任务
            int newTasks = taskManager.createTasksForAllPendingContent(batchSize);
            LOG.infof("为新内容创建了 %d 个翻译任务", newTasks);
            
            // 3. 重置可重试的失败任务
            int resetTasks = taskManager.resetFailedTasks(3); // 最多重试3次
            if (resetTasks > 0) {
                LOG.infof("重置了 %d 个失败任务以便重试", resetTasks);
            }
            
            // 4. 处理待翻译的标题任务
            List<TranslationTask> titleTasks = taskManager.getPendingTasksByType(ContentType.TITLE, batchSize);
            if (!titleTasks.isEmpty()) {
                LOG.infof("开始处理 %d 个标题翻译任务", titleTasks.size());
                processTranslationTasks(titleTasks);
            }
            
            // 5. 处理待翻译的描述任务
            List<TranslationTask> descTasks = taskManager.getPendingTasksByType(ContentType.DESCRIPTION, batchSize);
            if (!descTasks.isEmpty()) {
                LOG.infof("开始处理 %d 个描述翻译任务", descTasks.size());
                processTranslationTasks(descTasks);
            }
            
            // 6. 打印总体统计信息
            Map<String, Long> stats = taskManager.getTaskStatistics();
            LOG.info("翻译任务统计: " + stats);
            
        } catch (Exception e) {
            LOG.error("翻译任务处理出错", e);
        }
    }
    
    /**
     * 处理一组翻译任务
     * 
     * @param tasks 待处理的任务列表
     */
    private void processTranslationTasks(List<TranslationTask> tasks) {
        if (tasks.isEmpty()) {
            return;
        }
        
        // 按照contentId分组，获取要翻译的原始内容
        Map<String, List<TranslationTask>> tasksByContent = tasks.stream()
            .collect(Collectors.groupingBy(task -> task.contentId));
            
        // 处理每组内容
        for (Map.Entry<String, List<TranslationTask>> entry : tasksByContent.entrySet()) {
            String contentId = entry.getKey();
            List<TranslationTask> contentTasks = entry.getValue();
            
            // 获取原始内容
            MovieInfo movie = MovieInfo.find("code", contentId).firstResult();
            if (movie == null) {
                LOG.warnf("找不到内容ID: %s 的原始数据", contentId);
                continue;
            }
            
            // 按内容类型分组
            Map<String, List<TranslationTask>> tasksByType = contentTasks.stream()
                .collect(Collectors.groupingBy(task -> task.contentType)); // 直接使用字符串类型
                
            // 处理标题翻译任务
            if (tasksByType.containsKey("TITLE") && movie.title != null && !movie.title.trim().isEmpty()) {
                processContentTypeTask(movie, movie.title, tasksByType.get("TITLE"), "TITLE");
            }
            
            // 处理描述翻译任务
            if (tasksByType.containsKey("DESCRIPTION") && movie.description != null && !movie.description.trim().isEmpty()) {
                processContentTypeTask(movie, movie.description, tasksByType.get("DESCRIPTION"), "DESCRIPTION");
            }
        }
    }
    
    /**
     * 处理特定内容类型的翻译任务
     * 
     * @param movie 电影信息
     * @param content 要翻译的内容
     * @param tasks 任务列表
     * @param contentType 内容类型
     */
    private void processContentTypeTask(MovieInfo movie, String content, List<TranslationTask> tasks, String contentType) {
        if (content == null || content.trim().isEmpty()) {
            LOG.warnf("内容为空，跳过翻译: %s, %s", movie.code, contentType);
            return;
        }
        
        // 获取要翻译的目标语言列表
        List<String> targetLanguages = tasks.stream()
                .map(task -> task.targetLanguage)
                .distinct()
                .collect(Collectors.toList());
        
        if (!targetLanguages.isEmpty()) {
            // 根据内容长度动态调整语言批次大小
            int contentLength = content.length();
            List<List<String>> languageBatches = splitLanguagesIntoBatches(targetLanguages, contentLength);
            
            LOG.infof("内容长度为 %d 个字符，将 %d 种语言拆分为 %d 个批次进行翻译", 
                    contentLength, targetLanguages.size(), languageBatches.size());
            
            // 逐批次处理翻译
            for (List<String> batchLanguages : languageBatches) {
                // 过滤出当前批次语言对应的任务
                List<TranslationTask> batchTasks = filterTasksByLanguages(tasks, batchLanguages);
                
                CompletableFuture<Void> future = translateContent(
                    content,
                    movie,
                    batchTasks,
                    batchLanguages,
                    contentType
                );

                try {
                    // 等待翻译完成，但设置超时以避免长时间阻塞
                    future.get(60, TimeUnit.SECONDS);
                } catch (Exception e) {
                    LOG.errorf("翻译等待超时或出错: %s", e.getMessage());
                    // 继续处理其他批次
                }
            }
        }
    }
    
    /**
     * 根据内容长度动态调整语言批次大小并拆分成多个批次
     * 
     * @param languages 要处理的语言列表
     * @param contentLength 内容长度
     * @return 拆分后的语言批次列表
     */
    private List<List<String>> splitLanguagesIntoBatches(List<String> languages, int contentLength) {
        int languagesPerBatch;
        
        // 根据内容长度动态调整每批次的语言数量
        // 基于Azure API的Token限制和平均语言长度进行估算
        if (contentLength <= 100) {
            // 短内容（<=100字符），每批次可处理更多语言
            languagesPerBatch = 20;
        } else if (contentLength <= 500) {
            // 中等内容（101-500字符）
            languagesPerBatch = 10;
        } else if (contentLength <= 1000) {
            // 较长内容（501-1000字符）
            languagesPerBatch = 5;
        } else if (contentLength <= 2000) {
            // 长内容（1001-2000字符）
            languagesPerBatch = 3;
        } else {
            // 超长内容（>2000字符），每批次只处理少量语言以避免超出Token限制
            languagesPerBatch = 2;
        }
        
        List<List<String>> batches = new ArrayList<>();
        for (int i = 0; i < languages.size(); i += languagesPerBatch) {
            int end = Math.min(i + languagesPerBatch, languages.size());
            batches.add(languages.subList(i, end));
        }
        
        return batches;
    }
    
    /**
     * 根据指定的目标语言过滤任务列表
     * 
     * @param allTasks 所有任务
     * @param targetLanguages 目标语言列表
     * @return 过滤后的任务列表
     */
    private List<TranslationTask> filterTasksByLanguages(List<TranslationTask> allTasks, List<String> targetLanguages) {
        return allTasks.stream()
                .filter(task -> targetLanguages.contains(task.targetLanguage))
                .collect(Collectors.toList());
    }
    
    /**
     * 执行翻译并处理结果
     */
    private CompletableFuture<Void> translateContent(String content, MovieInfo movie, List<TranslationTask> tasks, 
                                  List<String> targetLanguages, String contentType) {
        long startTime = System.currentTimeMillis();
        List<Long> taskIds = tasks.stream().map(task -> task.id).collect(Collectors.toList());
        String taskType = "TITLE".equals(contentType) ? "title" : "description";
        
        return CompletableFuture.runAsync(() -> {
            try {
                LOG.infof("开始翻译 %s 的%s到 %d 种语言", movie.code, taskType, targetLanguages.size());
                
                // 调用翻译API
                String result = translationClient.translateBatch(
                    Collections.singletonList(content),
                    "Japanese",
                    targetLanguages,
                    false,
                    true
                );
                
                if (!result.startsWith("Translation failed:")) {
                // 处理翻译结果
                Map<String, MovieInfo> contentMap = new HashMap<>();
                contentMap.put(content, movie);
                
                // 使用 self 代理调用以确保拦截器有效
                List<MovieInfoTranslation> translations = self.processResult(
                    result, 
                    Collections.singletonList(content), 
                    contentMap, 
                    taskType
                );
                
                // 保存翻译结果并更新任务状态
                self.persistResults(translations, taskIds, "COMPLETED");
                
                // 更新源记录状态
                String statusField = "TITLE".equals(contentType) ? "titleStatus" : "descriptionStatus";
                // 通过 self 代理调用以确保在异步线程中有活跃的事务和上下文
                self.updateMovieInfoStatus(movie.code, statusField);
                
                logStatistics(startTime, targetLanguages.size(), translations.size(), 0);
                
            } else {
                LOG.errorf("翻译失败: %s", result);
                taskManager.updateTasksStatus(taskIds, "FAILED");
                logStatistics(startTime, targetLanguages.size(), 0, targetLanguages.size());
            }
            
        } catch (Exception e) {
            LOG.errorf("翻译过程中出错: %s", e.getMessage(), e);
            taskManager.updateTasksStatus(taskIds, "FAILED");
            logStatistics(startTime, targetLanguages.size(), 0, targetLanguages.size());
        }
        }, executor);
    }
    
    /**
     * 处理翻译结果
     * 使用 ActivateRequestContext 确保在异步线程中有活跃的请求上下文
     * 使用 Transactional 确保数据库操作在事务中执行
     */
    @ActivateRequestContext
    @Transactional
    protected List<MovieInfoTranslation> processResult(String result, List<String> items,
                                                     Map<String, MovieInfo> itemMap, String taskType) {
        List<MovieInfoTranslation> results = new ArrayList<>();
        
        try {
            Map<String, Map<String, String>> resultMap = translationClient.parseTranslationResult(result);
            
            // 检查结果映射是否为空
            if (resultMap == null || resultMap.isEmpty()) {
                LOG.error("翻译结果解析错误：返回空的结果映射");
                return results;
            }
            
            // 安全获取第一个翻译结果
            Map<String, String> translation;
            try {
                translation = resultMap.values().iterator().next();
            } catch (NoSuchElementException e) {
                LOG.error("翻译结果解析错误：无法获取翻译结果", e);
                return results;
            }
            
            // 检查翻译结果是否为空
            if (translation == null || translation.isEmpty()) {
                LOG.error("翻译结果解析错误：翻译映射为空");
                return results;
            }
            
            translation.forEach((lang, text) -> {
                AzureAILanguageCode langCode = AzureAILanguageCode.fromDisplayName(lang);
                if (langCode != null && !text.isEmpty()) {
                    // 检测可能的错误翻译格式
                    if (containsMultipleLanguagePairs(text)) {
                        LOG.errorf("检测到翻译结果包含多语言标记，可能为错误格式: %s = %s", 
                                   lang, text.substring(0, Math.min(text.length(), 100)) + (text.length() > 100 ? "..." : ""));
                        return;
                    }
                    
                    MovieInfo sourceInfo = itemMap.get(items.get(0));
                    MovieInfoTranslation translated = MovieInfoTranslation.findByCodeAndLanguage(
                        sourceInfo.code, langCode.getLanguageCode()
                    );
                    
                    if (translated == null) {
                        translated = new MovieInfoTranslation();
                        translated.code = sourceInfo.code;
                        translated.language = langCode.getLanguageCode();
                    }
                    
                    if ("title".equals(taskType)) {
                        translated.title = text;
                    } else {
                        translated.description = text;
                    }
                    
                    results.add(translated);
                }
            });
        } catch (Exception e) {
            LOG.error("处理翻译结果时出错", e);
        }
        
        return results;
    }
    
    /**
     * 检测翻译结果是否包含多个语言标记
     */
    private boolean containsMultipleLanguagePairs(String text) {
        return LANGUAGE_LABEL_PATTERN.matcher(text).find();
    }
    
    /**
     * 统计并记录翻译耗时信息
     */
    private void logStatistics(long startTime, int total, int success, int failure) {
        long duration = System.currentTimeMillis() - startTime;
        LOG.infof("翻译完成: 总计=%d, 成功=%d, 失败=%d, 耗时=%dms", total, success, failure, duration);
    }
    
    /**
     * 更新电影信息的翻译状态
     * 使用 ActivateRequestContext 和 Transactional 确保在异步线程中可以正常访问数据库
     * 
     * @param code 电影代码
     * @param statusField 要更新的状态字段
     */
    @ActivateRequestContext
    @Transactional
    public void updateMovieInfoStatus(String code, String statusField) {
        try {
            int updated = MovieInfo.update(statusField + " = 'translated' WHERE code = ?1", code);
            if (updated > 0) {
                LOG.infof("成功更新内容 %s 的%s状态为 translated", code, statusField);
            }
        } catch (Exception e) {
            LOG.errorf("更新电影状态时出错: %s", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 保存翻译结果并更新任务状态
     * 使用 ActivateRequestContext 确保在异步线程中有活跃的请求上下文
     */
    @ActivateRequestContext
    @Transactional
    public void persistResults(List<MovieInfoTranslation> translations, List<Long> taskIds, String status) {
        if (translations == null || translations.isEmpty()) {
            return;
        }
        
        try {
            for (MovieInfoTranslation translation : translations) {
                MovieInfoTranslation existing = MovieInfoTranslation.findByCodeAndLanguage(
                    translation.code, translation.language
                );
                
                if (existing != null) {
                    if (translation.title != null) {
                        existing.title = translation.title;
                    }
                    if (translation.description != null) {
                        existing.description = translation.description;
                    }
                    existing.updatedAt = Instant.now();
                } else {
                    translation.persist();
                }
            }
            
            // 更新任务状态
            if (taskIds != null && !taskIds.isEmpty()) {
                taskManager.updateTasksStatus(taskIds, status);
            }
            
            LOG.infof("成功保存了 %d 个翻译结果", translations.size());
            
        } catch (Exception e) {
            LOG.error("保存翻译结果时发生错误", e);
            throw e;
        }
    }
}
