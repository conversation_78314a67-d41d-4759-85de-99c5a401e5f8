package org.acme.service.translation;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.acme.config.AzureOpenAIConfig;
import org.acme.config.AzureOpenAIConfig.ClientConfig;
import org.jboss.logging.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * Direct client for Azure OpenAI API for translation purposes.
 * This provides a reliable implementation for Azure OpenAI integration.
 */
@ApplicationScoped
public class AzureOpenAITranslationClient {

    private static final Logger logger = Logger.getLogger(AzureOpenAITranslationClient.class);
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    private static final ObjectMapper mapper = new ObjectMapper();
    private static final org.slf4j.Logger log = LoggerFactory.getLogger(AzureOpenAITranslationClient.class);

    // This client will be replaced by the ProxyAwareAzureOpenAIClient
    // which injects a properly configured client with retry and error handling
    private OkHttpClient client = new OkHttpClient.Builder()
        .connectTimeout(120, java.util.concurrent.TimeUnit.SECONDS)
        .writeTimeout(120, java.util.concurrent.TimeUnit.SECONDS)
        .readTimeout(180, java.util.concurrent.TimeUnit.SECONDS) // Longer read timeout for large responses
        .protocols(java.util.Arrays.asList(okhttp3.Protocol.HTTP_1_1)) // Force HTTP/1.1 to avoid HTTP/2 issues
        .retryOnConnectionFailure(true)
        .build();

    // Client configuration and selection
    @Inject
    AzureOpenAIConfig config;

    // Track client usage for rotation
    private final AtomicInteger requestCounter = new AtomicInteger(0);
    private ClientConfig currentClient = null;

    // Constants for batch processing
    private static final String BATCH_TEXT_START_DELIMITER_PREFIX = "---TEXT_";
    private static final String BATCH_TEXT_START_DELIMITER_SUFFIX = "_TRANSLATIONS_START---";
    private static final String BATCH_TEXT_END_DELIMITER_PREFIX = "---TEXT_";
    private static final String BATCH_TEXT_END_DELIMITER_SUFFIX = "_TRANSLATIONS_END---";

    public String translateBatch(List<String> textsToTranslate, String sourceLanguage, List<String> targetLanguages,
                                 boolean isSubtitle, boolean isAVContext) {

        if (textsToTranslate == null || textsToTranslate.isEmpty()) {
            logger.error("Texts to translate list cannot be null or empty.");
            return "Translation failed: No source texts specified.";
        }
        if (targetLanguages == null || targetLanguages.isEmpty()) {
            logger.error("Target languages list cannot be null or empty.");
            return "Translation failed: No target languages specified.";
        }

        try {
            List<Map<String, String>> messages = new ArrayList<>();
            Map<String, String> systemMessageMap = new HashMap<>();
            systemMessageMap.put("role", "system");

            String targetLanguagesString = targetLanguages.stream().collect(Collectors.joining(", "));

            // Core Preamble - largely unchanged but implies multiple texts
            String multiLangCorePreamble =
                "You are a top-tier multilingual translation expert, renowned for producing outstanding translations that are not only accurate but also exceptionally natural, idiomatic, and culturally resonant across diverse languages and contexts.\n\n" +
                "Your Task:\n" +
                "For EACH of the [Source Text] entries provided by the user (originally in " + sourceLanguage + "), translate it into EACH of the specified [Target Languages] (" + targetLanguagesString + ").\n\n" +
                "For each target language, for each source text, the translation must strictly adhere to these core principles:\n" +
                "1. Accuracy and Fidelity: Completely and precisely convey the original core meaning, all nuances, overall tone, and deeper intent of the given [Source Text] entry.\n" +
                "2. Naturalness and Idiomaticity: The translation must achieve ultimate fluency and naturalness, perfectly aligning with the everyday idiomatic expressions of a native speaker of that target language. It should read and sound as if originally crafted by a native speaker of that language who has a profound understanding of the [Source Text] entry's specific subject matter, context, and intended audience. [SPECIFIC_GENRE_INSTRUCTION_PLACEHOLDER]\n" +
                "3. Cultural Relevance and Adaptation: Appropriately handle and adapt any culturally specific references, allusions, humor, or potential sensitivities from the original text entry, ensuring the translation is easily understandable, suitable, and resonant for the audience of each target language.\n\n";

            // MODIFIED Output Format Instruction for BATCHES
            StringBuilder outputFormatInstructionBuilder = new StringBuilder(
                "Output Format Requirement:\n" +
                "You MUST process EACH [Source Text] provided by the user sequentially. For EACH [Source Text], you will provide its translations into all [Target Languages].\n" +
                "Clearly demarcate the translations for EACH source text using the following structure:\n\n"
            );
            for (int i = 0; i < textsToTranslate.size(); i++) {
                final int currentIndex = i;
                outputFormatInstructionBuilder.append(BATCH_TEXT_START_DELIMITER_PREFIX).append(currentIndex + 1).append(BATCH_TEXT_START_DELIMITER_SUFFIX).append("\n");
                outputFormatInstructionBuilder.append(
                    targetLanguages.stream()
                        .map(lang -> lang + " - [Translated text for " + lang + " for Source Text " + (currentIndex + 1) + "]")
                        .collect(Collectors.joining("\n"))
                ).append("\n");
                outputFormatInstructionBuilder.append(BATCH_TEXT_END_DELIMITER_PREFIX).append(i + 1).append(BATCH_TEXT_END_DELIMITER_SUFFIX).append("\n");
                if (i < textsToTranslate.size() - 1) {
                    outputFormatInstructionBuilder.append("\n"); // Add a blank line between text blocks for readability
                }
            }

            outputFormatInstructionBuilder.append(
                "\n(Example: If there are 2 source texts and target languages are Chinese, English, the output structure would be:\n" +
                BATCH_TEXT_START_DELIMITER_PREFIX + "1" + BATCH_TEXT_START_DELIMITER_SUFFIX + "\n" +
                "Chinese - [中文翻译文本 for source text 1]\n" +
                "English - [English translated text for source text 1]\n" +
                BATCH_TEXT_END_DELIMITER_PREFIX + "1" + BATCH_TEXT_END_DELIMITER_SUFFIX + "\n\n" +
                BATCH_TEXT_START_DELIMITER_PREFIX + "2" + BATCH_TEXT_START_DELIMITER_SUFFIX + "\n" +
                "Chinese - [中文翻译文本 for source text 2]\n" +
                "English - [English translated text for source text 2]\n" +
                BATCH_TEXT_END_DELIMITER_PREFIX + "2" + BATCH_TEXT_END_DELIMITER_SUFFIX + "\n" +
                ")\n\n"
            );
            String outputFormatInstruction = outputFormatInstructionBuilder.toString();


            String coreFinalSystemInstructions =
                "\nImportant Instructions:\n" +
                "Do NOT add any form of extra comments, personal explanations, introductions, apologies, annotations, or any non-translation text outside of the structured multilingual translation list for each source text.\n" +
                "Strictly return only the structured multilingual translation list in the format specified above for ALL provided source texts.";

            String systemContent;
            String specificGenreInstruction;
            String additionalFormattingInfo;

            // --- Specific Genre Instructions (same as your original code) ---
            if (isSubtitle) {
                logger.warn("Subtitle translation path called, but user focus is on AV metadata. Ensure this path is intended or review prompt for subtitle specifics.");
                additionalFormattingInfo = "Ensure all original subtitle formatting (timestamps, tags, line breaks) is meticulously preserved within each language's translated subtitle block. [Detailed subtitle rules would go here if this path were primary]";
                if (isAVContext) {
                    specificGenreInstruction = "This is an AV subtitle. Translate with appropriate tone, slang, and nuance for adult content in each target language, suitable for subtitles. [Detailed AV subtitle content rules would go here]";
                } else {
                    specificGenreInstruction = "This is a standard subtitle. Ensure clarity and conciseness appropriate for subtitles in each target language.";
                }
            } else { // NOT a subtitle
                additionalFormattingInfo = "If any [Source Text] entry has specific formatting (like Markdown, multiple paragraphs for an intro, significant whitespace), preserve this original structure within the translated text for each language where appropriate for the content type.";

                if (isAVContext) {
                    specificGenreInstruction =
                        "The [Source Text] entries are metadata (e.g., video title, actress name/profile, introduction/description) for Adult Video (AV) content. Your role is not just a translator, but a **master-level, native-speaking AV marketing genius and cultural insider** for each target language.\n" +
                        "Your mission is to make the translated metadata for EACH entry **so irresistibly enticing, idiomatic, and culturally resonant that it feels like it was handcrafted by a top local promoter for that specific market.**\n\n" +

                        "**CRUCIAL DIRECTIVE: Destroy 'Translation-ese'.**\n" +
                        "Your primary enemy is stiff, literal, word-for-word translation that simply mirrors the source language's grammar. You MUST break free from this. **Your value lies in your creative and cultural re-interpretation.** This means you are empowered to:\n" +
                        "- **Completely rephrase and restructure sentences** to match the natural rhythm and flow of the target language.\n" +
                        "- **Replace literal terms with powerful, evocative, and niche-specific slang, jargon, and idioms.**\n" +
                        "- **Prioritize the intended marketing IMPACT and emotional VIBE** over a dry, academic replication of the original text.\n\n" +

                        "**Example of WEAK vs. STRONG Translation (for a Title):**\n" +
                        "- **Source Fragments:** `♯有点阴沉？♯但却是夜总会小姐♯潮吹失禁♯`\n" +
                        "- **WEAK/LITERAL Translation (what to AVOID):** 'A bit gloomy? But is a club hostess; Gushing and Incontinence.' (This is robotic and lacks appeal).\n" +
                        "- **STRONG/IDIOMATIC Translation (what to AIM FOR):** 'The Moody Club Hostess's Shameless, Gushing Climax!' or 'She's a Gloomy Beauty by Day, a Gushing Goddess by Night!' (This synthesizes the concepts into a punchy, marketable, and natural-sounding title).\n\n" +

                        "**Detailed Guidelines by Content Type (apply to each entry):\n" +
                        "- For **AV Titles**: Your titles must be **sensational, viral-ready, and dripping with allure**. Be a creative headline writer. If the source is a list of tags, **your job is to weave them into a killer narrative or an unforgettable hook.** What's the core fantasy? Sell it hard.\n" +
                        "- For **AV Actress Names/Profiles**: Use established fan names or create memorable, sexy renderings. Profiles must read like **seductive whispers**, highlighting their most magnetic traits using language that fans in that culture would use and search for.\n" +
                        "- For **AV Introductions/Descriptions**: Transform these into **vivid, tantalizing plot summaries**. Use powerful, descriptive verbs. Build suspense and anticipation. Make the user feel the heat and urgency, making them believe this video is an unmissable event.\n\n" +
                        "In summary, discard any bland or literal phrasing. Think like a fan, write like a marketing pro, and create translations that don't just inform, but **seduce and sell**.";

                } else { // General text, not AV, not subtitle
                    specificGenreInstruction =
                        "If any [Source Text] entry possesses a distinct genre or style (e.g., news report, technical manual, casual conversation, formal marketing copy, literary prose, poetry, etc.), you must meticulously replicate that specific style using the most authentic and professional expressions appropriate for that genre in each target language.";
                }
            }
            // --- End of Specific Genre Instructions ---

            String tempSystemContent = multiLangCorePreamble.replace("[SPECIFIC_GENRE_INSTRUCTION_PLACEHOLDER]", specificGenreInstruction);
            systemContent = tempSystemContent + outputFormatInstruction + additionalFormattingInfo + coreFinalSystemInstructions;

            systemMessageMap.put("content", systemContent);
            messages.add(systemMessageMap);

            Map<String, String> userMessage = new HashMap<>();
            userMessage.put("role", "user");

            // Construct user message with all texts
            StringBuilder userMessageContentBuilder = new StringBuilder();
            userMessageContentBuilder.append(String.format(
                "Translate EACH of the following [Source Text] entries from their original language '%s' into the listed [Target Languages]. " +
                "Adhere strictly to all system instructions regarding accuracy, idiomatic style for the specific content type (especially making it enticing and SEO-friendly for AV content if applicable), cultural relevance, and the batch output formatting for each language and each text.\n\n" +
                "[Target Languages]: %s\n\n" +
                "Source Texts to Translate:\n",
                sourceLanguage, targetLanguagesString
            ));

            for (int j = 0; j < textsToTranslate.size(); j++) {
                userMessageContentBuilder.append(String.format("%d. [Source Text %d]: %s\n", j + 1, j + 1, textsToTranslate.get(j)));
            }
            userMessage.put("content", userMessageContentBuilder.toString());
            messages.add(userMessage);

            ObjectNode requestBody = mapper.createObjectNode();
            ArrayNode messagesNode = requestBody.putArray("messages");
            for (Map<String, String> message : messages) {
                ObjectNode messageNode = messagesNode.addObject();
                message.forEach(messageNode::put);
            }

            // Consider increasing max_tokens if the combined length of translations for all texts might exceed the default
            // The default max_tokens for gpt-4 models is typically 4096 for the *completion*.
            // If 10 texts * N languages * avg_translation_length is large, this might need adjustment.
            // For now, 4090 is kept, but monitor if translations get truncated.
            requestBody.put("max_tokens", 4090);
            requestBody.put("temperature", 0.6);

                        // Select client configuration and build URL for Azure OpenAI
            ClientConfig clientConfig = selectClient();
            String url = String.format("%s/openai/deployments/%s/chat/completions?api-version=%s",
                    clientConfig.getEndpoint(), clientConfig.getDeploymentId(), clientConfig.getApiVersion());

            logger.infof("Sending BATCH multi-language translation request. Number of texts: {}. Target languages: {}. isAVContext: {}, isSubtitle: {}",
                         textsToTranslate.size(), targetLanguagesString, isAVContext, isSubtitle);
            // logger.debug("System Prompt for BATCH: {}", systemContent); // Extremely long

            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("api-key", clientConfig.getApiKey())
                    .post(RequestBody.create(requestBody.toString(), JSON))
                    .build();

            try (Response response = client.newCall(request).execute()) {
                String responseBodyString = response.body() != null ? response.body().string() : "No response body";

                if (!response.isSuccessful()) {
                    logger.errorf("BATCH Multi-language translation API request failed with code {}: {}",
                            response.code(), responseBodyString);
                    return "Translation failed with error code: " + response.code() + ", details: " + responseBodyString;
                }

                JsonNode rootNode = mapper.readTree(responseBodyString);
                if (rootNode.has("choices") && rootNode.get("choices").isArray() && !rootNode.get("choices").isEmpty()) {
                    JsonNode firstChoice = rootNode.get("choices").get(0);
                    if (firstChoice.has("message") && firstChoice.get("message").has("content")) {
                        return firstChoice.get("message").get("content").asText();
                    }
                }

                logger.errorf("Unexpected response format from BATCH multi-language translation: {}", responseBodyString);
                return "Translation failed: Unexpected response format";
            }

        } catch (IOException e) {
            logger.error("Error during BATCH multi-language translation request", e);
            return "Translation failed: " + e.getMessage();
        }
    }

    /**
     * Parses the batch translation response string.
     *
     * @param rawResponse The raw string response from the translation API.
     * @param numTextsExpected The number of original texts sent for translation.
     * @return A list of maps. Each map corresponds to an original text,
     * mapping target language codes to their translated text.
     * Returns an empty list if parsing fails or the format is incorrect.
     */
    public List<Map<String, String>> parseBatchTranslationResponse(String rawResponse, int numTextsExpected) {
        log.info("------ raw: " + rawResponse);
        List<Map<String, String>> allTextsTranslations = new ArrayList<>();
        if (rawResponse == null || rawResponse.trim().isEmpty()) {
            logger.error("Raw response for batch translation is null or empty.");
            return allTextsTranslations;
        }

        // Pattern to capture each text block
        // It looks for ---TEXT_(\d+)_TRANSLATIONS_START---(.*?)---TEXT_(\d+)_TRANSLATIONS_END---
        // The DOTALL flag (s) allows . to match newlines
        Pattern textBlockPattern = Pattern.compile(
            Pattern.quote(BATCH_TEXT_START_DELIMITER_PREFIX) + "(\\d+)" + Pattern.quote(BATCH_TEXT_START_DELIMITER_SUFFIX) +
            "(.*?)" +
            Pattern.quote(BATCH_TEXT_END_DELIMITER_PREFIX) + "\\1" + Pattern.quote(BATCH_TEXT_END_DELIMITER_SUFFIX),
            Pattern.DOTALL
        );
        Matcher textBlockMatcher = textBlockPattern.matcher(rawResponse);

        // Temporary list to hold found blocks before sorting by index
        List<Map.Entry<Integer, Map<String, String>>> tempTranslations = new ArrayList<>();

        while (textBlockMatcher.find()) {
            try {
                int textIndex = Integer.parseInt(textBlockMatcher.group(1)); // Text number (1-based)
                String translationsForOneTextBlock = textBlockMatcher.group(2).trim();

                Map<String, String> currentTextTranslations = new HashMap<>();
                String[] lines = translationsForOneTextBlock.split("\\r?\\n");
                for (String line : lines) {
                    if (line.trim().isEmpty()) continue;
                    int hyphenIndex = line.indexOf(" - ");
                    if (hyphenIndex > 0) {
                        String lang = line.substring(0, hyphenIndex).trim();
                        String translation = line.substring(hyphenIndex + 3).trim();
                        currentTextTranslations.put(lang, translation);
                    } else {
                        logger.warnf("Could not parse line in translation block for text index {}: '{}'", textIndex, line);
                    }
                }
                if (!currentTextTranslations.isEmpty()) {
                    tempTranslations.add(new HashMap.SimpleEntry<>(textIndex, currentTextTranslations));
                }
            } catch (NumberFormatException e) {
                logger.error("Error parsing text index from delimiter: " + textBlockMatcher.group(0), e);
            } catch (Exception e) {
                logger.error("Error parsing translation block: " + textBlockMatcher.group(0), e);
            }
        }

        // Sort by text index to ensure original order
        tempTranslations.sort(Map.Entry.comparingByKey());

        for(Map.Entry<Integer, Map<String, String>> entry : tempTranslations) {
            allTextsTranslations.add(entry.getValue());
        }


        if (allTextsTranslations.size() != numTextsExpected) {
            logger.warnf("Warning: Expected translations for {} texts, but parsed {}. The LLM might not have followed the format perfectly for all texts. Raw response:\n{}",
                        numTextsExpected, allTextsTranslations.size(), rawResponse.substring(0, Math.min(rawResponse.length(), 1000))); // Log first 1000 chars
        }

        return allTextsTranslations;
    }


    /**
     * Translates text using Azure OpenAI directly
     *
     * @param text Text to translate
     * @param sourceLanguage Source language
     * @param targetLanguage Target language
     * @param isSubtitle Whether this is a subtitle translation (requiring format preservation)
     * @param isAVSubtitle Whether this is an adult video subtitle translation (requiring specialized handling)
     * @return Translated text
     */
    public String translate(String text, String sourceLanguage, String targetLanguage, boolean isSubtitle, boolean isAVSubtitle) {
        try {
            // Build system and user messages
            List<Map<String, String>> messages = new ArrayList<>();

            // System message - instructions for the model
            Map<String, String> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");

            if (isSubtitle) {
                if (isAVSubtitle) {
                    // Specialized system message for adult video subtitles
                    systemMessage.put("content",
                        "You are a professional subtitle translator, specializing in colloquial " + sourceLanguage + " and adult-oriented content. " +
                        "Your primary task is to translate the " + sourceLanguage + " subtitle text from adult videos into " + targetLanguage + ". " +

                        // Strict Formatting Constraints
                        "You MUST strictly maintain the original subtitle format. This includes: " +
                        "1. DO NOT alter any timestamps, subtitle sequence numbers, or other technical formatting cues. " +
                        "2. If the original subtitles contain formatting tags (e.g., <i>italics</i>, <b>bold</b>, <font color=\"#FFFF00\">color tags</font>, positioning tags), replicate these tags around the correspondingly translated text. " +
                        "3. Translate ONLY the actual textual content of the subtitles. " +
                        "4. Preserve the original line breaks and the distribution of text across lines precisely as they appear in the source. Each translated line must correspond to an original line. " +

                        // Content-Specific Translation Requirements for AV
                        "For this adult video content, it is crucial that your translation accurately captures and conveys: " +
                        "A. The original nuance, subtlety, and any suggestive or double meanings in the dialogue. " +
                        "B. The emotional tone (e.g., seductive, playful, demanding, surprised, expressions of pleasure or pain, including moans or other character sounds if they are transcribed as text within the subtitle). " +
                        "C. Any specific slang, colloquialisms, or idiomatic expressions common in adult entertainment contexts, translating them into natural-sounding equivalents in " + targetLanguage + ". " +

                        // Output Style and Fidelity
                        "The " + targetLanguage + " translation must sound natural, befitting the scene's context, and reflect how the dialogue or transcribed sounds would be understood by a native " + targetLanguage + " speaker familiar with such content. " +
                        "Maintain utmost fidelity to the source text's intended meaning and impact. " +

                        // Final Instruction
                        "Return ONLY the translated text for each subtitle segment. Do not add any explanations, notes, headers, or any text whatsoever beyond the direct translation of the subtitle content itself, fitting within the original formatting structure."
                    );
                } else {
                    // Standard subtitle translation system message
                    systemMessage.put("content",
                        "You are a professional subtitle translator. " +
                        "Your task is to translate subtitle text while strictly maintaining the original format. " +
                        "Do not change any timestamps, subtitle numbers, or formatting. " +
                        "Translate only the actual text content. " +
                        "Maintain the same line breaks and text distribution as in the original."
                    );
                }
            } else {
                systemMessage.put("content",
                    "You are a professional translator with deep expertise in multiple languages and a keen understanding of their cultural nuances and idiomatic expressions. " +
                    "Your task is to translate the provided text. Beyond mere accuracy, the translation must:" +
                    "1.  Be **exceptionally natural, fluent, and perfectly idiomatic** for the specific context, subject matter, and intended audience of the original text. It should read as if originally written by a native speaker of the target language who is an expert in that subject matter or deeply familiar with that context." +
                    "2.  Maintain **the highest fidelity** to the original meaning, tone, and intent." +
                    "3.  Preserve the **original formatting** meticulously." +
                    "Do not add any explanations, apologies, or notes—return only the translated text."
                );
            }
            messages.add(systemMessage);

            // User message - the text to translate
            Map<String, String> userMessage = new HashMap<>();
            userMessage.put("role", "user");

            if (isSubtitle) {
                userMessage.put("content",
                    "Here is a subtitle file section in " + sourceLanguage + " that I need translated to " +
                    targetLanguage + ". Translate only the text, keeping all numbers, timestamps, and formatting intact:\n\n" +
                    text
                );
            } else {
                userMessage.put("content",
                    "Translate the following text from " + sourceLanguage + " to " + targetLanguage + ":\n\n" +
                    text
                );
            }
            messages.add(userMessage);

            // Create the request body
            ObjectNode requestBody = mapper.createObjectNode();
            ArrayNode messagesNode = requestBody.putArray("messages");

            for (Map<String, String> message : messages) {
                ObjectNode messageNode = messagesNode.addObject();
                message.forEach(messageNode::put);
            }

            requestBody.put("max_tokens", 4000);
            requestBody.put("temperature", 0.1);

            // Build URL for Azure OpenAI
                        // Select client configuration and build URL for Azure OpenAI
            ClientConfig clientConfig = selectClient();
            String url = String.format("%sopenai/deployments/%s/chat/completions?api-version=%s",
                    clientConfig.getEndpoint(), clientConfig.getDeploymentId(), clientConfig.getApiVersion());

            logger.infof("Sending translation request to: %s", url);

            // Create and execute request
            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("api-key", clientConfig.getApiKey())
                    .post(RequestBody.create(requestBody.toString(), JSON))
                    .build();

            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String responseBody = response.body() != null ? response.body().string() : "No response body";
                    logger.errorf("Translation API request failed with code %d: %s",
                            response.code(), responseBody);
                    return "Translation failed with error code: " + response.code() + ", details: " + responseBody;
                }

                String responseBody = response.body().string();
                JsonNode rootNode = mapper.readTree(responseBody);

                // Extract the response content
                if (rootNode.has("choices") && rootNode.get("choices").isArray() && rootNode.get("choices").size() > 0) {
                    JsonNode firstChoice = rootNode.get("choices").get(0);
                    if (firstChoice.has("message") && firstChoice.get("message").has("content")) {
                        return firstChoice.get("message").get("content").asText();
                    }
                }

                logger.error("Unexpected response format: " + responseBody);
                return "Translation failed: Unexpected response format";
            }

        } catch (java.net.SocketTimeoutException e) {
            logger.error("Connection timeout during translation request", e);
            return "Translation failed: Connection timeout - " + e.getMessage();
        } catch (java.net.ConnectException e) {
            logger.error("Connection error during translation request", e);
            return "Translation failed: Connection error - " + e.getMessage();
        } catch (java.io.EOFException e) {
            logger.error("EOF error during translation request - likely connection closed prematurely", e);
            return "Translation failed: Connection closed prematurely - " + e.getMessage();
        } catch (java.net.UnknownHostException e) {
            logger.error("DNS resolution error during translation request", e);
            return "Translation failed: DNS resolution error - " + e.getMessage();
        } catch (javax.net.ssl.SSLException e) {
            logger.error("SSL/TLS error during translation request", e);
            return "Translation failed: SSL/TLS error - " + e.getMessage();
        } catch (IOException e) {
            logger.error("I/O error during translation request", e);
            return "Translation failed: I/O error - " + e.getMessage();
        } catch (Exception e) {
            logger.error("Unexpected error during translation request", e);
            return "Translation failed: Unexpected error - " + e.getMessage();
        }
    }



    /**
     * Translates subtitle text preserving formatting
     */
    public String translateSubtitle(String text, String sourceLanguage, String targetLanguage) {
        // Handle large subtitle files by chunking if needed
        if (text.length() > 4000) {
            return translateLargeSubtitle(text, sourceLanguage, targetLanguage);
        }
        return translate(text, sourceLanguage, targetLanguage, true, true);
    }

    // translate av text
    public String translateAvText(String text, String sourceLanguage, String targetLanguage) {

            // Build system and user messages
            List<Map<String, String>> messages = new ArrayList<>();

            // System message - instructions for the model
            Map<String, String> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");

            systemMessage.put("content",
                    "You are a professional translator with expertise in multiple languages. You are a professional adult video konwledge. " +
                    "Translate the text with accuracy while preserving the original formatting. " +
                    "Do not add any explanations or notes - return only the translated text."
                );
            messages.add(systemMessage);

            // User message - the text to translate
            Map<String, String> userMessage = new HashMap<>();
            userMessage.put("role", "user");

            userMessage.put("content",
                    "Translate the following text from " + sourceLanguage + " to " + targetLanguage + ":\n\n" +
                    text
                );
            messages.add(userMessage);

            // Create the request body
            ObjectNode requestBody = mapper.createObjectNode();
            ArrayNode messagesNode = requestBody.putArray("messages");

            for (Map<String, String> message : messages) {
                ObjectNode messageNode = messagesNode.addObject();
                message.forEach(messageNode::put);
            }

            requestBody.put("max_tokens", 4000);
            requestBody.put("temperature", 0.1);

            // Build URL for Azure OpenAI
                        // Select client configuration and build URL for Azure OpenAI
            ClientConfig clientConfig = selectClient();
            String url = String.format("%sopenai/deployments/%s/chat/completions?api-version=%s",
                    clientConfig.getEndpoint(), clientConfig.getDeploymentId(), clientConfig.getApiVersion());

            logger.infof("Sending translation request to: %s", url);

            // Create and execute request
            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("api-key", clientConfig.getApiKey())
                    .post(RequestBody.create(requestBody.toString(), JSON))
                    .build();

            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String responseBody = response.body() != null ? response.body().string() : "No response body";
                    logger.errorf("Translation API request failed with code %d: %s",
                            response.code(), responseBody);
                    return "Translation failed with error code: " + response.code() + ", details: " + responseBody;
                }

                String responseBody = response.body().string();
                JsonNode rootNode = mapper.readTree(responseBody);

                // Extract the response content
                if (rootNode.has("choices") && rootNode.get("choices").isArray() && rootNode.get("choices").size() > 0) {
                    JsonNode firstChoice = rootNode.get("choices").get(0);
                    if (firstChoice.has("message") && firstChoice.get("message").has("content")) {
                        return firstChoice.get("message").get("content").asText();
                    }
                }

                logger.error("Unexpected response format: " + responseBody);
                return "Translation failed: Unexpected response format";
            } catch (IOException e) {
                logger.error("Error during translation request", e);
                return "Translation failed: " + e.getMessage();
            }
    }


    public String translate(String text, String sourceLanguage, List<String> targetLanguages,
                            boolean isSubtitle, boolean isAVContext) {

        if (targetLanguages == null || targetLanguages.isEmpty()) {
            logger.error("Target languages list cannot be null or empty.");
            return "Translation failed: No target languages specified.";
        }

        try {
            List<Map<String, String>> messages = new ArrayList<>();
            Map<String, String> systemMessageMap = new HashMap<>();
            systemMessageMap.put("role", "system");

            String targetLanguagesString = targetLanguages.stream().collect(Collectors.joining(", "));

            String multiLangCorePreamble =
                "You are a top-tier multilingual translation expert, renowned for producing outstanding translations that are not only accurate but also exceptionally natural, idiomatic, and culturally resonant across diverse languages and contexts.\n\n" +
                "Your Task:\n" +
                "Translate the [Source Text] (provided by the user, originally in " + sourceLanguage + ") into each of the specified [Target Languages] (" + targetLanguagesString + ").\n\n" +
                "For each target language, the translation must strictly adhere to these core principles:\n" +
                "1. Accuracy and Fidelity: Completely and precisely convey the original core meaning, all nuances, overall tone, and deeper intent of the [Source Text].\n" +
                "2. Naturalness and Idiomaticity: The translation must achieve ultimate fluency and naturalness, perfectly aligning with the everyday idiomatic expressions of a native speaker of that target language. It should read and sound as if originally crafted by a native speaker of that language who has a profound understanding of the [Source Text]'s specific subject matter, context, and intended audience. [SPECIFIC_GENRE_INSTRUCTION_PLACEHOLDER]\n" +
                "3. Cultural Relevance and Adaptation: Appropriately handle and adapt any culturally specific references, allusions, humor, or potential sensitivities from the original text, ensuring the translation is easily understandable, suitable, and resonant for the audience of each target language.\n\n";

            String outputFormatInstruction =
                "Output Format Requirement:\n" +
                "You MUST strictly adhere to the following format to clearly present all translation results, with each language's translation on a new line. Each line must start with the language name (exactly as provided in the target languages list) followed by a hyphen and a space, then the translated text for that language:\n\n" +
                targetLanguages.stream().map(lang -> lang + " - [Translated text for " + lang + "]").collect(Collectors.joining("\n")) + "\n" +
                "(Example: If target languages are Chinese, English, then output should be like:\nChinese - [中文翻译文本]\nEnglish - [English translated text])\n\n";

            String coreFinalSystemInstructions =
                "\nImportant Instructions:\n" +
                "Do NOT add any form of extra comments, personal explanations, introductions, apologies, annotations, or any non-translation text outside of the translation results.\n" +
                "Strictly return only the structured multilingual translation list in the format specified above.";

            String systemContent;
            String specificGenreInstruction;
            String additionalFormattingInfo;

            if (isSubtitle) {
                logger.warn("Subtitle translation path called, but user focus is on AV metadata. Ensure this path is intended or review prompt for subtitle specifics.");
                additionalFormattingInfo = "Ensure all original subtitle formatting (timestamps, tags, line breaks) is meticulously preserved within each language's translated subtitle block. [Detailed subtitle rules would go here if this path were primary]";
                if (isAVContext) {
                    specificGenreInstruction = "This is an AV subtitle. Translate with appropriate tone, slang, and nuance for adult content in each target language, suitable for subtitles. [Detailed AV subtitle content rules would go here]";
                } else {
                    specificGenreInstruction = "This is a standard subtitle. Ensure clarity and conciseness appropriate for subtitles in each target language.";
                }
            } else { // NOT a subtitle - User's primary focus (e.g., AV Title, Actress, Intro)
                additionalFormattingInfo = "If the [Source Text] has specific formatting (like Markdown, multiple paragraphs for an intro, significant whitespace), preserve this original structure within the translated text for each language where appropriate for the content type.";

                if (isAVContext) { // AV Metadata (Title, Actress, Intro) - REFINED PROMPT
                    specificGenreInstruction =
                        "You are not merely a translator; you are a **master AV marketing wordsmith and SEO strategist**, specifically for the [Target Language] AV market.\n" +
                        "The [Source Text] is metadata (e.g., video title, actress name/profile, introduction/description) for Adult Video (AV) content.\n" +
                        "Your mission: to transform this metadata into **explosively enticing, supremely clickable, and highly discoverable (SEO-maximized) marketing copy** tailored for AV consumers within the [Target Language] culture.\n\n" +
                        "For the [Target Language], the translation MUST:\n" +
                        "1. **Channel the authentic voice of a native AV connoisseur**: The translation MUST be **hyper-idiomatic, intensely provocative, and possess an 'insider' authenticity**. Employ the precise slang, niche jargon, culturally-specific euphemisms, and powerfully suggestive language that *electrifies* AV consumers in the [Target Language] market. The tone must be **palpably exciting, deeply titillating, and promise an unparalleled, unforgettable encounter**.\n" +
                        "2. **Engineer Maximum Click-Through Rate (CTR) and Desire**: Every syllable must **ooze allure**. Transform even the most vanilla source text into an **unmissable, magnetic teaser**. Your language must ignite an *urgent need* in the user to click and consume.\n" +
                        "3. **Embed High-Impact SEO Keywords Organically**: Seamlessly weave in the most potent, high-traffic AV search terms (for actresses, kinks, scenarios, themes) that [Target Language] users *actually type into search bars*. This keyword integration must feel completely natural, enhancing the provocative copy, not disrupting it. Think discoverability *through* desire.\n\n" +
                        "Detailed guidelines for AV content types in [Target Language]:\n" +
                        "- For **AV Titles**: Craft **explosively viral, keyword-saturated, and utterly irresistible titles**. They must be intensely suggestive, instantly spotlighting the core kink, actress archetype, or unique scenario (USP). Use language that *commands* attention and sparks immediate curiosity. **Abandon literalism; embody a top-tier AV title guru focused on maximum clicks and conversions.** For fragmented inputs (like '♯有点阴沉？♯但却是夜总会小姐♯潮吹失禁♯'), you MUST forge them into a **singular, electrifying main title OR a series of hard-hitting, SEO-optimized sub-headlines/taglines** in the [Target Language] that are both cohesive and magnetically marketable.\n" +
                        "- For **AV Actress Names/Profiles**: Use **established fan transliterations or iconic nicknames** in the [Target Language] where they exist. Otherwise, coin **alluring, unforgettable, and culturally resonant phonetic renderings or descriptive pseudonyms**. Profiles MUST be potent marketing blurbs, spotlighting their most erotic qualities, signature physical attributes, or character archetypes (e.g., 'innocent debutante with a shocking secret lust') in a way that's both intensely seductive and keyword-optimized for fan searches.\n" +
                        "- For **AV Introductions/Descriptions**: Architect these into **gripping, vividly explicit, and overwhelmingly seductive narrative teasers or plot synopses**. Escalate the erotic tension, deploy rich, evocative language (using precise [Target Language] AV terminology) to paint tantalizing pictures of key scenes, kinks, and interactions. Build unbearable anticipation. Pinpoint and amplify the video’s 'must-see' factor and its unique selling proposition, making it utterly distinct and irresistible.\n\n" +
                        "Your output MUST embody **elite, hyper-persuasive, SEO-driven AV ad copy**, meticulously engineered for the [Target Language] audience, compelling them to desperately seek out the content. **Ruthlessly eliminate any bland, literal, or lackluster phrasing.** Every word must be a weapon of seduction and conversion, selling the fantasy with maximum impact.";
                } else { // General text, not AV, not subtitle
                    specificGenreInstruction =
                        "If the [Source Text] possesses a distinct genre or style (e.g., news report, technical manual, casual conversation, formal marketing copy, literary prose, poetry, etc.), you must meticulously replicate that specific style using the most authentic and professional expressions appropriate for that genre in each target language.";
                }
            }

            String tempSystemContent = multiLangCorePreamble.replace("[SPECIFIC_GENRE_INSTRUCTION_PLACEHOLDER]", specificGenreInstruction);
            systemContent = tempSystemContent + outputFormatInstruction + additionalFormattingInfo + coreFinalSystemInstructions;

            systemMessageMap.put("content", systemContent);
            messages.add(systemMessageMap);

            Map<String, String> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            String userMessageContent = String.format(
                "Translate the following [Source Text] from its original language '%s' into the listed [Target Languages]. " +
                "Adhere strictly to all system instructions regarding accuracy, idiomatic style for the specific content type (especially making it enticing and SEO-friendly for AV content if applicable), cultural relevance, and output formatting for each language.\n\n" +
                "[Target Languages]: %s\n\n" +
                "[Source Text]:\n%s",
                sourceLanguage, targetLanguagesString, text
            );
            userMessage.put("content", userMessageContent);
            messages.add(userMessage);

            ObjectNode requestBody = mapper.createObjectNode();
            ArrayNode messagesNode = requestBody.putArray("messages");
            for (Map<String, String> message : messages) {
                ObjectNode messageNode = messagesNode.addObject();
                message.forEach(messageNode::put);
            }

            requestBody.put("max_tokens", 4090);
            requestBody.put("temperature", 0.3); // Increased temperature slightly for more "creative" and enticing marketing copy. Test between 0.2-0.5.

                        // Select client configuration and build URL for Azure OpenAI
            ClientConfig clientConfig = selectClient();
            String url = String.format("%s/openai/deployments/%s/chat/completions?api-version=%s",
                    clientConfig.getEndpoint(), clientConfig.getDeploymentId(), clientConfig.getApiVersion());

            logger.infof("Sending multi-language translation request to: {}. Target languages: {}. isAVContext: {}, isSubtitle: {}", url, targetLanguagesString, isAVContext, isSubtitle);
            // logger.debug("System Prompt: {}", systemContent); // Extremely long, use with caution

            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("api-key", clientConfig.getApiKey())
                    .post(RequestBody.create(requestBody.toString(), JSON))
                    .build();

            try (Response response = client.newCall(request).execute()) {
                String responseBodyString = response.body() != null ? response.body().string() : "No response body";

                if (!response.isSuccessful()) {
                    logger.errorf("Multi-language translation API request failed with code {}: {}",
                            response.code(), responseBodyString);
                    return "Translation failed with error code: " + response.code() + ", details: " + responseBodyString;
                }

                JsonNode rootNode = mapper.readTree(responseBodyString);
                if (rootNode.has("choices") && rootNode.get("choices").isArray() && !rootNode.get("choices").isEmpty()) {
                    JsonNode firstChoice = rootNode.get("choices").get(0);
                    if (firstChoice.has("message") && firstChoice.get("message").has("content")) {
                        return firstChoice.get("message").get("content").asText();
                    }
                }
                logger.errorf("Unexpected response format from multi-language translation: {}", responseBodyString);
                return "Translation failed: Unexpected response format";
            }

        } catch (java.net.SocketTimeoutException e) {
            logger.error("Connection timeout during multi-language translation request", e);
            return "Translation failed: Connection timeout - " + e.getMessage();
        } catch (java.net.ConnectException e) {
            logger.error("Connection error during multi-language translation request", e);
            return "Translation failed: Connection error - " + e.getMessage();
        } catch (java.io.EOFException e) {
            logger.error("EOF error during multi-language translation request - likely connection closed prematurely", e);
            return "Translation failed: Connection closed prematurely - " + e.getMessage();
        } catch (java.net.UnknownHostException e) {
            logger.error("DNS resolution error during multi-language translation request", e);
            return "Translation failed: DNS resolution error - " + e.getMessage();
        } catch (javax.net.ssl.SSLException e) {
            logger.error("SSL/TLS error during multi-language translation request", e);
            return "Translation failed: SSL/TLS error - " + e.getMessage();
        } catch (IOException e) {
            logger.error("I/O error during multi-language translation request", e);
            return "Translation failed: I/O error - " + e.getMessage();
        } catch (Exception e) {
            logger.error("Unexpected error during multi-language translation request", e);
            return "Translation failed: Unexpected error - " + e.getMessage();
        }

        // This code should never be reached as all paths above return a value
        // Adding a fallback return statement
    }


    /**
     * Translates large subtitle files by breaking them into chunks
     * based on subtitle entries, then combining the results
     */
    private String translateLargeSubtitle(String text, String sourceLanguage, String targetLanguage) {
        logger.info("Breaking large subtitle into chunks for translation - Total length: " + text.length());

        // Use a more reliable pattern to split subtitle entries
        // This regex looks for number patterns that typically start subtitle entries
        String[] entries = text.split("(\\n|^)(\\d+)(\\n|$)");

        // Filter out empty entries
        List<String> validEntries = new ArrayList<>();
        for (String entry : entries) {
            if (entry != null && !entry.trim().isEmpty()) {
                validEntries.add(entry.trim());
            }
        }

        logger.infof("Found %d subtitle entries to translate", validEntries.size());
        StringBuilder result = new StringBuilder();

        // Start with smaller chunks for more reliable translation
        int maxChunkSize = 1500; // Reduced chunk size for better reliability

        for (int i = 0; i < validEntries.size();) {
            StringBuilder chunk = new StringBuilder();
            int startIndex = i;

            // Build a chunk that doesn't exceed maxChunkSize
            while (i < validEntries.size() && chunk.length() < maxChunkSize) {
                // Add the subtitle entry number
                chunk.append(i + 1).append("\n");

                // Add the subtitle content
                chunk.append(validEntries.get(i)).append("\n\n");

                i++;
            }

            // If we created a valid chunk
            if (chunk.length() > 0) {
                logger.infof("Translating subtitle chunk %d to %d (of %d) - Size: %d chars",
                        startIndex + 1, i, validEntries.size(), chunk.length());

                // Retry logic for this chunk
                int retryCount = 0;
                int maxRetries = 3;
                int backoffMs = 2000; // Initial backoff
                boolean success = false;
                String translatedChunk = null;

                while (!success && retryCount <= maxRetries) {
                    try {
                        if (retryCount > 0) {
                            logger.infof("Retry attempt %d/%d after %d ms for chunk %d-%d", 
                                retryCount, maxRetries, backoffMs, startIndex + 1, i);
                            Thread.sleep(backoffMs);
                            // Exponential backoff with jitter
                            backoffMs = (int) (backoffMs * 1.5 + (Math.random() * 500));
                        }
                        
                        translatedChunk = translate(chunk.toString(), sourceLanguage, targetLanguage, true, true);

                        // Check if translation failed
                        if (translatedChunk.startsWith("Translation failed")) {
                            if (translatedChunk.contains("EOF") || 
                                translatedChunk.contains("unexpected end of stream") ||
                                translatedChunk.contains("Connection closed prematurely")) {
                                // Network-related errors - retry
                                logger.warn("Network error during translation, will retry: " + translatedChunk);
                                retryCount++;
                            } else {
                                // Other errors - reduce chunk size and retry
                                logger.warn("Translation failed for chunk. Reducing chunk size and retrying.");
                                maxChunkSize = Math.max(500, maxChunkSize / 2);
                                i = startIndex; // Retry from the beginning of this chunk
                                logger.infof("Reduced chunk size to max %d chars", maxChunkSize);
                                break; // Exit retry loop to try with smaller chunk
                            }
                        } else {
                            // Successfully translated this chunk
                            result.append(translatedChunk);
                            logger.infof("Successfully translated chunk %d-%d", startIndex + 1, i);
                            success = true;
                        }
                    } catch (InterruptedException e) {
                        logger.error("Retry interrupted: " + e.getMessage(), e);
                        Thread.currentThread().interrupt();
                        return "Translation failed: Retry interrupted - " + e.getMessage();
                    } catch (Exception e) {
                        // Handle all exceptions including network errors
                        logger.warn("Error during translation (will retry): " + e.getClass().getSimpleName() + ": " + e.getMessage(), e);
                        retryCount++;
                    }
                }
                
                // If we've exhausted all retries and still failed
                if (!success && retryCount > maxRetries) {
                    logger.error("Failed to translate chunk after " + maxRetries + " retries");
                    return "Translation failed: Maximum retries exceeded";
                }
            }
        }

        return result.toString();
    }

    /**
     * Translates normal text
     */
    public String translateText(String text, String sourceLanguage, String targetLanguage) {
        return translate(text, sourceLanguage, targetLanguage, false, true);
    }
    
    /**
     * 解析翻译结果字符串，将其转换为结构化的数据
     * 增强版：支持更多格式的解析，即使LLM返回不完全符合预期格式的结果
     * 
     * @param result 翻译API返回的原始结果字符串
     * @return 解析后的结构化数据，每个原始内容对应一个Map，其中key为语言名称，value为翻译结果
     */
    public Map<String, Map<String, String>> parseTranslationResult(String result) {
        Map<String, Map<String, String>> parsedResults = new HashMap<>();
        
        try {
            // 如果结果以错误开头，返回空集合
            if (result == null || result.startsWith("Translation failed:") || result.trim().isEmpty()) {
                logger.warn("翻译失败或返回空数据: " + (result != null ? result : "null"));
                return parsedResults;
            }
            
            // 先尝试使用标准格式解析
            List<Map<String, String>> results = parseBatchTranslationResponse(result, 1);
            
            // 如果标准格式解析失败，尝试备用解析方法
            if (results == null || results.isEmpty() || results.get(0).isEmpty()) {
                logger.info("标准格式解析失败，尝试备用解析方法");
                Map<String, String> fallbackResults = parseUnstructuredTranslation(result);
                
                if (fallbackResults != null && !fallbackResults.isEmpty()) {
                    results = Collections.singletonList(fallbackResults);
                    logger.info("备用解析成功获取了 " + fallbackResults.size() + " 组翻译");
                } else {
                    logger.warn("所有解析方法均失败，返回空数据");
                    return parsedResults;
                }
            }
            
            // 翻译服务只处理了一个文本，所以回答中只有一组语言对应的翻译
            Map<String, String> translationsByLanguage = results.get(0);
            parsedResults.put("translations", translationsByLanguage);
            
        } catch (Exception e) {
            logger.error("解析翻译结果时出错", e);
        }
        
        return parsedResults;
    }
    
    /**
     * 尝试解析非标准格式的翻译结果
     * 支持以下格式:
     * 1. "Language = Translation"
     * 2. "Language: Translation"
     * 3. "Language - Translation"
     * 4. "**Language**: Translation"
     * 5. 简单的 "# Language\nTranslation"
     * 
     * @param rawResponse 原始响应文本
     * @return 解析出的语言-翻译映射
     */
    private Map<String, String> parseUnstructuredTranslation(String rawResponse) {
        Map<String, String> translations = new HashMap<>();
        
        try {
            // 根据常见分隔符分割内容，处理可能的整块翻译结果
            String[] sections = rawResponse.split("(?:\\n\\s*\\n|\\n#{1,3}\\s*|---+\\s*|\\*\\*\\*+\\s*)");
            
            for (String section : sections) {
                if (section.trim().isEmpty()) continue;
                
                // 尝试多种格式模式匹配
                // 1. Language = Translation  或 Language: Translation 或 Language - Translation
                Pattern pattern1 = Pattern.compile("^\\s*([A-Za-z]+(?:\\s+[A-Za-z]+)*)\\s*[:=-]\\s*(.+)$", Pattern.MULTILINE);
                Matcher matcher1 = pattern1.matcher(section);
                
                // 2. **Language**: Translation 格式 (Markdown格式)
                Pattern pattern2 = Pattern.compile("\\*\\*([A-Za-z]+(?:\\s+[A-Za-z]+)*)\\*\\*\\s*[:=-]\\s*(.+)", Pattern.MULTILINE);
                Matcher matcher2 = pattern2.matcher(section);
                
                // 3. # Language\nTranslation 格式
                Pattern pattern3 = Pattern.compile("#\\s*([A-Za-z]+(?:\\s+[A-Za-z]+)*)\\s*\\n(.+)", Pattern.DOTALL);
                Matcher matcher3 = pattern3.matcher(section);
                
                if (matcher1.find()) {
                    String language = matcher1.group(1).trim();
                    String translation = matcher1.group(2).trim();
                    translations.put(language, translation);
                    logger.debug("匹配到格式1: " + language + " -> " + translation.substring(0, Math.min(translation.length(), 30)) + "...");
                } else if (matcher2.find()) {
                    String language = matcher2.group(1).trim();
                    String translation = matcher2.group(2).trim();
                    translations.put(language, translation);
                    logger.debug("匹配到格式2: " + language + " -> " + translation.substring(0, Math.min(translation.length(), 30)) + "...");
                } else if (matcher3.find()) {
                    String language = matcher3.group(1).trim();
                    String translation = matcher3.group(2).trim();
                    translations.put(language, translation);
                    logger.debug("匹配到格式3: " + language + " -> " + translation.substring(0, Math.min(translation.length(), 30)) + "...");
                }
            }
        } catch (Exception e) {
            logger.error("备用解析方法出错", e);
        }
        
        return translations;
    }

    /**
     * Select a client for the current request based on priority and rotation
     * @return Selected client configuration
     */
    private synchronized ClientConfig selectClient() {
        // Increment request counter for rotation
        int requestNum = requestCounter.incrementAndGet();

        // Get all available clients
        List<ClientConfig> clients = config.getAllClients();

        // If we only have one client, return it
        if (clients.size() == 1) {
            currentClient = clients.get(0);
            return currentClient;
        }

        // For now, use a simple round-robin selection among clients with the highest priority
        int index = (requestNum - 1) % clients.size();
        currentClient = clients.get(index);

        logger.infof("Selected client %s (group: %s, priority: %d) for request %d",
            currentClient.getId(), currentClient.getGroup(), currentClient.getPriority(), requestNum);

        return currentClient;
    }

    // Removed unused getCurrentClient() method

    /**
     * Get a client by group name
     * @param group Group name
     * @return Selected client configuration
     */
    public ClientConfig getClientByGroup(String group) {
        return config.getClientByGroup(group)
            .orElseGet(() -> config.getBestClient());
    }
}
