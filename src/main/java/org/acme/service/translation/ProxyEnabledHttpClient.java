package org.acme.service.translation;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.acme.config.ProxyConfig;
import org.acme.config.ProxyConfig.ProxyEntry;
import org.jboss.logging.Logger;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import okhttp3.Authenticator;
import okhttp3.ConnectionPool;
import okhttp3.Credentials;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.Route;
import okhttp3.Interceptor;

/**
 * A factory for creating OkHttpClient instances with proxy support.
 * This class manages the creation of HTTP clients that can use a proxy from a proxy pool.
 */
@ApplicationScoped
public class ProxyEnabledHttpClient {
    private static final Logger LOG = Logger.getLogger(ProxyEnabledHttpClient.class);
    
    @Inject
    ProxyConfig proxyConfig;
    
    // Default timeouts - increased for better reliability
    private static final int DEFAULT_CONNECT_TIMEOUT_SECONDS = 120;
    private static final int DEFAULT_WRITE_TIMEOUT_SECONDS = 120;
    private static final int DEFAULT_READ_TIMEOUT_SECONDS = 180;
    
    // Connection pool settings
    private static final int MAX_IDLE_CONNECTIONS = 5;
    private static final long KEEP_ALIVE_DURATION_MS = 300000; // 5 minutes
    
    // Retry settings
    private static final int MAX_RETRIES = 3;
    
    /**
     * Creates a default OkHttpClient with standard timeouts but no proxy.
     * 
     * @return A configured OkHttpClient instance
     */
    public OkHttpClient createDefaultClient() {
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
            .connectTimeout(DEFAULT_CONNECT_TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .writeTimeout(DEFAULT_WRITE_TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .readTimeout(DEFAULT_READ_TIMEOUT_SECONDS, TimeUnit.SECONDS)
            // Use HTTP/1.1 for consistency with proxy clients
            .protocols(java.util.Arrays.asList(okhttp3.Protocol.HTTP_1_1))
            // Connection pool for better performance
            .connectionPool(new ConnectionPool(MAX_IDLE_CONNECTIONS, KEEP_ALIVE_DURATION_MS, TimeUnit.MILLISECONDS))
            // Retry failed requests
            .addInterceptor(getRetryInterceptor())
            // Enable connection retries
            .retryOnConnectionFailure(true);
        
        // Add SSL certificate validation bypass
        applySslBypass(builder);
        
        return builder.build();
    }
    
    /**
     * Creates an interceptor that retries failed requests.
     * 
     * @return An OkHttp Interceptor for retrying requests
     */
    private Interceptor getRetryInterceptor() {
        return chain -> {
            Request request = chain.request();
            Response response = null;
            IOException exception = null;
            int tryCount = 0;
            
            while (tryCount < MAX_RETRIES) {
                try {
                    if (tryCount > 0) {
                        LOG.infof("Retrying request (attempt %d/%d): %s", tryCount + 1, MAX_RETRIES, request.url());
                        // Add a small delay before retrying
                        try {
                            Thread.sleep(1000 * tryCount); // Exponential backoff
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                    }
                    
                    response = chain.proceed(request);
                    
                    // If we get a successful response, return it
                    if (response.isSuccessful()) {
                        return response;
                    } else if (response.code() >= 500) {
                        // Server errors can be retried
                        LOG.warnf("Server error: %d for %s", response.code(), request.url());
                        response.close();
                    } else {
                        // Client errors should not be retried
                        return response;
                    }
                } catch (IOException e) {
                    LOG.warnf("Request failed: %s - %s", request.url(), e.getMessage());
                    exception = e;
                    if (response != null) {
                        response.close();
                    }
                }
                
                tryCount++;
            }
            
            // If we've exhausted our retries, throw the last exception
            if (exception != null) {
                throw exception;
            }
            
            // This should never happen, but just in case
            throw new IOException("Unexpected error after " + MAX_RETRIES + " retries");
        };
    }
    
    /**
     * Creates an OkHttpClient with the next available proxy from the pool.
     * If the proxy pool is empty, returns a default client with no proxy.
     * 
     * @return A configured OkHttpClient instance with proxy if available
     */
    public OkHttpClient createClientWithNextProxy() {
        Optional<ProxyEntry> proxyEntryOpt = proxyConfig.getNextProxy();
        
        if (proxyEntryOpt.isEmpty()) {
            LOG.info("No proxies available in pool, using direct connection");
            return createDefaultClient();
        }
        
        return createClientWithProxy(proxyEntryOpt.get());
    }
    
    /**
     * Creates an OkHttpClient with a proxy from the specified group.
     * If no proxy is found in the group, returns a client with the next available proxy.
     * 
     * @param group The proxy group name
     * @return A configured OkHttpClient instance with proxy if available
     */
    public OkHttpClient createClientWithProxyFromGroup(String group) {
        Optional<ProxyEntry> proxyEntryOpt = proxyConfig.getProxyByGroup(group);
        
        if (proxyEntryOpt.isEmpty()) {
            LOG.infof("No proxy found in group '%s', using next available proxy", group);
            return createClientWithNextProxy();
        }
        
        return createClientWithProxy(proxyEntryOpt.get());
    }
    
    /**
     * Creates an OkHttpClient with the specified proxy.
     * 
     * @param proxyEntry The proxy entry to use
     * @return A configured OkHttpClient instance with the specified proxy
     */
    public OkHttpClient createClientWithProxy(ProxyEntry proxyEntry) {
        LOG.infof("Creating HTTP client with proxy: %s", proxyEntry);
        
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
            .connectTimeout(DEFAULT_CONNECT_TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .writeTimeout(DEFAULT_WRITE_TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .readTimeout(DEFAULT_READ_TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyEntry.getHost(), proxyEntry.getPort())))
            // Disable HTTP/2 when using proxies to avoid protocol negotiation issues
            .protocols(java.util.Arrays.asList(okhttp3.Protocol.HTTP_1_1))
            // Connection pool for better performance
            .connectionPool(new ConnectionPool(MAX_IDLE_CONNECTIONS, KEEP_ALIVE_DURATION_MS, TimeUnit.MILLISECONDS))
            // Retry failed requests
            .addInterceptor(getRetryInterceptor())
            // Enable connection retries
            .retryOnConnectionFailure(true);
        
        // Add proxy authentication if credentials are provided
        if (proxyEntry.hasAuthentication()) {
            builder.proxyAuthenticator(new Authenticator() {
                @Override
                public Request authenticate(Route route, Response response) {
                    String credential = Credentials.basic(proxyEntry.getUsername(), proxyEntry.getPassword());
                    return response.request().newBuilder()
                        .header("Proxy-Authorization", credential)
                        .build();
                }
            });
        }
        
        // Add SSL certificate validation bypass
        applySslBypass(builder);
        
        return builder.build();
    }
    
    /**
     * Creates an OkHttpClient with custom timeouts and the next available proxy.
     * 
     * @param connectTimeoutSeconds Connection timeout in seconds
     * @param writeTimeoutSeconds Write timeout in seconds
     * @param readTimeoutSeconds Read timeout in seconds
     * @return A configured OkHttpClient instance with proxy if available
     */
    public OkHttpClient createClientWithCustomTimeouts(int connectTimeoutSeconds, int writeTimeoutSeconds, int readTimeoutSeconds) {
        Optional<ProxyEntry> proxyEntryOpt = proxyConfig.getNextProxy();
        
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
            .connectTimeout(connectTimeoutSeconds, TimeUnit.SECONDS)
            .writeTimeout(writeTimeoutSeconds, TimeUnit.SECONDS)
            .readTimeout(readTimeoutSeconds, TimeUnit.SECONDS)
            // Connection pool for better performance
            .connectionPool(new ConnectionPool(MAX_IDLE_CONNECTIONS, KEEP_ALIVE_DURATION_MS, TimeUnit.MILLISECONDS))
            // Retry failed requests
            .addInterceptor(getRetryInterceptor())
            // Enable connection retries
            .retryOnConnectionFailure(true);
        
        if (proxyEntryOpt.isPresent()) {
            ProxyEntry proxyEntry = proxyEntryOpt.get();
            LOG.infof("Creating HTTP client with custom timeouts and proxy: %s", proxyEntry);
            
            builder.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyEntry.getHost(), proxyEntry.getPort())))
                  // Disable HTTP/2 when using proxies to avoid protocol negotiation issues
                  .protocols(java.util.Arrays.asList(okhttp3.Protocol.HTTP_1_1));
            
            // Add proxy authentication if credentials are provided
            if (proxyEntry.hasAuthentication()) {
                builder.proxyAuthenticator(new Authenticator() {
                    @Override
                    public Request authenticate(Route route, Response response) {
                        String credential = Credentials.basic(proxyEntry.getUsername(), proxyEntry.getPassword());
                        return response.request().newBuilder()
                            .header("Proxy-Authorization", credential)
                            .build();
                    }
                });
            }
        } else {
            LOG.info("No proxies available in pool, using direct connection with custom timeouts");
            // Use HTTP/1.1 for consistency
            builder.protocols(java.util.Arrays.asList(okhttp3.Protocol.HTTP_1_1));
        }
        
        // Add SSL certificate validation bypass
        applySslBypass(builder);
        
        return builder.build();
    }
    
    /**
     * Apply SSL certificate validation bypass to an OkHttpClient.Builder
     * This will allow connections to servers with invalid or self-signed certificates
     * 
     * @param builder OkHttpClient.Builder to modify
     */
    private void applySslBypass(OkHttpClient.Builder builder) {
        try {
            // Create a trust manager that does not validate certificate chains
            final X509TrustManager trustAllCerts = new X509TrustManager() {
                @Override
                public void checkClientTrusted(X509Certificate[] chain, String authType) {
                    // No validation
                }

                @Override
                public void checkServerTrusted(X509Certificate[] chain, String authType) {
                    // No validation
                }

                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return new X509Certificate[0];
                }
            };

            // Install the all-trusting trust manager
            final SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, new TrustManager[]{trustAllCerts}, new SecureRandom());

            // Create an ssl socket factory with our all-trusting manager
            final SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();

            builder.sslSocketFactory(sslSocketFactory, trustAllCerts)
                   .hostnameVerifier((hostname, session) -> true); // Accept all hostnames
            
            LOG.debug("Applied SSL certificate validation bypass");
        } catch (Exception e) {
            LOG.error("Failed to apply SSL certificate validation bypass: " + e.getMessage(), e);
        }
    }
}
