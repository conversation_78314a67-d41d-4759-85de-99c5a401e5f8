package org.acme.service.browser;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import org.jboss.logging.Logger;

import com.microsoft.playwright.Browser;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.BrowserType;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.PlaywrightException;
import com.microsoft.playwright.Response;
import com.microsoft.playwright.options.LoadState;
import dev.langchain4j.exception.TimeoutException;
import io.quarkus.runtime.ShutdownEvent;
import io.quarkus.runtime.StartupEvent;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;

@ApplicationScoped
public class PlaywrightBrowserService {
    private static final Logger logger = Logger.getLogger(PlaywrightBrowserService.class);

    private static final int MAX_BROWSER_CONTEXTS = 7; // 最大浏览器上下文数量
    private static final int MAX_PAGES_PER_CONTEXT = 2; // 每个上下文的最大页面数
    private static final long CONTEXT_IDLE_TIMEOUT_MS = 60_000; // 60秒闲置超时
    private static final long CONTEXT_ACQUIRE_TIMEOUT_MS = 15_000; // 15秒获取上下文超时

    private Playwright playwright;
    private Browser browser;

    private final Semaphore contextSemaphore = new Semaphore(MAX_BROWSER_CONTEXTS, true); // 使用公平信号量

    private final ConcurrentHashMap<String, BrowserContextEntry> contextPool = new ConcurrentHashMap<>();
    private final AtomicInteger contextIdGenerator = new AtomicInteger(0);
    private volatile boolean isRunning = true; // 控制清理线程

    // 浏览器上下文条目
    private static class BrowserContextEntry {
        final String id;
        final BrowserContext context;
        final AtomicInteger activePages = new AtomicInteger(0);
        volatile long lastUsedTimestamp;

        BrowserContextEntry(String id, BrowserContext context) {
            this.id = id;
            this.context = context;
            this.lastUsedTimestamp = System.currentTimeMillis();
        }

        // 尝试增加页面计数 (原子操作)
        boolean tryIncrementPages() {
            while (true) {
                int current = activePages.get();
                if (current >= MAX_PAGES_PER_CONTEXT) {
                    return false; // 已满
                }
                if (activePages.compareAndSet(current, current + 1)) {
                    lastUsedTimestamp = System.currentTimeMillis();
                    return true; // 成功
                }
                // 如果 CAS 失败，循环重试
            }
        }

        // 减少页面计数
        void decrementPages() {
            activePages.decrementAndGet();
            lastUsedTimestamp = System.currentTimeMillis();
        }
    }

    // 可自动关闭的 Page 包装器
    public static class ManagedPage implements AutoCloseable {
        private final Page page;
        private final BrowserContextEntry contextEntry;
        private final PlaywrightBrowserService service;
        private boolean closed = false;

        ManagedPage(Page page, BrowserContextEntry contextEntry, PlaywrightBrowserService service) {
            this.page = page;
            this.contextEntry = contextEntry;
            this.service = service;
        }

        public Page getPage() {
            if (closed) {
                throw new IllegalStateException("Page is already closed.");
            }
            return page;
        }



        @Override
        public void close() {
            if (!closed) {
                closed = true;
                try {
                    page.close();
                    logger.debugf("Page closed in context %s", contextEntry.id);
                } catch (PlaywrightException e) {
                    logger.warnf("Error closing page in context %s: %s", contextEntry.id, e.getMessage());
                } finally {
                    service.releasePage(contextEntry);
                }
            }
        }
    }

    void onStart(@Observes StartupEvent ev) {
        logger.info("Initializing Playwright browser service...");
        try {
            playwright = Playwright.create();
            browser = playwright.chromium().launch(new BrowserType.LaunchOptions()
                    .setHeadless(true)
                    .setSlowMo(10));
            isRunning = true;
            startContextCleanupThread();
            logger.info("Playwright browser service initialized.");
        } catch (Exception e) {
            logger.error("Failed to initialize Playwright browser service", e);
            // Consider shutting down the app or retrying
        }
    }

    void onStop(@Observes ShutdownEvent ev) {
        logger.info("Shutting down Playwright browser service...");
        isRunning = false; // Signal cleanup thread to stop

        // Give cleanup thread a moment to finish, then interrupt if needed
        // (Alternatively, use a dedicated ExecutorService and shutdown)
        try { Thread.sleep(500); } catch (InterruptedException ignored) {}

        contextPool.forEach((id, entry) -> {
            try {
                logger.debugf("Closing context %s during shutdown.", id);
                entry.context.close();
            } catch (Exception e) {
                logger.warnf("Error closing context %s during shutdown: %s", id, e.getMessage());
            }
        });
        contextPool.clear();

        if (browser != null) {
            try { browser.close(); } catch (Exception e) { logger.warn("Error closing browser.", e); }
            browser = null;
        }
        if (playwright != null) {
            try { playwright.close(); } catch (Exception e) { logger.warn("Error closing playwright.", e); }
            playwright = null;
        }
        logger.info("Playwright browser service shutdown complete.");
    }

    /**
     * 模拟随机等待，模拟人类操作的时间间隔
     * 
     * @param minMs 最小等待时间（毫秒）
     * @param maxMs 最大等待时间（毫秒）
     */
    private void randomWait(int minMs, int maxMs) {
        try {
            int waitTime = minMs + (int) (Math.random() * (maxMs - minMs));
            logger.debugf("Random waiting for %d ms", waitTime);
            Thread.sleep(waitTime);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 模拟鼠标移动，在页面上随机移动鼠标
     * 
     * @param page Playwright页面对象
     */
    private void simulateMouseMovement(Page page) {
        try {
            // 获取页面尺寸
            int viewportWidth = page.viewportSize().width;
            int viewportHeight = page.viewportSize().height;

            // 执行2-4次随机鼠标移动
            int movements = 2 + (int) (Math.random() * 3);
            for (int i = 0; i < movements; i++) {
                int x = (int) (Math.random() * viewportWidth);
                int y = (int) (Math.random() * viewportHeight);

                page.mouse().move(x, y);
                randomWait(300, 800); // 移动之间的短暂停顿
            }
            logger.debugf("Simulated %d mouse movements", movements);
        } catch (Exception e) {
            logger.warnf("Failed to simulate mouse movement: %s", e.getMessage());
        }
    }

    /**
     * 模拟页面滚动，包括滚动到底部和中间随机停顿
     * 
     * @param page Playwright页面对象
     */
    private void simulateScrolling(Page page) {
        try {
            // 获取页面高度
            Object pageHeight = page.evaluate("document.body.scrollHeight");
            int height = 0;
            if (pageHeight instanceof Number) {
                height = ((Number) pageHeight).intValue();
            } else if (pageHeight instanceof String) {
                height = Integer.parseInt((String) pageHeight);
            }

            if (height <= 0) {
                logger.warn("Could not determine page height, using default value");
                height = 5000; // 默认高度
            }

            // 分段滚动，模拟人类阅读行为
            int segments = 4 + (int) (Math.random() * 3); // 4-6段
            int segmentHeight = height / segments;

            for (int i = 1; i <= segments; i++) {
                int scrollTo = i * segmentHeight;
                page.evaluate("window.scrollTo(0, " + scrollTo + ")");
                logger.debugf("Scrolled to position %d of %d", scrollTo, height);

                // 每次滚动后等待一段时间，模拟阅读
                randomWait(500, 1500);
            }

            // 最后确保滚动到底部
            page.evaluate("window.scrollTo(0, document.body.scrollHeight)");
            logger.debug("Scrolled to bottom of page");
        } catch (Exception e) {
            logger.warnf("Failed to simulate scrolling: %s", e.getMessage());
        }
    }
    
    private void startContextCleanupThread() {
        Thread cleanupThread = new Thread(() -> {
            logger.info("Browser context cleanup thread started.");
            while (isRunning && !Thread.currentThread().isInterrupted()) {
                try {
                    Thread.sleep(10_000); // Check every 10 seconds
                    cleanupIdleContexts();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    logger.info("Context cleanup thread interrupted.");
                    break;
                } catch (Exception e) {
                    logger.error("Error in context cleanup thread", e);
                }
            }
            logger.info("Browser context cleanup thread stopped.");
        }, "playwright-context-cleanup");
        cleanupThread.setDaemon(true);
        cleanupThread.start();
    }

    private void cleanupIdleContexts() {
        long now = System.currentTimeMillis();
        contextPool.entrySet().removeIf(entry -> {
            BrowserContextEntry contextEntry = entry.getValue();
            if (contextEntry.activePages.get() == 0 && (now - contextEntry.lastUsedTimestamp) > CONTEXT_IDLE_TIMEOUT_MS) {
                logger.infof("Closing idle browser context: %s", contextEntry.id);
                try {
                    contextEntry.context.close();
                } catch (Exception e) {
                    logger.warnf("Error closing idle browser context %s: %s", contextEntry.id, e.getMessage());
                } finally {
                    contextSemaphore.release(); // 释放信号量
                    logger.debugf("Semaphore released for context %s. Available: %d", contextEntry.id, contextSemaphore.availablePermits());
                }
                return true; // Remove from map
            }
            return false; // Keep in map
        });
        logger.debugf("Cleanup finished. Pool size: %d. Available permits: %d", contextPool.size(), contextSemaphore.availablePermits());
    }

    private BrowserContextEntry getBrowserContext() {
        if (browser == null || !isRunning) {
            throw new IllegalStateException("Browser service is not running or initialized.");
        }

        // 1. 尝试在现有上下文中找到一个可用的
        for (BrowserContextEntry entry : contextPool.values()) {
            if (entry.tryIncrementPages()) {
                logger.debugf("Reusing context %s. Active pages: %d", entry.id, entry.activePages.get());
                return entry;
            }
        }

        // 2. 尝试创建新上下文（如果信号量允许）
        logger.debugf("Trying to acquire semaphore. Available: %d", contextSemaphore.availablePermits());
        try {
            if (!contextSemaphore.tryAcquire(CONTEXT_ACQUIRE_TIMEOUT_MS, TimeUnit.MILLISECONDS)) {
                throw new TimeoutException("Failed to acquire browser context within " + CONTEXT_ACQUIRE_TIMEOUT_MS + "ms. Pool likely exhausted.");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Interrupted while waiting for browser context", e);
        } catch (TimeoutException e) {
            logger.error("Timeout acquiring semaphore. Pool size: " + contextPool.size());
            throw new RuntimeException(e);
        }

        // 成功获取信号量，创建新上下文
        BrowserContext context = null;
        String contextId = "context-" + contextIdGenerator.incrementAndGet();
        try {
            logger.infof("Creating new browser context: %s. Available permits: %d", contextId, contextSemaphore.availablePermits());
            context = browser.newContext(new Browser.NewContextOptions()
                    .setUserAgent("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36")
                    .setViewportSize(1280, 720));
                    // .setDeviceScaleFactor(1.0)); // 通常不需要手动设置

            BrowserContextEntry newEntry = new BrowserContextEntry(contextId, context);
            newEntry.tryIncrementPages(); // 必定成功，因为刚创建，计数为 1
            contextPool.put(contextId, newEntry);
            logger.infof("New context %s created and added to pool. Pool size: %d", contextId, contextPool.size());
            return newEntry;
        } catch (Exception e) {
            logger.errorf("Failed to create new browser context %s", contextId, e);
            if (context != null) {
                try { context.close(); } catch (Exception ce) { logger.warn("Error closing context after creation failure.", ce); }
            }
            contextSemaphore.release(); // 创建失败，必须释放信号量
            logger.debugf("Semaphore released due to creation failure. Available: %d", contextSemaphore.availablePermits());
            throw new RuntimeException("Failed to create new browser context", e);
        }
    }

    // 释放页面（由 ManagedPage.close() 调用）
    void releasePage(BrowserContextEntry contextEntry) {
        if (contextEntry != null) {
            contextEntry.decrementPages();
            logger.debugf("Page released in context %s. Active pages: %d", contextEntry.id, contextEntry.activePages.get());
        }
    }

    /**
     * 获取一个新的、受管理的页面。
     * 使用 try-with-resources 来确保页面被正确关闭和资源被释放。
     * <pre>
     * {@code
     * try (ManagedPage managedPage = playwrightBrowserService.newPage()) {
     * Page page = managedPage.getPage();
     * // ... 使用 page ...
     * } catch (Exception e) {
     * // ... 处理错误 ...
     * }
     * }
     * </pre>
     * @return ManagedPage 实例
     * @throws RuntimeException 如果无法获取或创建页面
     */
    public ManagedPage newPage() {
        BrowserContextEntry contextEntry = getBrowserContext();
        Page page = null;
        try {
            page = contextEntry.context.newPage();
            logger.debugf("New page created in context %s", contextEntry.id);
            return new ManagedPage(page, contextEntry, this);
        } catch (PlaywrightException e) {
            logger.errorf("Failed to create new page in context %s: %s", contextEntry.id, e.getMessage());
            // 如果创建页面失败，必须释放我们之前增加的计数
            releasePage(contextEntry);
            throw new RuntimeException("Failed to create new page", e);
        }
    }

    /**
     * 抓取指定 URL 的 HTML 内容。
     * 此方法内部处理页面的获取和释放。
     * @param url 要抓取的 URL
     * @return HTML 内容，如果失败则返回 null
     */
    public String fetchHtml(String url) {
        logger.infof("Fetching HTML for URL: %s", url);
        ManagedPage managedPage = null; // 声明在 try 外部
        try {
            managedPage = newPage(); // 赋值
            Page page = managedPage.getPage();
            final String contextId = managedPage.contextEntry.id; // 获取 Context ID

            logger.infof("Navigating to URL: %s with context %s", url, contextId);
            Response response = page.navigate(url, new Page.NavigateOptions().setTimeout(60000));

            if (response == null) {
                logger.errorf("Failed to navigate to URL (null response): %s in context %s", url, contextId);
                return null;
            }

            int status = response.status();
            logger.infof("Response: %d for URL: %s in context %s", status, url, contextId);

            if (status < 200 || status >= 400) {
                logger.warnf("Received non-success status code %d for URL: %s in context %s", status, url, contextId);
            }

            page.waitForLoadState(LoadState.DOMCONTENTLOADED, new Page.WaitForLoadStateOptions().setTimeout(30000));
        
        // 模拟人类操作：随机等待
        randomWait(1000, 3000);
        
        // 模拟人类操作：随机鼠标移动
        simulateMouseMovement(page);
        
        // 模拟人类操作：滚动页面
        simulateScrolling(page);
        
        // 再次等待以确保所有内容都已加载（懒加载内容）
        randomWait(1000, 2000);
        
        String content = page.content();
            logger.infof("Successfully fetched HTML from URL: %s (length: %d) in context %s", url, content.length(), contextId);
            return content;

        } catch (Exception e) {
            String contextId = (managedPage != null) ? managedPage.contextEntry.id : "UNKNOWN";
            String errorMessage = e.getMessage();
            if (errorMessage != null && errorMessage.contains("__adopt__: request-context")) {
                logger.errorf("FATAL Playwright Error (request-context) fetching %s in context %s: %s", url, contextId, errorMessage, e);
                // 考虑在这里实施更激进的措施，比如标记 contextEntry 为 'poisoned'
            } else if (errorMessage != null && errorMessage.toLowerCase().contains("context or page has been closed")) {
                logger.errorf("Critical Error: Context/Page closed unexpectedly while fetching %s in context %s: %s", url, contextId, errorMessage, e);
            } else {
                logger.errorf("Error fetching HTML from URL %s in context %s: %s", url, contextId, e.getMessage(), e);
            }
            return null;
        } finally {
            if (managedPage != null) {
                managedPage.close(); // 确保 close 被调用
            }
        }
    }
}
