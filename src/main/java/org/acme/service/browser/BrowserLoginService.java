package org.acme.service.browser;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import org.jboss.logging.Logger;

import com.microsoft.playwright.Browser;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.BrowserType;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.Cookie;

import jakarta.enterprise.context.ApplicationScoped;

/**
 * Service for managing browser login and cookie extraction
 * Automates login and retrieves cookies for authenticated requests
 */
@ApplicationScoped
public class BrowserLoginService {

    private static final Logger logger = Logger.getLogger(BrowserLoginService.class);
    
    private static final String LOGIN_URL = "https://123av.com/ja/ajax/user/signin";
    private static final String LOGIN_USERNAME = "kongqy";
    private static final String LOGIN_PASSWORD = "12345";
    
    // Cache for cookie string
    private final AtomicReference<String> cachedCookieString = new AtomicReference<>("");
    private volatile Instant lastRefreshTime = Instant.EPOCH;
    
    /**
     * Gets the authentication cookies, using cached value if available
     * 
     * @return Cookie string for HTTP headers
     */
    public String getAuthCookies() {
        String cachedCookies = cachedCookieString.get();
        if (!cachedCookies.isEmpty()) {
            return cachedCookies;
        }
        
        return refreshAuthCookies();
    }
    
    /**
     * Force refresh of cookies by performing login again
     * 
     * @return Newly obtained cookie string
     */
    public synchronized String refreshAuthCookies() {
        logger.info("Starting headless browser login process");
        String cookieString = "";
        
        int retryCount = 0;
        int maxRetries = 3;
        int retryDelayMs = 5000; // 5 seconds between retries
        
        while (retryCount < maxRetries) {
            try (Playwright playwright = Playwright.create()) {
                Browser browser = playwright.chromium().launch(
                        new BrowserType.LaunchOptions()
                                .setHeadless(true)
                                .setTimeout(60000) // Increased timeout to 60 seconds
                );
                
                BrowserContext context = browser.newContext(
                        new Browser.NewContextOptions()
                                .setUserAgent("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36")
                                .setExtraHTTPHeaders(Map.of(
                                    "Accept-Language", "en-US,en;q=0.9",
                                    "Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8"
                                ))
                );
                
                // Set a longer navigation timeout
                context.setDefaultNavigationTimeout(60000);
                context.setDefaultTimeout(60000);
                
                Page page = context.newPage();
                
                // Navigate to the site's main page first to get initial cookies
                logger.info("Navigating to main site for initial cookies (Attempt " + (retryCount + 1) + ")");
                
                try {
                    // Use a more basic page first
                    page.navigate("https://123av.com/");
                    page.waitForLoadState();
                    
                    // Now navigate to the feed page
                    logger.info("Successfully loaded main page, now navigating to feed page");
                    page.navigate("https://123av.com/ja/user/feed?sort=recent_update");
                    page.waitForLoadState();
                } catch (Exception e) {
                    logger.warn("Navigation failed: " + e.getMessage() + ". Trying alternative approach.");
                    
                    // Fallback: use a normal navigation call instead of JS redirect
                    page.navigate("https://123av.com/ja/user/feed?sort=recent_update");
                    page.waitForLoadState();
                }
                
                // Extract cookies before login attempt
                List<Cookie> preCookies = context.cookies();
                String preCookieString = preCookies.stream()
                        .map(cookie -> cookie.name + "=" + cookie.value)
                        .collect(Collectors.joining("; "));
                logger.info("Pre-login cookies available: " + preCookieString);
                
                // Perform login via API call with the correct headers and cookies
                logger.info("Performing API login");
                String loginPayload = String.format("{\"username\":\"%s\",\"password\":\"%s\",\"remember_me\":1}", 
                        LOGIN_USERNAME, LOGIN_PASSWORD);
                
                // Use the format from the curl command
                @SuppressWarnings("unchecked")
                Map<String, Object> response = (Map<String, Object>) page.evaluate(
                        "([url, data, cookies]) => {\n" +
                        "  return fetch(url, {\n" +
                        "    method: 'POST',\n" +
                        "    headers: {\n" +
                        "      'Content-Type': 'application/json',\n" +
                        "      'accept': 'application/json, text/plain, */*',\n" +
                        "      'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7',\n" +
                        "      'origin': 'https://123av.com',\n" +
                        "      'referer': 'https://123av.com/ja/dm7',\n" +
                        "      'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',\n" +
                        "      'x-requested-with': 'XMLHttpRequest',\n" +
                        "      'Cookie': cookies\n" +
                        "    },\n" +
                        "    body: data,\n" +
                        "    credentials: 'include'\n" +
                        "  })\n" +
                        "  .then(response => response.json())\n" +
                        "  .catch(error => ({ error: error.toString() }));\n" +
                        "}", 
                        List.of(LOGIN_URL, loginPayload, preCookieString)
                );
                
                // Log response status
                if (response.containsKey("error") &&
                        response.get("error").toString().contains("Failed to fetch")) {
                    logger.debug("API login failed (headless blocked), continuing with page login flow");
                } else {
                    logger.info("Login response received: " + response);
                }
                
                // Navigate to user feed to ensure cookies are properly set
                logger.info("Navigating to user feed page after login");
                try {
                    page.navigate("https://123av.com/ja/user/feed?sort=recent_update");
                    page.waitForLoadState();
                } catch (Exception e) {
                    logger.warn("Navigation to feed page failed: " + e.getMessage());
                    // Fallback: use a normal navigation call instead of JS redirect
                    page.navigate("https://123av.com/ja/user/feed?sort=recent_update");
                    page.waitForLoadState();
                }
                
                // Extract cookies from browser
                List<Cookie> cookies = context.cookies();
                cookieString = cookies.stream()
                        .map(cookie -> cookie.name + "=" + cookie.value)
                        .collect(Collectors.joining("; "));
                
                if (!cookieString.isEmpty()) {
                    logger.info("Login successful, cookies retrieved");
                    browser.close();
                    
                    // Update cache
                    cachedCookieString.set(cookieString);
                    lastRefreshTime = Instant.now();
                    logger.info("Updated cookie cache at " + lastRefreshTime);
                    return cachedCookieString.get();
                } else {
                    logger.warn("No cookies retrieved, attempt " + (retryCount + 1) + " failed");
                }
                
                browser.close();
            } catch (Exception e) {
                logger.error("Error during browser login (Attempt " + (retryCount + 1) + "): " + e.getMessage(), e);
            }
            
            retryCount++;
            if (retryCount < maxRetries) {
                logger.info("Retrying in " + (retryDelayMs / 1000) + " seconds... (Attempt " + (retryCount + 1) + " of " + maxRetries + ")");
                try {
                    Thread.sleep(retryDelayMs);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        // If we exhaust all retries, return the cached cookie if available
        if (!cachedCookieString.get().isEmpty()) {
            logger.warn("Using last known good cookie after all retry attempts failed");
            return cachedCookieString.get();
        }
        
        logger.error("All attempts to refresh auth cookies failed");
        return "";
    }
    
    /**
     * Check if cached cookies need to be refreshed due to 401/unauthorized response
     * 
     * @return true if cookies were refreshed, false otherwise
     */
    public boolean handleUnauthorized() {
        logger.warn("Received unauthorized response. Refreshing cookies...");
        String refreshedCookies = refreshAuthCookies();
        return !refreshedCookies.isEmpty();
    }
    
    /**
     * Verify that the cookies actually authenticate by making a test request to a protected page
     * 
     * @param page The browser page to use for verification
     * @param cookieString The cookie string to verify
     * @return true if the cookies authenticate successfully, false otherwise
     */
    private boolean verifyCookieAuthentication(Page page, String cookieString) {
        logger.info("Verifying authentication cookies...");
        try {
            // Test access to the collection page which requires authentication
            String testUrl = "https://123av.com/ja/user/collection?sort=recent_update";
            
            // Test the cookies by evaluating if we can access a protected resource
            Object result = page.evaluate(
                "([url, cookies]) => {\n" +
                "  return fetch(url, {\n" +
                "    method: 'GET',\n" +
                "    headers: {\n" +
                "      'Cookie': cookies\n" +
                "    }\n" +
                "  })\n" +
                "  .then(response => ({\n" +
                "    ok: response.ok,\n" +
                "    status: response.status,\n" +
                "    redirected: response.redirected,\n" +
                "    url: response.url\n" +
                "  }))\n" +
                "  .catch(error => ({ error: error.toString() }));\n" +
                "}", 
                List.of(testUrl, cookieString)
            );
            
            if (result instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> responseData = (Map<String, Object>) result;
                
                // Log the verification result
                if (responseData.containsKey("error")) {
                    logger.warn("Cookie verification request failed: " + responseData.get("error"));
                    return false;
                }
                
                boolean ok = Boolean.TRUE.equals(responseData.get("ok"));
                int status = responseData.get("status") instanceof Number ? ((Number) responseData.get("status")).intValue() : 0;
                boolean redirected = Boolean.TRUE.equals(responseData.get("redirected"));
                String finalUrl = responseData.get("url") != null ? responseData.get("url").toString() : "";
                
                logger.info("Cookie verification result - OK: " + ok + ", Status: " + status + ", Redirected: " + redirected);
                
                // Cookies are good if we get a 200 OK response
                if (ok && status == 200) {
                    logger.info("Cookie authentication verified successfully");
                    return true;
                }
                
                // If we're redirected to the login page, that means the cookies didn't work
                if (redirected && finalUrl != null && finalUrl.contains("/signin")) {
                    logger.warn("Cookie verification failed - redirected to login page");
                    return false;
                }
                
                // Any other 401 or 403 response means authentication failed
                if (status == 401 || status == 403) {
                    logger.warn("Cookie verification failed - unauthorized status code: " + status);
                    return false;
                }
            }
            
            // Default to false if we can't verify
            logger.warn("Cookie verification inconclusive");
            return false;
            
        } catch (Exception e) {
            logger.error("Error verifying cookie authentication: " + e.getMessage(), e);
            return false;
        }
    }
}
