package org.acme.service;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.acme.entity.Movie;
import org.acme.entity.MovieInfo;
import org.acme.entity.VideoSource;
import org.acme.enums.Av123LanguageCode;
import org.acme.enums.MissAvLanguageCode;
import org.acme.service.extractor.MovieInfoExtractor;
import org.acme.util.MovieInfoExtractorUtil;
import org.jsoup.Jsoup;

import io.quarkus.logging.Log;
import io.smallrye.common.annotation.Blocking;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.acme.config.AuthCookieConfig;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import java.util.ArrayList;
import com.cronutils.utils.StringUtils;

@ApplicationScoped
public class MovieInfoExtractionService {

    @Inject
    MovieInfoExtractor movieInfoExtractor;

    @Inject
    MovieDetailCrawlerService movieDetailCrawlerService;

    @Inject
    AuthCookieConfig authCookieConfig;

    // Using LanguageCode enum for language mapping

    // Base URL
    private static final String BASE_URL_MISSAV = "https://missav.ai/";
    private static final String BASE_URL_123 = "https://123av.com/";
    /**
     * Gets the URL for a specific DVD code and language
     *
     * @param dvdCode The DVD code
     * @param languageCode The language code (empty for default)
     * @return The full URL
     */
    private static String getMissavLanguageUrl(String dvdCode, String languageCode) {
        if (languageCode.isEmpty()) {
            return BASE_URL_MISSAV + dvdCode;
        } else {
            return BASE_URL_MISSAV + languageCode + "/" + dvdCode;
        }
    }

    /**
     * 使用123av网站爬取多语言电影数据并封装成MovieInfo对象
     *
     * @param movieCode 电影代码
     * @return 多语言的MovieInfo列表
     */
    public List<MovieInfo> extract123AvMovieLinks(String movieLink, String movieCode, String languageCode) {
        List<MovieInfo> results = new ArrayList<>();
        UUID movieUuid = UUID.randomUUID(); // 生成一个共享的UUID用于所有语言版本

        try {
            // 构建特定语言的URL
            String prefix = BASE_URL_123;
            if (!languageCode.isEmpty()) {
                prefix += languageCode + "/";
            }
            String url = prefix + movieLink;
            // 获取网页内容
            String htmlContent = fetchHtmlContent(url);
            if (htmlContent == null || htmlContent.isEmpty()) {
                Log.warnf("Failed to fetch HTML content for language %s", languageCode);
                return null;
            }

            // 提取电影链接
            Movie movie = new Movie();
            movie.setLink(url);
            movie = movieDetailCrawlerService.processMovie(movie);

            if (movie == null) {
                Log.infof("No movies found for language %s", languageCode);
                return null;
            }
            // 创建MovieInfo对象
            MovieInfo movieInfo = new MovieInfo();
            movieInfo.code = movieCode.toLowerCase();
            movieInfo.movieUuid = movieUuid;
            movieInfo.language = languageCode;
            movieInfo.title = movie.title;
            movieInfo.description = movie.description;
            movieInfo.coverUrl = movie.coverImageUrl;

            if (movie.actresses != null) {
                movieInfo.actresses = movie.actresses;
            } else {
                movieInfo.actresses = Collections.emptyList();
            }

            if (movie.genres != null) {
                movieInfo.genres = movie.genres;
                movieInfo.tags = movie.genres; // 使用genres作为初始tags
            } else {
                movieInfo.genres = Collections.emptyList();
                movieInfo.tags = Collections.emptyList();
            }

            movieInfo.director = movie.director;
            movieInfo.maker = movie.maker;
            movieInfo.series = movie.series;
            movieInfo.source = VideoSource.AV123.getValue();

            if (movie.releaseDate != null) {
                movieInfo.releaseDate = parseDate(movie.releaseDate);
            }

            // if (movie.duration != null && !movie.duration.isEmpty()) {
            //     try {
            //         movieInfo.duration = Integer.parseInt(movie.duration.replaceAll("\\D+", ""));
            //     } catch (NumberFormatException e) {
            //         Log.warnf("Could not parse duration: %s", movie.duration);
            //     }
            // }

            results.add(movieInfo);
            Log.infof("Created MovieInfo for movie %s in language %s", movieCode, languageCode);

        } catch (Exception e) {
            Log.errorf("Error extracting movie info for language %s: %s", languageCode, e.getMessage());
        }
        return results;
    }

    /**
     * 使用123网站爬取多语言电影数据并保存到数据库
     *
     * @param movieCode 电影代码
     * @return 保存的MovieInfo实体列表
     */
    @Transactional
    public List<MovieInfo> extract123AndSaveAllLanguages(String movieLink, String movieCode) {
        // 获取多语言的MovieInfo列表
        List<MovieInfo> extractedInfos = extract123AvMovieLinks(movieLink, movieCode, "ja");
        List<MovieInfo> savedEntities = new ArrayList<>();

        for (MovieInfo info : extractedInfos) {
            if (info != null) {
                // 保存到数据库
                info.persist();
                savedEntities.add(info);
                Log.infof("Saved MovieInfo for movie %s in language %s", movieCode, info.language);
            } else {
                Log.infof("MovieInfo for movie %s is null", movieCode);
            }
        }

        return savedEntities;
    }

    /**
     * 获取指定URL的HTML内容
     *
     * @param url 要获取的URL
     * @return HTML内容，如果失败则返回null
     */
    // OkHttpClient as a class field for reuse, with optimized settings
    private final OkHttpClient httpClient = new OkHttpClient.Builder()
            .connectTimeout(15, TimeUnit.SECONDS) // 减少连接超时时间
            .readTimeout(15, TimeUnit.SECONDS)    // 减少读取超时时间
            .writeTimeout(15, TimeUnit.SECONDS)   // 减少写入超时时间
            .retryOnConnectionFailure(true)       // 连接失败时重试
            .connectionPool(new okhttp3.ConnectionPool(5, 30, TimeUnit.SECONDS)) // 连接池配置
            .build();

    // 调整后的方法：支持自动跳转 "Click here to continue" 链接
    private String fetchHtmlContent(String url) throws IOException {
        String currentUrl = url;
        int maxRedirects = 3; // 防止无限重定向
        for (int i = 0; i < maxRedirects; i++) {
            Request request = new Request.Builder()
                    .url(currentUrl)
                    .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    Log.errorf("Error fetching HTML from %s: HTTP %s", currentUrl, response.code());
                    return null;
                }

                String html = response.body().string();
                String redirectionLink = checkForRedirectionLink(html);
                if (!StringUtils.isEmpty(redirectionLink)) {
                    Log.infof("Redirecting to: %s", redirectionLink);
                    currentUrl = redirectionLink.replace("/en/", "/ja/");
                } else {
                    return html; // 未找到目标链接，返回当前 HTML
                }
            } catch (Exception e) {
                Log.errorf("Error processing %s: %s", currentUrl, e.getMessage());
                return null;
            }
        }
        Log.errorf("Exceeded max redirects for URL: %s", url);
        return null;
    }
    
    /**
     * Extracts movie information for all supported languages and saves it to the database.
     * Uses a single headless browser instance for all languages to improve performance.
     *
     * @return A list of the created MovieInfo entities
     */
    @Transactional
    public List<MovieInfo> extractAndSaveAllLanguages(Movie movie) {
        // Extract the DVD code from the link URL
        String movieCode = movie.getCode();
        if (movieCode == null || movieCode.isEmpty()) {
            Log.errorf("Could not extract code from movie: %s", movie.getLink());
            return Collections.emptyList();
        }

        if (movieCode.contains("Uncensored-Leaked")) {
            return null;
        }

        // 提取基础代码，例如从 "rbd-301-uncensored-leaked" 提取 "rbd-301"
        String baseCode = extractBaseCode(movieCode);
        Log.infof("Extracted base code '%s' from movie code '%s'", baseCode, movieCode);

        List<MovieInfo> savedEntities = new ArrayList<>();
        UUID movieUuid = UUID.randomUUID(); // Generate a shared UUID for all language versions

        // 先查询 movie_info 对应 code 的数据，只对缺失的语言进行提取. 404的记录不进行提取
        // 同时也查询基础代码（例如 rbd-301）的数据，如果存在则直接使用
        List<MovieInfo> movieInfos = new ArrayList<>();
        Set<String> existingLanguages = new HashSet<>();

        // 首先查找完全匹配的记录
        List<MovieInfo> exactMatchInfos = MovieInfo.list("code = ?1", movieCode);
        if (exactMatchInfos != null && !exactMatchInfos.isEmpty()) {
            Log.infof("Found %d exact matches for movie code '%s'", exactMatchInfos.size(), movieCode);
            movieInfos.addAll(exactMatchInfos);
        } else if (!baseCode.equals(movieCode)) {
            // 如果没有完全匹配，且基础代码与原始代码不同，则查找基础代码的记录
            List<MovieInfo> baseCodeInfos = MovieInfo.list("code = ?1", baseCode);
            if (baseCodeInfos != null && !baseCodeInfos.isEmpty()) {
                Log.infof("Found %d matches for base code '%s'", baseCodeInfos.size(), baseCode);
                movieInfos.addAll(baseCodeInfos);

                // 如果找到基础代码的记录，我们可以选择复制这些记录并更新代码
                // 这里我们选择直接使用基础代码的记录，不创建新记录
                Log.infof("Using existing data for base code '%s' instead of fetching new data for '%s'", baseCode, movieCode);
                return baseCodeInfos; // 直接返回基础代码的记录
            }
        }

        // 收集已存在的语言代码
        if (!movieInfos.isEmpty()) {
            existingLanguages = movieInfos.stream().map(MovieInfo::getLanguage).collect(Collectors.toSet());
        }

        // 准备需要处理的语言列表和对应URL
        List<MissAvLanguageCode> languagesToProcess = new ArrayList<>();
        Map<MissAvLanguageCode, String> languageUrls = new HashMap<>();

        // 收集需要处理的语言和URL
        for (MissAvLanguageCode languageCode : MissAvLanguageCode.values()) {
            String languageDbCode = languageCode.getDbCode();
            String languagePathCode = languageCode.getPathCode();

            if (existingLanguages.contains(languageDbCode)) {
                Log.infof("Language %s already exists for movie %s, skipping extraction", languageDbCode, movieCode);
                continue;
            }

            languagesToProcess.add(languageCode);
            String url = getMissavLanguageUrl(movieCode.toLowerCase(), languagePathCode);
            languageUrls.put(languageCode, url);
        }

        if (languagesToProcess.isEmpty()) {
            Log.info("No languages to process for movie " + movieCode);
            return savedEntities;
        }

        Log.infof("Processing %d languages for movie %s in a single browser session", languagesToProcess.size(), movieCode);

        // todo
        languagesToProcess = Collections.singletonList(MissAvLanguageCode.JAPANESE);
        if (existingLanguages.contains(movieCode.toLowerCase())) {
            return null;
        }

        // 处理所有语言
        for (MissAvLanguageCode languageCode : languagesToProcess) {
            String languageDbCode = languageCode.getDbCode();
            String url = languageUrls.get(languageCode);

            // 提取信息
            Map<String, Object> extractedInfo;
            try {
                // 使用单一浏览器实例处理所有语言
                try {
                    extractedInfo = movieInfoExtractor.extractMissMovieInfoForLanguage(languageCode, url);
                    if ((extractedInfo == null) || extractedInfo.isEmpty() || extractedInfo.get("title") == null || extractedInfo.get("dvd_id") == null) {
                        Log.infof("Failed to extract info from MissAV for %s, language=%s, falling back to 123av", movieCode, languageDbCode);
                        extractedInfo = extract123AvInfo(movieCode, languageDbCode);
                    }
                } catch (Exception missAvEx) {
                    Log.warnf("Error extracting from MissAV for %s, language=%s: %s, falling back to 123av",
                            movieCode, languageDbCode, missAvEx.getMessage());
                    extractedInfo = extract123AvInfo(movieCode, languageDbCode);
                }
            } catch (Exception av123Ex) {
                // 如果从123av获取信息也失败，记录警告并继续
                Log.warnf("Failed to get info from 123av for %s, language=%s: %s",
                        movieCode, languageDbCode, av123Ex.getMessage());
                continue;
            }

            if (extractedInfo != null) {
                extractedInfo.put("code", movieCode);
                MovieInfo movieInfo = createMovieInfoFromExtracted(extractedInfo, movieCode, movieUuid, languageDbCode);
//                movieInfo.persist();
                savedEntities.add(movieInfo);
                Log.info("Saved movie info for " + movieCode + " in language: " + languageDbCode);
            }
        }

        return savedEntities;
    }

    /**
     * 从123av网站获取电影信息
     *
     * @param movieCode 电影代码
     * @param languageDbCode 语言代码
     * @return 提取的电影信息
     */
    public Map<String, Object> extract123AvInfo(String movieCode, String languageDbCode) {
        try {
            Log.infof("Extracting info from 123av for %s, language=%s", movieCode, languageDbCode);

            // 构建URL
            String prefix = BASE_URL_123 + languageDbCode + "/v/";

            String url = prefix + movieCode.toLowerCase();
            Log.infof("Constructed URL: %s", url);

            // 获取HTML内容
            String htmlContent = fetchHtmlContent(url);
            if (htmlContent == null || htmlContent.isEmpty()) {
                Log.warnf("Failed to fetch HTML content for %s", url);
                return null;
            }

            // 优化性能：处理HTML内容，移除不必要部分并限制大小
            String processedHtml = htmlContent;
            
            // 移除CSS字体声明部分
            int fontFaceIndex = processedHtml.indexOf("@font-face");
            if (fontFaceIndex > 0) {
                Log.debugf("Removing font-face declarations from HTML");
                // 找到style标签的开始位置
                int styleStartIndex = processedHtml.lastIndexOf("<style", fontFaceIndex);
                if (styleStartIndex > 0) {
                    // 找到style标签的结束位置
                    int styleEndIndex = processedHtml.indexOf("</style>", fontFaceIndex);
                    if (styleEndIndex > 0) {
                        // 保留style前的内容和style后的内容，跳过font-face声明
                        processedHtml = processedHtml.substring(0, styleStartIndex) + 
                                       processedHtml.substring(styleEndIndex + 8);
                    }
                }
            }
            
            // 移除版权声明后的内容
            String copyrightMarker = "Copyright © 2025 123av.com All rights reserved";
            int copyrightIndex = processedHtml.indexOf(copyrightMarker);
            if (copyrightIndex > 0) {
                Log.debugf("Removing content after copyright notice");
                // 找到版权声明所在的HTML元素（通常是footer或div）
                int closingTagIndex = processedHtml.indexOf("</div>", copyrightIndex);
                if (closingTagIndex > 0) {
                    // 保留到版权声明所在div结束标签的内容
                    processedHtml = processedHtml.substring(0, closingTagIndex + 6);
                }
            }
            
            // 最后限制整体大小
            if (processedHtml.length() > 500000) {
                Log.warnf("HTML content still too large (%d bytes), truncating to 500000 bytes", processedHtml.length());
                processedHtml = processedHtml.substring(0, 500000);
            }
        
            // 使用工具类解析HTML内容
            Map<String, Object> extractedInfo = MovieInfoExtractorUtil.extract123AvInfo(processedHtml, url, languageDbCode);

            // 确保电影代码正确
            if (extractedInfo != null && !extractedInfo.containsKey("dvd_id")) {
                extractedInfo.put("dvd_id", movieCode);
            }

            return extractedInfo;
        } catch (Exception e) {
            Log.errorf("Error extracting info from 123av for %s, language=%s: %s",
                    movieCode, languageDbCode, e.getMessage());
            return null;
        }
    }

    /**
     * Creates a MovieInfo entity from extracted data.
     */
    public MovieInfo createMovieInfoFromExtracted(Map<String, Object> extractedInfo,
                                                  String movieCode,
                                                  UUID movieUuid,
                                                  String language) {
        MovieInfo movieInfo = new MovieInfo();

        // Set basic fields
        movieInfo.code = movieCode;
        movieInfo.movieUuid = movieUuid;
        movieInfo.language = language;

        // Set content fields
        movieInfo.title = (String) extractedInfo.get("title");
        movieInfo.description = (String) extractedInfo.get("description");

        // Handle m3u8 info
        @SuppressWarnings("unchecked")
        List<String> m3u8Info = (List<String>) extractedInfo.get("m3u8_info");
        movieInfo.m3u8Info = m3u8Info != null ? m3u8Info : Collections.emptyList();

        // Handle duration with support for HH:mm:ss format
        String durationStr = (String) extractedInfo.get("duration");
        if (durationStr != null && !durationStr.isEmpty()) {
            try {
                // Check if duration is in HH:mm:ss format
                if (durationStr.contains(":")) {
                    // Parse HH:mm:ss format to total minutes
                    String[] parts = durationStr.split(":");
                    if (parts.length == 3) {
                        // Format: HH:mm:ss
                        int hours = Integer.parseInt(parts[0]);
                        int minutes = Integer.parseInt(parts[1]);
                        int seconds = Integer.parseInt(parts[2]);
                        movieInfo.duration = hours * 60 + minutes + (seconds >= 30 ? 1 : 0); // Round up if >= 30 seconds
                    } else if (parts.length == 2) {
                        // Format: mm:ss
                        int minutes = Integer.parseInt(parts[0]);
                        int seconds = Integer.parseInt(parts[1]);
                        movieInfo.duration = minutes + (seconds >= 30 ? 1 : 0); // Round up if >= 30 seconds
                    }
                    Log.debugf("Parsed time format duration '%s' to %d minutes", durationStr, movieInfo.duration);
                } else {
                    // Parse as regular integer (minutes)
                    movieInfo.duration = Integer.parseInt(durationStr);
                    Log.debugf("Parsed numeric duration '%s' to %d minutes", durationStr, movieInfo.duration);
                }
            } catch (Exception e) {
                Log.warnf("Failed to parse duration '%s': %s", durationStr, e.getMessage());
                // Don't set duration if parsing fails
            }
        }

        // Handle dates
        movieInfo.websiteDate = parseDate((String) extractedInfo.get("website_date"));
        movieInfo.releaseDate = parseDate((String) extractedInfo.get("release_date"));

        // Handle cover URL
        movieInfo.coverUrl = (String) extractedInfo.get("cover_url");

        // Handle metadata fields
        movieInfo.series = (String) extractedInfo.get("series");
        movieInfo.label = (String) extractedInfo.get("label");
        movieInfo.maker = (String) extractedInfo.get("maker");
        movieInfo.director = (String) extractedInfo.get("director"); // Now using the proper director field

        // Handle lists
        // Actress field
        List<String> actressStr = new ArrayList<>();
        actressStr = (List<String>) extractedInfo.get("actresses");
        if (actressStr != null && !actressStr.isEmpty()) {
            movieInfo.actresses = actressStr;
        } else {
            movieInfo.actresses = Collections.emptyList();
        }

        String magnets = (String) extractedInfo.get("magnets");
        movieInfo.setMagnets(magnets);
        // Genres
        @SuppressWarnings("unchecked")
        List<String> genres = (List<String>) extractedInfo.get("genres");
        if (genres != null) {
            movieInfo.genres = genres;
        } else {
            movieInfo.genres = Collections.emptyList();
        }

        // Tags (can be same as genres initially)
        movieInfo.tags = movieInfo.genres;

        return movieInfo;
    }


    /**
     * 从完整的电影代码中提取基础代码
     * 例如：从 "rbd-301-uncensored-leaked" 提取 "rbd-301"
     *
     * @param fullCode 完整的电影代码
     * @return 基础电影代码
     */
    private String extractBaseCode(String fullCode) {
        if (fullCode == null || fullCode.isEmpty()) {
            return fullCode;
        }

        // 常见的DVD代码格式为：字母-数字，例如 rbd-301, ipx-789 等
        // 使用正则表达式匹配基础代码部分
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("([a-zA-Z]+-\\d+)");
        java.util.regex.Matcher matcher = pattern.matcher(fullCode);

        if (matcher.find()) {
            String baseCode = matcher.group(1).toLowerCase();
            Log.debugf("Extracted base code '%s' from '%s'", baseCode, fullCode);
            return baseCode;
        }

        // 如果无法提取基础代码，则返回原始代码
        return fullCode;
    }

    /**
     * 检查HTML内容中是否包含重定向链接
     * 例如：<div id="body">
     * <div class="text-center align-content-center">
     * <h3><a class="btn btn-primary" href="https://123av.com/en/dm4/v/sqte-409">Click here to continue</a></h3>
     * </div>
     * </div>
     *
     * @param htmlContent HTML内容
     * @return 如果找到重定向链接则返回新链接，否则返回null
     */
    private String checkForRedirectionLink(String htmlContent) {
        if (htmlContent == null || htmlContent.isEmpty()) {
            return null;
        }

        Document doc = Jsoup.parse(htmlContent); // 解析时保留基准 URL（关键！）
        Element continueLink = doc.selectFirst("a:contains(Click here to continue)");

        // 若找到目标链接，更新当前 URL 继续请求
        if (continueLink != null) {
            String newHref = continueLink.attr("abs:href"); // 使用 abs:href 确保绝对路径
            Log.infof("Redirecting to: %s", newHref);
            return newHref;
        }
        return null;
    }



    /**
     * Parses a date string into a LocalDate object.
     *
     * @param dateStr The date string to parse
     * @return The parsed LocalDate, or null if parsing fails
     */
    private LocalDate parseDate(String dateStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return null;
        }

        // Try different date formats
        String[] dateFormats = {
                "yyyy-MM-dd",
                "yyyy/MM/dd",
                "dd-MM-yyyy",
                "dd/MM/yyyy",
                "MM-dd-yyyy",
                "MM/dd/yyyy"
        };

        for (String format : dateFormats) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
                return LocalDate.parse(dateStr, formatter);
            } catch (DateTimeParseException e) {
                // Try next format
            }
        }

        Log.warn("Could not parse date: " + dateStr);
        return null;
    }


    @Transactional
    public void extractJAMovieInfoAndSave(Movie movie) {
        // Extract the DVD code from the link URL
        String movieCode = movie.getCode();
        if (movieCode == null || movieCode.isEmpty() 
        || movieCode.toLowerCase().contains("uncensored-leaked")
        || movieCode.toLowerCase().contains("[検閲なしリーク]")) {
            Log.errorf("Could not extract code from movie: %s", movie.getLink());
            return;
        }

        UUID movieUuid = movie.getMovieUuid(); // Generate a shared UUID for all language versions
        // 提取信息
        try {
            Map<String, Object> extractedInfo = extract123AvInfo(movieCode, "ja");
            if (extractedInfo != null) {
                extractedInfo.put("code", movieCode);
                MovieInfo movieInfo = createMovieInfoFromExtracted(extractedInfo, movieCode, movieUuid, "ja");
                movieInfo.titleStatus = "new";
                movieInfo.descriptionStatus = "new";
                movieInfo.persist();
                Log.info("Saved movie info for " + movieCode + " in ja");
            }
        } catch (Exception av123Ex) {
            // 如果从123av获取信息失败，记录警告并继续
            Log.warnf("Failed to get info from 123av for %s, language=%s: %s",
                    movieCode, "ja", av123Ex.getMessage());
            return;
        }

       
    }

}
