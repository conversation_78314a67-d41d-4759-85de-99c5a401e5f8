package org.acme.enums;

public enum WatchUrlStatus {
    NEW("new"),
    PROCESSING("processing"),
    NO_RESULT("noResult"),
    FAILED("failed"),
    SUCCEED("succeed"),
    ONLINE("online"),
    OFFLINE("offline");

    private final String value;
    
    /**
     * Convert a string value to the corresponding enum constant
     * @param value The string value to convert
     * @return The matching enum constant or null if no match is found
     */
    public static WatchUrlStatus fromValue(String value) {
        for (WatchUrlStatus status : values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        return null; // No matching constant found
    }

    WatchUrlStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }

    public boolean noNeedRetry() {
        return this != NEW && this != PROCESSING;
    }
}
