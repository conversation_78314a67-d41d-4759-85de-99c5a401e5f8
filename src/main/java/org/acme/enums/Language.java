package org.acme.enums;


import io.quarkus.runtime.annotations.RegisterForReflection;

@RegisterForReflection
public enum Language {
    ZH("zh", "简体中文", "🇨🇳"),
    ZHHANT("zh-Hant", "繁體中文", "🇭🇰"),
    EN("en", "English", "🇺🇸"),
    ES("es", "Español", "🇪🇸"),
    PT("pt", "Português", "🇵🇹"),
    FR("fr", "Français", "🇫🇷"),
    DE("de", "Deutsch", "🇩🇪"),
    IT("it", "Italiano", "🇮🇹"),
    JA("ja", "日本語", "🇯🇵"),
    KO("ko", "한국어", "🇰🇷"),
    HI("hi", "हिन्दी", "🇮🇳"),
    VI("vi", "Tiếng Việt", "🇻🇳"),
    ID("id", "Bahasa Indonesia", "🇮🇩"),
    RU("ru", "Русский", "🇷🇺"),
    TR("tr", "<PERSON><PERSON>rk<PERSON>e", "🇹🇷"),
    AR("ar", "العربية", "🇦🇪");

    private final String code;
    private final String name;
    private final String flag;

    Language(String code, String name, String flag) {
        this.code = code;
        this.name = name;
        this.flag = flag;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getFlag() {
        return flag;
    }

    public static Language fromCode(String code) {
        for (Language lang : values()) {
            if (lang.code.equals(code)) {
                return lang;
            }
        }
        return null;
    }
}