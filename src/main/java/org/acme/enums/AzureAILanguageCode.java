package org.acme.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public enum AzureAILanguageCode {
    /**
     * "Russian", "Turkish", "Arabic", "Dutch", "Swedish", "Polish", "Danish", "Norwegian", "Finnish",
     * "Traditional Chinese", "Simplified Chinese", "English", "Korean", "Malay", "Thai",
     * "German", "French", "Vietnamese", "Indonesian", "Filipino", "Portuguese", "Hindi"
     */
    JAPANESE("Japanese", "ja", "ja-JP"),
    RUSSIAN("Russian", "ru", "ru-RU"),
    TURKISH("Turkish", "tr", "tr-TR"),
    ARABIC("Arabic", "ar", "ar-SA"),
    DUTCH("Dutch", "nl", "nl-NL"),
    SWEDISH("Swedish", "sv", "sv-SE"),
    POLISH("Polish", "pl", "pl-PL"),
    DANISH("Danish", "da", "da-DK"),
    NORWEGIAN("Norwegian", "no", "nb-NO"),
    FINNISH("Finnish", "fi", "fi-FI"),
    TRADITIONAL_CHINESE("Traditional Chinese", "zh-Hant", "zh-TW"),
    SIMPLIFIED_CHINESE("Simplified Chinese", "zh-Hans", "zh-CN"),
    ENGLISH("English", "en", "en-US"),
    KOREAN("Korean", "ko", "ko-KR"),
    MALAY("Malay", "ms", "ms-MY"),
    THAI("Thai", "th", "th-TH"),
    GERMAN("German", "de", "de-DE"),
    FRENCH("French", "fr", "fr-FR"),
    VIETNAMESE("Vietnamese", "vi", "vi-VN"),
    INDONESIAN("Indonesian", "id", "id-ID"),
    FILIPINO("Filipino", "fil", "fil-PH"),
    PORTUGUESE("Portuguese", "pt", "pt-PT"),
    HINDI("Hindi", "hi", "hi-IN");

    private final String displayName;  // 显示名称
    private final String languageCode; // 语言代码
    private final String localeCode;   // 区域代码

    // 缓存用于快速查找
    private static final Map<String, AzureAILanguageCode> BY_DISPLAY_NAME =
            Arrays.stream(values())
                  .collect(Collectors.toMap(AzureAILanguageCode::getDisplayName, Function.identity()));

    private static final Map<String, AzureAILanguageCode> BY_LANGUAGE_CODE =
            Arrays.stream(values())
                  .collect(Collectors.toMap(AzureAILanguageCode::getLanguageCode, Function.identity()));

    AzureAILanguageCode(String displayName, String languageCode, String localeCode) {
        this.displayName = displayName;
        this.languageCode = languageCode;
        this.localeCode = localeCode;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getLanguageCode() {
        return languageCode;
    }

    public String getLocaleCode() {
        return localeCode;
    }

    /**
     * 根据显示名称获取枚举
     */
    public static AzureAILanguageCode fromDisplayName(String displayName) {
        return BY_DISPLAY_NAME.get(displayName);
    }

    /**
     * 根据语言代码获取枚举
     */
    public static AzureAILanguageCode fromLanguageCode(String languageCode) {
        return BY_LANGUAGE_CODE.get(languageCode);
    }

    /**
     * 获取所有支持的目标语言代码
     */
    public static List<String> getSupportedTargetLanguages() {
        return Arrays.stream(values())
                .map(AzureAILanguageCode::getDisplayName)
                .filter(name -> !name.equals("Chinese (Simplified)") &&
                               !name.equals("Chinese (Traditional)"))
                .collect(Collectors.toList());
    }

    /**
     * 获取所有支持的语言代码（用于API调用）
     */
    public static List<String> getSupportedLanguageCodes() {
        return Arrays.stream(values())
                .map(AzureAILanguageCode::getLanguageCode)
                .collect(Collectors.toList());
    }

    public static List<AzureAILanguageCode> getSupportedLanguages() {
        return Arrays.stream(values())
                .collect(Collectors.toList());
    }

    public static List<String> getSupportedLanguageNames() {
        return Arrays.stream(values())
                .map(AzureAILanguageCode::getDisplayName)
                .collect(Collectors.toList());
    }

    /**
     * 检查是否是支持的语言
     */
    public static boolean isSupportedLanguage(String language) {
        return BY_DISPLAY_NAME.containsKey(language) ||
               BY_LANGUAGE_CODE.containsKey(language);
    }
}
