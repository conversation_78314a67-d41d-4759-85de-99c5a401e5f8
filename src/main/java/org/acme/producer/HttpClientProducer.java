package org.acme.producer;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Produces;
import okhttp3.OkHttpClient;
import java.util.concurrent.TimeUnit;

@ApplicationScoped
public class HttpClientProducer {
    
    @Produces
    @ApplicationScoped
    public OkHttpClient produceOkHttpClient() {
        return new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
    }
}
