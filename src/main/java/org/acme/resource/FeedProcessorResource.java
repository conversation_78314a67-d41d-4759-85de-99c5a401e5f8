package org.acme.resource;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;

import org.acme.scheduler.CollectionMoiveProcessWorker.TaskResponse;
import org.acme.service.feed.FeedService;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.jboss.logging.Logger;

import jakarta.enterprise.context.control.ActivateRequestContext;
import jakarta.inject.Inject;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

@Path("/api/feed")
@Produces(MediaType.APPLICATION_JSON)
public class FeedProcessorResource {

    private static final Logger logger = Logger.getLogger(FeedProcessorResource.class);
    private static volatile boolean isRunning = false;

    @Inject
    FeedService feedService;

    /**
     * Get total number of feed pages
     */
    @GET
    @Path("/pages")
    @Tag(name = "获取Feed页面总数")
    public Response getTotalPages() {
        try {
            int totalPages = feedService.getTotalFeedPages();
            return Response.ok(Map.of("total_pages", totalPages)).build();
        } catch (Exception e) {
            logger.errorf("Error getting total feed pages: %s", e.getMessage());
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity(new TaskResponse("Error: " + e.getMessage()))
                    .build();
        }
    }

    /**
     * Process feed movies
     */
    @POST
    @Path("/process")
    @Tag(name ="处理Feed页面中的最新视频",description = "从最新的页面获取视频ID，比对数据库中现有记录，处理新的视频")
    public Response processFeedMovies(@QueryParam("pages") @DefaultValue("5000") int pagesToFetch) {
        if (isRunning) {
            return Response.status(Response.Status.CONFLICT)
                    .entity(new TaskResponse("Feed processing task is already running"))
                    .build();
        }

        synchronized (FeedProcessorResource.class) {
            if (isRunning) {
                return Response.status(Response.Status.CONFLICT)
                        .entity(new TaskResponse("Feed processing task is already running"))
                        .build();
            }
            isRunning = true;
        }

        try {
            // Start a background task that properly handles transactions
            CompletableFuture.runAsync(() -> processFeedMoviesAsync(pagesToFetch), Executors.newSingleThreadExecutor());

            return Response.ok(new TaskResponse("Feed processing started. Processing " + pagesToFetch + " pages.")).build();
        } catch (Exception e) {
            isRunning = false;
            logger.errorf("Error starting feed processing: %s", e.getMessage());
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity(new TaskResponse("Error: " + e.getMessage()))
                    .build();
        }
    }

    /**
     * Check feed processing status
     */
    @GET
    @Path("/status")
    @Tag(name = "检查Feed处理任务状态")
    public Response getStatus() {
        return Response.ok(Map.of("running", isRunning)).build();
    }
    
    /**
     * Helper method to process feed movies with proper transaction context
     */
    @ActivateRequestContext
    protected void processFeedMoviesAsync(int pagesToFetch) {
        // 直接让异常冒出，交给 CompletableFuture 日志 or 监控捕获
        feedService.processFeedMovies(pagesToFetch);
    }
}
