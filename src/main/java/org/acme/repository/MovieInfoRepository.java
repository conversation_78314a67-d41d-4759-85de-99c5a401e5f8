package org.acme.repository;

import java.util.List;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;

@ApplicationScoped
public class MovieInfoRepository {

    @Inject
    EntityManager entityManager;

    public List<String> findMissingMovieCodes(int limit) {
        return entityManager.createQuery(
            "SELECT m.code " + //
            "FROM Movie m " + // Using entity name 'Movie' instead of table name 'movies'
            "WHERE NOT EXISTS (" + //
            "    SELECT 1 " + //
            "    FROM MovieInfo mi " + // Using entity name 'MovieInfo' instead of table name 'movie_info'
            "    WHERE mi.code = m.code " + //
            ") ORDER BY m.originalId", String.class) // Using field name 'originalId' instead of column name 'original_id'
            .setMaxResults(limit)
            .getResultList(); 
    }
}
