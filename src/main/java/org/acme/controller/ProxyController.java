package org.acme.controller;

import org.acme.config.ProxyConfig;
import org.acme.config.ProxyConfig.ProxySource;
import org.jboss.logging.Logger;

import jakarta.inject.Inject;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Map;

/**
 * REST controller for managing proxy settings
 */
@Path("/api/proxy")
public class ProxyController {
    private static final Logger LOG = Logger.getLogger(ProxyController.class);
    
    @Inject
    ProxyConfig proxyConfig;
    
    /**
     * Get the current proxy configuration
     * 
     * @return Current proxy configuration
     */
    @GET
    @Path("/config")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getProxyConfig() {
        Map<String, Object> config = proxyConfig.getProxyPoolConfig();
        return Response.ok(config).build();
    }
    
    /**
     * Switch between proxy sources (proxy pool or local VPN)
     * 
     * @param source "pool" for proxy pool or "vpn" for local VPN
     * @return Response with the updated configuration
     */
    @POST
    @Path("/source")
    @Produces(MediaType.APPLICATION_JSON)
    public Response switchProxySource(@QueryParam("source") String source) {
        try {
            if ("vpn".equalsIgnoreCase(source)) {
                proxyConfig.setProxySource(ProxySource.LOCAL_VPN);
                LOG.info("Switched to local VPN proxy (127.0.0.1:10802)");
            } else if ("pool".equalsIgnoreCase(source)) {
                proxyConfig.setProxySource(ProxySource.PROXY_POOL);
                LOG.info("Switched to proxy pool");
            } else {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("Invalid source. Use 'pool' or 'vpn'.")
                        .build();
            }
            
            return Response.ok(proxyConfig.getProxyPoolConfig()).build();
        } catch (Exception e) {
            LOG.error("Error switching proxy source", e);
            return Response.serverError()
                    .entity("Error: " + e.getMessage())
                    .build();
        }
    }
    
    /**
     * Get the current proxy source
     * 
     * @return Current proxy source (PROXY_POOL or LOCAL_VPN)
     */
    @GET
    @Path("/source")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getCurrentProxySource() {
        return Response.ok(Map.of(
                "source", proxyConfig.getProxySource(),
                "description", proxyConfig.getProxySource() == ProxySource.LOCAL_VPN ?
                        "Using local VPN proxy (127.0.0.1:10802)" : "Using proxy pool"
        )).build();
    }
}
