package org.acme.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import io.quarkus.arc.Arc;
import io.quarkus.arc.ArcContainer;
import io.quarkus.arc.ManagedContext;

import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import org.acme.entity.WatchUrl;
import org.acme.enums.WatchUrlStatus;
import org.acme.service.VideoIdCrawlerService;
import org.eclipse.microprofile.openapi.annotations.OpenAPIDefinition;
import org.eclipse.microprofile.openapi.annotations.info.Contact;
import org.eclipse.microprofile.openapi.annotations.info.Info;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.jboss.logging.Logger;

/**
 * Controller for managing the video ID crawler
 */
@Path("/crawler/video-id")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class VideoIdCrawlerController {

    private static final Logger logger = Logger.getLogger(VideoIdCrawlerController.class);
    private boolean isRunning = false;
    private static final int MAX_RETRIES = 3;
    private static final long DELAY_MS = 300; // 300ms delay between requests

    @Inject
    VideoIdCrawlerService videoIdCrawlerService;

    /**
     * Start the video ID crawler
     * @param startId The ID to start crawling from (optional)
     * @param endId The ID to end crawling at (optional)
     * @return Response indicating if the crawler was started
     */
    @POST
    @Path("/start")
    @Tag(name = "VideoIdCrawlerController-startCrawler",description = "视频m3u8爬虫，爬取视频ID从startId到endId,遇到已经存在的会跳过.并且爬取对应的 movies 如果不存在")
    public Response startCrawler(@QueryParam("startId") @DefaultValue("1") int startId,
                                @QueryParam("endId") @DefaultValue("200000") int endId) {
        if (isRunning) {
            return Response.status(Response.Status.CONFLICT)
                    .entity(new CrawlerResponse("Crawler is already running"))
                    .build();
        }

        // Set running flag
        isRunning = true;

        // Start the crawler in a separate thread to not block the response
        CompletableFuture.runAsync(() -> {
            try {
                logger.info("Starting crawler in background thread...");
                
                // Process IDs sequentially
                processVideoIds(startId, endId);
                
                logger.info("Video ID crawler completed successfully.");
            } catch (Exception e) {
                logger.errorf("Error during video ID processing: %s", e.getMessage());
                logger.error("Stack trace:", e);
            } finally {
                isRunning = false;
            }
        });

        return Response.ok(new CrawlerResponse("Video ID crawler started with optimized processing"))
                .build();
    }

    /**
     * Process video IDs sequentially with retries and delay
     * First processes non-continuous IDs (gaps) and then from max movie_id to endId
     * @param startId starting ID
     * @param endId ending ID
     */
    protected void processVideoIds(int startId, int endId) {
        logger.infof("Starting optimized processing from ID %d to %d", startId, endId);
        
        // Get Arc container to manually activate request context for database operations
        ArcContainer container = Arc.container();
        ManagedContext requestContext = container.requestContext();
        
        // Activate the request context for this part of the code
        if (!requestContext.isActive()) {
            requestContext.activate();
            logger.info("Activated request context for processing");
        }
        
        try {
            for (int index = startId; index <= endId; index++) {
                videoIdCrawlerService.processVideoById(index);
            }
            logger.infof("Completed all processing. Total: %d IDs processed");
            
        } catch (Exception e) {
            logger.errorf("Unexpected error during processing: %s", e.getMessage());
            logger.error("Stack trace:", e);
        } finally {
            // Make sure to terminate the request context we started
            if (requestContext != null && requestContext.isActive()) {
                requestContext.terminate();
                logger.info("Terminated request context for processing");
            }
        }
    }

    /**
     * Get the current status of the crawler
     * @return Response with crawler status
     */
    @GET
    @Path("/status")
    public Response getStatus() {
        return Response.ok(new CrawlerResponse(isRunning ? "Crawler is running" : "Crawler is not running"))
                .build();
    }

    /**
     * Stop the crawler if it's running
     * @return Response indicating if the crawler was stopped
     */
    @POST
    @Path("/stop")
    public Response stopCrawler() {
        if (!isRunning) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity(new CrawlerResponse("Crawler is not running"))
                    .build();
        }

        // Set the flag to false - the thread should check this and terminate gracefully
        isRunning = false;
        
        // Interrupt any running thread
        Thread.currentThread().interrupt();

        return Response.ok(new CrawlerResponse("Video ID crawler stopped"))
                .build();
    }

    /**
     * Simple response object
     */
    public static class CrawlerResponse {
        public String message;

        public CrawlerResponse(String message) {
            this.message = message;
        }
    }
}
