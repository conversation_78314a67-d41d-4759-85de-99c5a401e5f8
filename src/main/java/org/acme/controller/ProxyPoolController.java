package org.acme.controller;

import java.util.List;
import java.util.Map;

import org.acme.service.translation.ProxyPoolManager;
import org.jboss.logging.Logger;

import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.acme.config.ProxyConfig;

/**
 * REST controller for managing the proxy pool.
 */
@Path("/api/proxy-pool")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class ProxyPoolController {
    private static final Logger LOG = Logger.getLogger(ProxyPoolController.class);
    
    @Inject
    ProxyPoolManager proxyPoolManager;
    
    @Inject
    ProxyConfig proxyConfig;
    
    /**
     * Initialize the proxy pool with a list of proxies.
     * 
     * @param proxyList List of proxy specifications in format host:port:username:password:group:priority
     * @return Response with the number of successfully added proxies
     */
    @POST
    @Path("/initialize")
    public Response initializeProxyPool(List<String> proxyList) {
        try {
            int addedCount = proxyPoolManager.initializeProxyPool(proxyList);
            return Response.ok(Map.of(
                "success", true,
                "message", String.format("Added %d proxies to the pool", addedCount),
                "addedCount", addedCount
            )).build();
        } catch (Exception e) {
            LOG.error("Failed to initialize proxy pool", e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(Map.of(
                    "success", false,
                    "message", "Failed to initialize proxy pool: " + e.getMessage()
                ))
                .build();
        }
    }
    
    /**
     * Add a new proxy to the pool.
     * 
     * @param host Proxy host
     * @param port Proxy port
     * @param username Username for authentication (optional)
     * @param password Password for authentication (optional)
     * @param group Group name (optional, defaults to "default")
     * @param priority Priority (optional, defaults to 1)
     * @return Response indicating success or failure
     */
    @POST
    @Path("/add")
    public Response addProxy(
            @QueryParam("host") String host,
            @QueryParam("port") int port,
            @QueryParam("username") String username,
            @QueryParam("password") String password,
            @QueryParam("group") String group,
            @QueryParam("priority") Integer priority) {
        
        if (host == null || host.isEmpty()) {
            return Response.status(Response.Status.BAD_REQUEST)
                .entity(Map.of("success", false, "message", "Host is required"))
                .build();
        }
        
        if (port <= 0 || port > 65535) {
            return Response.status(Response.Status.BAD_REQUEST)
                .entity(Map.of("success", false, "message", "Invalid port number"))
                .build();
        }
        
        // Use default values if not provided
        String effectiveGroup = (group == null || group.isEmpty()) ? "default" : group;
        int effectivePriority = (priority == null) ? 1 : priority;
        
        try {
            boolean added = proxyPoolManager.addProxy(
                host, port, username, password, effectiveGroup, effectivePriority);
            
            if (added) {
                return Response.ok(Map.of(
                    "success", true,
                    "message", String.format("Added proxy %s:%d to the pool", host, port)
                )).build();
            } else {
                return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity(Map.of("success", false, "message", "Failed to add proxy"))
                    .build();
            }
        } catch (Exception e) {
            LOG.error("Failed to add proxy", e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(Map.of(
                    "success", false,
                    "message", "Failed to add proxy: " + e.getMessage()
                ))
                .build();
        }
    }
    
    /**
     * Test a proxy connection before adding it to the pool.
     * 
     * @param host Proxy host
     * @param port Proxy port
     * @param username Username for authentication (optional)
     * @param password Password for authentication (optional)
     * @return Response indicating success or failure
     */
    @GET
    @Path("/test")
    public Response testProxy(
            @QueryParam("host") String host,
            @QueryParam("port") int port,
            @QueryParam("username") String username,
            @QueryParam("password") String password) {
        
        if (host == null || host.isEmpty()) {
            return Response.status(Response.Status.BAD_REQUEST)
                .entity(Map.of("success", false, "message", "Host is required"))
                .build();
        }
        
        if (port <= 0 || port > 65535) {
            return Response.status(Response.Status.BAD_REQUEST)
                .entity(Map.of("success", false, "message", "Invalid port number"))
                .build();
        }
        
        try {
            boolean success = proxyPoolManager.testProxy(host, port, username, password);
            
            return Response.ok(Map.of(
                "success", success,
                "message", success 
                    ? String.format("Successfully connected to proxy %s:%d", host, port)
                    : String.format("Failed to connect to proxy %s:%d", host, port)
            )).build();
        } catch (Exception e) {
            LOG.error("Failed to test proxy", e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(Map.of(
                    "success", false,
                    "message", "Failed to test proxy: " + e.getMessage()
                ))
                .build();
        }
    }
    
    /**
     * Test all proxies in the pool.
     * 
     * @return Response with test results for all proxies
     */
    @GET
    @Path("/test-all")
    public Response testAllProxies() {
        try {
            Map<String, Boolean> results = proxyPoolManager.testAllProxies();
            
            long successCount = results.values().stream().filter(Boolean::booleanValue).count();
            long failCount = results.size() - successCount;
            
            return Response.ok(Map.of(
                "success", true,
                "message", String.format("Tested %d proxies: %d successful, %d failed", 
                    results.size(), successCount, failCount),
                "results", results
            )).build();
        } catch (Exception e) {
            LOG.error("Failed to test all proxies", e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(Map.of(
                    "success", false,
                    "message", "Failed to test all proxies: " + e.getMessage()
                ))
                .build();
        }
    }
    
    /**
     * Get statistics for all proxies in the pool.
     * 
     * @return Response with statistics for all proxies
     */
    @GET
    @Path("/stats")
    public Response getProxyStatistics() {
        try {
            Map<String, Map<String, Object>> statistics = proxyPoolManager.getProxyStatistics();
            
            return Response.ok(Map.of(
                "success", true,
                "statistics", statistics
            )).build();
        } catch (Exception e) {
            LOG.error("Failed to get proxy statistics", e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(Map.of(
                    "success", false,
                    "message", "Failed to get proxy statistics: " + e.getMessage()
                ))
                .build();
        }
    }
    
    /**
     * Get a summary of proxy pool health.
     * 
     * @return Response with summary statistics about the proxy pool
     */
    @GET
    @Path("/summary")
    public Response getProxyPoolSummary() {
        try {
            Map<String, Object> summary = proxyPoolManager.getProxyPoolSummary();
            
            return Response.ok(Map.of(
                "success", true,
                "summary", summary
            )).build();
        } catch (Exception e) {
            LOG.error("Failed to get proxy pool summary", e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(Map.of(
                    "success", false,
                    "message", "Failed to get proxy pool summary: " + e.getMessage()
                ))
                .build();
        }
    }
    
    /**
     * Manually refresh the proxy pool by fetching new international proxies.
     * 
     * @param count Optional number of proxies to fetch (defaults to configured minimum)
     * @return Response with the number of successfully added proxies
     */
    @POST
    @Path("/refresh")
    public Response refreshProxyPool(@QueryParam("count") Integer count) {
        try {
            int addedCount;
            
            if (count != null && count > 0) {
                // Fetch the specified number of proxies
                addedCount = proxyPoolManager.initializeProxyPool(
                    java.util.Collections.nCopies(count, "api:fetch:international"));
            } else {
                // Use the default refresh mechanism
                addedCount = proxyConfig.refreshProxyPool();
            }
            
            Map<String, Object> config = proxyConfig.getProxyPoolConfig();
            
            return Response.ok(Map.of(
                "success", true,
                "message", String.format("Added %d new proxies to the pool", addedCount),
                "addedCount", addedCount,
                "config", config
            )).build();
        } catch (Exception e) {
            LOG.error("Failed to refresh proxy pool", e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(Map.of(
                    "success", false,
                    "message", "Failed to refresh proxy pool: " + e.getMessage()
                ))
                .build();
        }
    }
}
