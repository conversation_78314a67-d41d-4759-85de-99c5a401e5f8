package org.acme.controller;


import org.acme.service.translation.TranslationService;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import jakarta.inject.Inject;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

@Path("/translate")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class TranslationController {

    @Inject
    TranslationService translationService;

    @POST
    @Path("/movieInfo")
    @Tag(name = "翻译MovieInfo 到 MovieInfoTranslation")
    public Response triggerTranslation(
            @QueryParam("batchSize") @DefaultValue("5") int batchSize
    ) {
        translationService.triggerTranslation(batchSize);
        return Response.accepted().entity("任务已启动").build();
    }
}
