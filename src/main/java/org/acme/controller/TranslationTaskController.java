package org.acme.controller;

import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import org.acme.entity.MovieInfo;
import org.acme.service.translation.TaskBasedTranslationService;
import org.acme.service.translation.TranslationTaskManager;
import org.jboss.logging.Logger;

import java.util.Map;

/**
 * 翻译任务管理控制器
 * 提供翻译任务相关的API端点
 */
@Path("/api/translation")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class TranslationTaskController {

    private static final Logger LOG = Logger.getLogger(TranslationTaskController.class);
    
    @Inject
    TranslationTaskManager taskManager;
    
    @Inject
    TaskBasedTranslationService translationService;
    
    /**
     * 手动触发翻译处理
     */
    @POST
    @Path("/trigger")
    public Response triggerTranslation(@QueryParam("batchSize") @DefaultValue("10") int batchSize) {
        try {
            LOG.info("手动触发翻译处理...");
            translationService.triggerTranslation(batchSize);
            return Response.ok().entity(Map.of("status", "success", "message", "翻译处理已启动")).build();
        } catch (Exception e) {
            LOG.error("触发翻译处理失败", e);
            return Response.serverError().entity(Map.of("status", "error", "message", e.getMessage())).build();
        }
    }
    
    /**
     * 获取翻译任务统计信息
     */
    @GET
    @Path("/stats")
    public Response getTaskStatistics() {
        try {
            Map<String, Long> stats = taskManager.getTaskStatistics();
            return Response.ok().entity(stats).build();
        } catch (Exception e) {
            LOG.error("获取翻译任务统计信息失败", e);
            return Response.serverError().entity(Map.of("status", "error", "message", e.getMessage())).build();
        }
    }
    
    /**
     * 重置失败和超时任务
     */
    @POST
    @Path("/reset-failed")
    public Response resetFailedTasks(@QueryParam("maxRetries") @DefaultValue("3") int maxRetries) {
        try {
            int resetCount = taskManager.resetFailedTasks(maxRetries);
            return Response.ok().entity(Map.of(
                "status", "success", 
                "message", "已重置 " + resetCount + " 个失败任务"
            )).build();
        } catch (Exception e) {
            LOG.error("重置失败任务出错", e);
            return Response.serverError().entity(Map.of("status", "error", "message", e.getMessage())).build();
        }
    }
    
    /**
     * 检查和处理超时任务
     */
    @POST
    @Path("/handle-timeout")
    public Response handleTimeoutTasks(@QueryParam("timeoutMinutes") @DefaultValue("10") int timeoutMinutes) {
        try {
            int timeoutCount = taskManager.processTimeoutTasks(timeoutMinutes);
            return Response.ok().entity(Map.of(
                "status", "success", 
                "message", "已处理 " + timeoutCount + " 个超时任务"
            )).build();
        } catch (Exception e) {
            LOG.error("处理超时任务出错", e);
            return Response.serverError().entity(Map.of("status", "error", "message", e.getMessage())).build();
        }
    }
    
    /**
     * 为单个内容创建翻译任务
     */
    @POST
    @Path("/create-tasks/{contentId}")
    public Response createTasksForContent(@PathParam("contentId") String contentId) {
        try {
            MovieInfo movie = MovieInfo.find("code", contentId).firstResult();
            if (movie == null) {
                return Response.status(Response.Status.NOT_FOUND)
                    .entity(Map.of("status", "error", "message", "内容不存在: " + contentId))
                    .build();
            }
            
            int taskCount = taskManager.createTasksForContent(movie);
            return Response.ok().entity(Map.of(
                "status", "success", 
                "message", "已为内容 " + contentId + " 创建 " + taskCount + " 个翻译任务"
            )).build();
        } catch (Exception e) {
            LOG.error("创建翻译任务出错", e);
            return Response.serverError().entity(Map.of("status", "error", "message", e.getMessage())).build();
        }
    }
}
