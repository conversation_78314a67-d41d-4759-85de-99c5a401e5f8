package org.acme.controller;

import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.acme.service.ai.XAIService;
import org.jboss.logging.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Controller for XAI API endpoints
 */
@Path("/api/xai")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class XAIController {
    private static final Logger logger = Logger.getLogger(XAIController.class);

    @Inject
    XAIService xaiService;

    /**
     * Generate text based on a prompt
     */
    @POST
    @Path("/generate")
    public Response generateText(TextGenerationRequest request) {
        try {
            String result = xaiService.generateText(
                request.getPrompt(),
                request.getTemperature(),
                request.getMaxTokens()
            );
            
            Map<String, Object> response = new HashMap<>();
            response.put("text", result);
            return Response.ok(response).build();
        } catch (Exception e) {
            logger.error("Error in text generation endpoint", e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(Map.of("error", e.getMessage()))
                .build();
        }
    }

    /**
     * Generate chat completion
     */
    @POST
    @Path("/chat")
    public Response generateChatCompletion(ChatCompletionRequest request) {
        try {
            String result = xaiService.generateChatResponse(request.getMessages());
            
            Map<String, Object> response = new HashMap<>();
            response.put("response", result);
            return Response.ok(response).build();
        } catch (Exception e) {
            logger.error("Error in chat completion endpoint", e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(Map.of("error", e.getMessage()))
                .build();
        }
    }

    /**
     * Generate embeddings for text
     */
    @POST
    @Path("/embeddings")
    public Response generateEmbeddings(EmbeddingRequest request) {
        try {
            List<Float> embeddings = xaiService.generateEmbedding(request.getText());
            
            Map<String, Object> response = new HashMap<>();
            response.put("embeddings", embeddings);
            return Response.ok(response).build();
        } catch (Exception e) {
            logger.error("Error in embeddings endpoint", e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(Map.of("error", e.getMessage()))
                .build();
        }
    }

    /**
     * Classify text into categories
     */
    @POST
    @Path("/classify")
    public Response classifyText(ClassificationRequest request) {
        try {
            String category = xaiService.classifyText(request.getText(), request.getCategories());
            
            Map<String, Object> response = new HashMap<>();
            response.put("category", category);
            return Response.ok(response).build();
        } catch (Exception e) {
            logger.error("Error in classification endpoint", e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(Map.of("error", e.getMessage()))
                .build();
        }
    }

    /**
     * Summarize text
     */
    @POST
    @Path("/summarize")
    public Response summarizeText(SummarizationRequest request) {
        try {
            String summary = xaiService.summarizeText(request.getText(), request.getMaxLength());
            
            Map<String, Object> response = new HashMap<>();
            response.put("summary", summary);
            return Response.ok(response).build();
        } catch (Exception e) {
            logger.error("Error in summarization endpoint", e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(Map.of("error", e.getMessage()))
                .build();
        }
    }

    // Request/Response models

    public static class TextGenerationRequest {
        private String prompt;
        private double temperature = 0.7;
        private int maxTokens = 1000;

        public String getPrompt() {
            return prompt;
        }

        public void setPrompt(String prompt) {
            this.prompt = prompt;
        }

        public double getTemperature() {
            return temperature;
        }

        public void setTemperature(double temperature) {
            this.temperature = temperature;
        }

        public int getMaxTokens() {
            return maxTokens;
        }

        public void setMaxTokens(int maxTokens) {
            this.maxTokens = maxTokens;
        }
    }

    public static class ChatCompletionRequest {
        private List<Map<String, String>> messages = new ArrayList<>();

        public List<Map<String, String>> getMessages() {
            return messages;
        }

        public void setMessages(List<Map<String, String>> messages) {
            this.messages = messages;
        }
    }

    public static class EmbeddingRequest {
        private String text;

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }
    }

    public static class ClassificationRequest {
        private String text;
        private List<String> categories = new ArrayList<>();

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public List<String> getCategories() {
            return categories;
        }

        public void setCategories(List<String> categories) {
            this.categories = categories;
        }
    }

    public static class SummarizationRequest {
        private String text;
        private int maxLength = 200;

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public int getMaxLength() {
            return maxLength;
        }

        public void setMaxLength(int maxLength) {
            this.maxLength = maxLength;
        }
    }
}
