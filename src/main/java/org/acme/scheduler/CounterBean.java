package org.acme.scheduler;

import java.util.concurrent.atomic.AtomicInteger;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

@Path("/scheduler/counter")
@ApplicationScoped
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class CounterBean {

    private AtomicInteger counter = new AtomicInteger();

    /**
     * Get the current counter value
     * @return The current counter value
     */
    @GET
    @Path("/value")
    public Response getValue() {
        return Response.ok(new CounterResponse(counter.get())).build();
    }

    /**
     * Manually increment the counter
     * @return The new counter value after incrementing
     */
    @POST
    @Path("/increment")
    public Response manualIncrement() {
        int newValue = counter.incrementAndGet();
        return Response.ok(new CounterResponse(newValue, "Counter incremented successfully")).build();
    }
    
    /**
     * Reset the counter to zero
     * @return Response indicating the counter was reset
     */
    @POST
    @Path("/reset")
    public Response resetCounter() {
        counter.set(0);
        return Response.ok(new CounterResponse(0, "Counter reset to zero")).build();
    }
    
    /**
     * Response class for counter operations
     */
    public static class CounterResponse {
        public int value;
        public String message;
        
        public CounterResponse(int value) {
            this.value = value;
            this.message = "Current counter value";
        }
        
        public CounterResponse(int value, String message) {
            this.value = value;
            this.message = message;
        }
    }
}
