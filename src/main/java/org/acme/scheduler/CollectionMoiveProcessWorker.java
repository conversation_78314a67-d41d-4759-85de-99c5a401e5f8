package org.acme.scheduler;

import org.acme.service.favourite.CollectionProcessService;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.jboss.logging.Logger;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.context.control.ActivateRequestContext;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Worker for processing collection movies - now accessible via API
 */
@Path("/scheduler/collection")
@ApplicationScoped
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class CollectionMoiveProcessWorker {
    private static final Logger logger = Logger.getLogger(CollectionMoiveProcessWorker.class);

    @Inject
    CollectionProcessService collectionProcessService;
    
    private boolean isRunning = false;

    /**
     * Manually trigger the collection movie processing task
     * @param batchSize Number of movies to process in this batch (default: 120)
     * @return Response with operation status
     */
    @POST
    @Path("/start")
    @Tag(name = "批量处理收藏夹，具体步骤是：1. 检查收藏夹中的电影，2. 处理新电影，3. 处理已存在的电影")
    public Response triggerProcessing(@QueryParam("batchSize") @DefaultValue("30000") int batchSize) {
        if (isRunning) {
            return Response.status(Response.Status.CONFLICT)
                    .entity(new TaskResponse("Collection processing task is already running"))
                    .build();
        }

        logger.info("Manually triggered collection processing");
        try {
            isRunning = true;
            // 改成异步
            ExecutorService executor = Executors.newSingleThreadExecutor();
            CompletableFuture.runAsync(() -> {
                try {
                    startWorker(batchSize);
                } finally {
                    isRunning = false;
                    executor.shutdown();
                }
            }, executor);
            
            return Response.ok(new TaskResponse("Successfully triggered collection processing"))
                    .build();
        } catch (Exception e) {
            isRunning = false;
            logger.error("Error in manual movie processing: " + e.getMessage(), e);
            return Response.serverError()
                    .entity(new TaskResponse("Error processing collection: " + e.getMessage()))
                    .build();
        }
    }
    
    /**
     * Get the current status of the collection processor
     * @return Status information
     */
    @GET
    @Path("/status")
    public Response getStatus() {
        return Response.ok(new TaskResponse(isRunning ? "Running" : "Idle"))
                .build();
    }

    /**
     * Process movies in a single thread, continuously running until no more movies are found
     * Uses a direct approach to avoid transaction nesting issues
     * 
     * @param batchSize Number of movies to process in each batch
     */
    @ActivateRequestContext
    protected void startWorker(int batchSize) {
        logger.infof("Starting continuous processing with batch size %d", batchSize);
        
        boolean hasMore = true;

        // Step 1: Check existing favorites and handle them
        collectionProcessService.handleExistingFavorites(null);

        try {
            // Step 2: Process new batch (non-transactional coordination method)
            // Continue processing batches until no more movies are found
            while (hasMore) {
                logger.infof("Processing batch with size %d", batchSize);

                // Find movies that need processing
                Set<Integer> moviesToProcess = collectionProcessService.findMoviesForProcessingNonTransactional();
                
                if (moviesToProcess.isEmpty()) {
                    logger.info("No more movies found for processing, stopping");
                    hasMore = false;
                    break;
                }  
                
                logger.infof("Found %d movies to process in batch", moviesToProcess.size());
                
                // Process movies in groups of 12 to optimize HTTP requests
                int batchProcessedCount = 0;
                
                // Convert Set to List for easier batch processing
                List<Integer> movieIdList = new ArrayList<>(moviesToProcess);
                try {
                    // Process the group of movies
                    int movieSuccessCount = collectionProcessService.processMovieGroupNonTransactional(movieIdList);
                    batchProcessedCount += movieSuccessCount;
                    
                    logger.infof("Successfully processed %d movies", movieSuccessCount);
                } catch (Exception e) {
                    logger.errorf("Error processing movie group: %s", e.getMessage());
                }
                
                logger.infof("Batch: Processed %d out of %d movies", 
                        batchProcessedCount, moviesToProcess.size());
                
                // Add a short pause between batches to avoid overloading the system
                try {
                    Thread.sleep(1000); // 1 second pause between batches
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
            
            logger.infof("Continuous processing completed.");
            
        } catch (Exception e) {
            logger.errorf("Error in continuous processing: %s", e.getMessage());
            logger.error("Stack trace:", e);
        }
    }
    
    /**
     * Simple response class for API responses
     */
    public static class TaskResponse {
        public String message;
        
        public TaskResponse(String message) {
            this.message = message;
        }
    }
}
