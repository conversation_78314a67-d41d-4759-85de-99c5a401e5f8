package org.acme.scheduler;

import org.acme.service.MovieInfoService;
import org.jboss.logging.Logger;

import io.quarkus.scheduler.Scheduled;
import io.quarkus.scheduler.Scheduled.ConcurrentExecution;
import io.smallrye.common.annotation.Blocking;
import jakarta.inject.Inject;

public class MovieInfoSyncTaskJob {
    private static final Logger logger = Logger.getLogger(MovieInfoSyncTaskJob.class);
    
    @Inject
    MovieInfoService movieInfoService;
    /**
     * 必须执行完才执行下一次
     */
    @Scheduled(every = "30m", concurrentExecution = ConcurrentExecution.SKIP)
    @Blocking
    public void syncMovieInfo() {
        try {
            logger.info("Scheduled syncMovieInfo start");
            // 大幅减少批量处理数量，避免长时间运行和网络阻塞
            movieInfoService.handleMissingMovieInfo(20);
            logger.info("Scheduled syncMovieInfo end");
        } catch (Exception e) {
            logger.error("Error during scheduled syncMovieInfo: " + e.getMessage(), e);
        }
    }
}
