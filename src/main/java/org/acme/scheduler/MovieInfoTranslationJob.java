package org.acme.scheduler;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.acme.entity.MovieInfo;
import org.acme.enums.AzureAILanguageCode;
import org.apache.commons.beanutils.BeanUtils;
import org.acme.service.translation.AzureOpenAITranslationClient;
import org.jboss.logging.Logger;

import com.cronutils.utils.StringUtils;

import dev.langchain4j.internal.Json;
import io.quarkus.panache.common.Page;
import io.quarkus.scheduler.Scheduled;
import io.quarkus.scheduler.Scheduled.ConcurrentExecution;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class MovieInfoTranslationJob {
    private static final Logger LOG = Logger.getLogger(MovieInfoTranslationJob.class);
    List<String> allLanguagesToTranslate = Arrays.asList(
            "Russian", "Turkish", "Arabic", "Dutch", "Swedish", "Polish", "Danish", "Norwegian", "Finnish",
            "Traditional Chinese", "Simplified Chinese", "English", "Korean", "Malay", "Thai",
            "German", "French", "Vietnamese", "Indonesian", "Filipino", "Portuguese", "Hindi"
        );
    @Inject
    AzureOpenAITranslationClient translationClient;
    
    // @Scheduled(every = "1h", concurrentExecution = ConcurrentExecution.SKIP)
    public void translateMovieInfo() {
        // 步骤1：构建子查询，筛选每个code下仅日语的分组
        String subQuery = """
            SELECT m.code 
            FROM MovieInfo m 
            GROUP BY m.code 
            HAVING COUNT(DISTINCT m.language) = 1 AND MAX(m.language) = 'ja'
            """;

        // 步骤2：关联子查询获取完整数据
        List<MovieInfo> movieInfos = MovieInfo.find(
            "language = 'ja' AND title IS NOT NULL AND code IN (" + subQuery + ")"
        )
        .page(Page.ofSize(10))
        .list();

        // map: title -> movieInfos
        Map<String, MovieInfo> titleToMovieMap = movieInfos.stream()
            .collect(Collectors.toMap(
                mi -> mi.title,  // 键：电影标题
                mi -> mi,        // 值：MovieInfo 对象
                (existing, replacement) -> existing,  // 如果键重复，保留已存在的
                LinkedHashMap::new  // 使用 LinkedHashMap 保持原始顺序
            ));

        List<String> titlesToTranslate = movieInfos.stream()
                .map(mi -> mi.title)
                .collect(Collectors.toList());



        // 2. 创建线程池
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(3); // 控制并发数
        ExecutorService executor = Executors.newCachedThreadPool();
        CountDownLatch latch = new CountDownLatch(titlesToTranslate.size());
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);

        // 3. 记录开始时间
        long startTime = System.currentTimeMillis();
        LOG.info("Translation started at: " + new Date(startTime));

        List<MovieInfo> translatedMovies = new ArrayList<>();

        // 4. 调度任务
        for (int i = 0; i < titlesToTranslate.size(); i++) {
            final String title = titlesToTranslate.get(i);
            final int index = i + 1;

            // 每8秒调度一个任务
            scheduler.schedule(() -> {
                executor.submit(() -> {
                    try {
                        LOG.infof("Processing title %d/%d: %s",
                                index, titlesToTranslate.size(), title);

                        // 5. 执行翻译
                        String result = translationClient.translateBatch(
                                List.of(title),
                                "Japanese",
                                allLanguagesToTranslate,
                                false,
                                true
                        );

                        // 6. 处理结果
                        if (!result.startsWith("Translation failed:")) {
                            List<Map<String, String>> translations =
                                    translationClient.parseBatchTranslationResponse(result, 1);
                            LOG.infof("Translation success for '%s': %s",
                                    title, translations);

                            // 7. 保存翻译结果
                            for (Map<String, String> translation : translations) {
                                translation.forEach((lang, text) -> {
                                    MovieInfo movieInfo = titleToMovieMap.get(title);
                                    if (movieInfo != null && AzureAILanguageCode.fromDisplayName(lang) != null && !StringUtils.isEmpty(text)) {
                                        // 创建新的 MovieInfo 对象用于保存翻译
                                        MovieInfo translatedMovie = new MovieInfo();
                                        // 复制原始对象的属性,除了 description 属性
                                        try {
                                            BeanUtils.copyProperties(translatedMovie, movieInfo);
                                            // 更新翻译后的标题
                                            translatedMovie.title = text;
                                            // 设置目标语言
                                            translatedMovie.language = AzureAILanguageCode.fromDisplayName(lang).getLanguageCode();
                                            // 保存翻译结果
                                            // translatedMovie.persist();
                                            translatedMovies.add(translatedMovie);
                                        } catch (Exception e) {
                                            LOG.error("Failed to copy properties", e);
                                            return;
                                        }
                                    }
                                });
                            }
                            successCount.incrementAndGet();
                        } else {
                            LOG.errorf("Translation failed for '%s': %s", title, result);
                            failCount.incrementAndGet();
                        }
                    } catch (Exception e) {
                        LOG.error("Error processing title: " + title, e);
                        failCount.incrementAndGet();
                    } finally {
                        latch.countDown();
                    }
                });
            }, i * 8, TimeUnit.SECONDS);  // 每8秒一个任务
        }

        // 7. 等待所有任务完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            LOG.error("Translation interrupted", e);
        }
        long endTime = System.currentTimeMillis();
        LOG.info(Json.toJson(translatedMovies));

        // 9. 输出统计信息
        LOG.info("\n=== Translation Summary ===");
        LOG.infof("Total titles: %d", titlesToTranslate.size());
        LOG.infof("Success: %d", successCount.get());
        LOG.infof("Failed: %d", failCount.get());
        LOG.infof("Total time: %d seconds", (endTime - startTime) / 1000);
        LOG.infof("Average time per title: %.2f seconds",
                (endTime - startTime) / 1000.0 / titlesToTranslate.size());

        // 10. 验证结果
        if (failCount.get() > 0) {
            LOG.warn("Some translations failed. Check logs for details.");
        }
    }
}
