package org.acme.scheduler;

import org.acme.config.AuthCookieConfig;
import org.jboss.logging.Logger;

import io.quarkus.scheduler.Scheduled;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

/**
 * Scheduler for refreshing the authentication cookie dynamically via network login
 * This scheduler runs periodically to ensure the auth cookie is always up to date
 */
@ApplicationScoped
public class AuthCookieRefreshScheduler {
    
    private static final Logger logger = Logger.getLogger(AuthCookieRefreshScheduler.class);
    
    @Inject
    AuthCookieConfig authCookieConfig;
    /**
     * Scheduled task that runs every 15 minutes to refresh the authentication cookie
     * Uses BrowserLoginService through AuthCookieConfig to get a fresh cookie
     */
    @Scheduled(every = "PT15M")
    public void refreshAuthCookie() {
        try {
            logger.info("Scheduled refresh of auth cookie");
            boolean success = authCookieConfig.refreshCookie();
            if (success) {
                logger.info("Auth cookie successfully refreshed");
            } else {
                logger.warn("Failed to refresh auth cookie");
            }
        } catch (Exception e) {
            logger.error("Error during scheduled auth cookie refresh: " + e.getMessage(), e);
        }
    }
    
    /**
     * Initialize the cookie on startup
     * This method is called automatically when the application starts
     */
    void onStart() {
        refreshAuthCookie();
        logger.info("Auth cookie refresh scheduler initialized");
    }
}
