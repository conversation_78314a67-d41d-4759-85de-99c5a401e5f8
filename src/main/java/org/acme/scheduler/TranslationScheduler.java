package org.acme.scheduler;

import io.quarkus.scheduler.Scheduled;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

import org.acme.service.translation.TaskBasedTranslationService;
import org.acme.service.translation.TranslationTaskManager;
import org.jboss.logging.Logger;

/**
 * 翻译定时调度器
 * 定期执行翻译任务检查、创建和处理
 */
@ApplicationScoped
public class TranslationScheduler {

    private static final Logger LOG = Logger.getLogger(TranslationScheduler.class);
    
    @Inject
    TaskBasedTranslationService translationService;
    
    @Inject
    TranslationTaskManager taskManager;
    
    /**
     * 每5分钟检查超时任务
     */
    // @Scheduled(every = "5m")
    void checkTimeoutTasks() {
        LOG.info("开始检查超时任务...");
        int timeoutTasks = taskManager.processTimeoutTasks(10); // 10分钟超时
        LOG.infof("超时任务检查完成: 共处理 %d 个超时任务", timeoutTasks);
    }
    
    /**
     * 每1分钟自动触发一次翻译处理
     */
    // @Scheduled(every = "1m")
    void triggerTranslation() {
        LOG.info("定时触发翻译处理...");
        translationService.triggerTranslation(10); // 每次处理10个任务
    }
    
    /**
     * 每小时重置失败任务以便重试
     */
    // @Scheduled(every = "1h")
    void resetFailedTasks() {
        LOG.info("重置失败任务...");
        int resetTasks = taskManager.resetFailedTasks(3); // 最多重试3次
        LOG.infof("失败任务重置完成: 共重置 %d 个任务", resetTasks);
    }
}
