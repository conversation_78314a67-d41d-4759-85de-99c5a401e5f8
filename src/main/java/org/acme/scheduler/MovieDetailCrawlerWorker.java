package org.acme.scheduler;

import java.time.Instant;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import org.acme.entity.Movie;
import org.acme.entity.MovieInfo;
import org.acme.enums.CrawlerStatus;
import org.acme.enums.MovieStatus;
import org.acme.service.MovieInfoExtractionService;
import org.jboss.logging.Logger;

import io.netty.util.internal.StringUtil;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

@Path("/scheduler/movie-detail")
@ApplicationScoped
public class MovieDetailCrawlerWorker {

    private static final Logger logger = Logger.getLogger(MovieDetailCrawlerWorker.class);

    @Inject
    MovieInfoExtractionService movieInfoExtractionService;

    private static final int THREAD_POOL_SIZE = 5; // 并发线程数

    private boolean isRunning = false;

    /**
     * Manually trigger movie detail crawling via API endpoint
     * Uses a non-transaction approach to find movies with gaps in ID sequence
     *
     * @param batchSize The number of movies to process in one batch (default: 10)
     * @return Response with status of the operation
     */
    @POST
    @Path("/start")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Response startProcessing(@QueryParam("batchSize") @DefaultValue("10") int batchSize) {
        if (isRunning) {
            return Response.status(Response.Status.CONFLICT)
                    .entity(new TaskResponse("Movie detail crawler is already running"))
                    .build();
        }

        logger.info("Manually triggered movie detail crawling with batch size: " + batchSize);
        try {
            isRunning = true;
            // 使用异步处理，立即返回响应
            CompletableFuture.runAsync(() -> {
                try {
                    processMoviesBatch(batchSize);
                } catch (Exception e) {
                    logger.error("Error in async movie detail processing: " + e.getMessage(), e);
                } finally {
                    isRunning = false;
                }
            });

            return Response.ok(new TaskResponse("Successfully triggered movie detail crawling"))
                    .build();
        } catch (Exception e) {
            isRunning = false;
            logger.error("Error starting movie detail processing: " + e.getMessage(), e);
            return Response.serverError()
                    .entity(new TaskResponse("Error starting movie detail processing: " + e.getMessage()))
                    .build();
        }
    }

    /**
     * Get the current status of the movie detail crawler
     * @return Status information
     */
    @GET
    @Path("/status")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getStatus() {
        return Response.ok(new TaskResponse(isRunning ? "Running" : "Idle"))
                .build();
    }

    /**
     * 批量处理电影，连续处理直到没有更多电影可处理
     *
     * @param batchSize 每批最大处理数量
     * @return true 如果有电影被处理，false 如果没有电影被处理
     */
    public boolean processMoviesBatch(int batchSize) {
        int processedMoviesCount = 0;
        int totalBatchesProcessed = 0;
        boolean hasProcessedAnyMovie = false;

        try {

            // 连续处理直到没有更多电影
            while (true) {
                // 1. 找出当前批次的电影
                List<Movie> moviesToProcess = findMoviesWithIdGaps(batchSize);

                if (moviesToProcess == null || moviesToProcess.isEmpty()) {
                    logger.info("No more movies found for processing");
                    break;
                }

                totalBatchesProcessed++;
                processedMoviesCount += moviesToProcess.size();
                hasProcessedAnyMovie = true;

                // 2. 原子性地将这些电影状态更新为PROCESSING
                updateMoviesStatusToProcessing(moviesToProcess);

                // 3. 创建更高效的线程池并发处理当前批次
                // 创建可缩放的线程池，支持更多并发任务
                int coreThreads = THREAD_POOL_SIZE;
                int maxThreads = Math.max(moviesToProcess.size(), THREAD_POOL_SIZE * 3);

                // 使用任务计数来确定线程池大小，确保所有任务都能并发执行
                logger.infof("Setting up thread pool with core=%d, max=%d for %d tasks",
                        coreThreads, maxThreads, moviesToProcess.size());

                try {
                    // 改成单线程
                    for (Movie movie : moviesToProcess) {
                        processOneMovieByCode(movie);
                    }

                    logger.infof("Batch #%d with %d movies processed successfully",
                            totalBatchesProcessed, moviesToProcess.size());
                } catch (Exception e) {
                    logger.errorf("Error processing movies in batch: %s", e.getMessage());
                    logger.error("Stack trace:", e);
                }
            }

            logger.infof("Total movies processed: %d in %d batches", processedMoviesCount, totalBatchesProcessed);
            return hasProcessedAnyMovie;

        } catch (Exception e) {
            logger.errorf("Error in movie batch processing: %s", e.getMessage());
            logger.error("Stack trace:", e);
            return hasProcessedAnyMovie;
        }
    }

    /**
     * 查找数据库中具有ID间隔的电影记录
     * 1. 首先找出最小和最大ID
     * 2. 使用原生SQL查询找出ID范围内状态为NEW的电影
     *
     * @param batchSize 最大处理数量
     * @return 要处理的电影列表
     */
    @Transactional
    protected List<Movie> findMoviesWithIdGaps(int batchSize) {
        try {
            // 1. 找出数据库中的最小和最大ID
            Number minIdObj = (Number) Movie.getEntityManager()
                    .createQuery("SELECT MIN(m.originalId) FROM Movie m")
                    .getSingleResult();

            Number maxIdObj = (Number) Movie.getEntityManager()
                    .createQuery("SELECT MAX(m.originalId) FROM Movie m")
                    .getSingleResult();

            if (minIdObj == null || maxIdObj == null) {
                logger.info("No movie IDs found in database");
                return List.of();
            }

            // 2. 使用原生SQL查询找出状态为NEW的电影，优先考虑ID间隔
            // 注意：这里使用了PostgreSQL的generate_series函数找出ID序列中的间隔
            String nativeQuery =
                "WITH all_ids AS (" +
                "  SELECT original_id FROM movies WHERE status = ? ORDER BY original_id" +
                "), id_gaps AS (" +
                "  SELECT original_id FROM all_ids WHERE original_id NOT IN (" +
                "    SELECT original_id FROM movies WHERE status != ?" +
                "  )" +
                ")" +
                "SELECT * FROM movies WHERE original_id IN (SELECT original_id FROM id_gaps) AND status = ? " +
                "ORDER BY original_id ASC LIMIT ?";

            @SuppressWarnings("unchecked")
            List<Movie> movies = Movie.getEntityManager()
                    .createNativeQuery(nativeQuery, Movie.class)
                    .setParameter(1, CrawlerStatus.NEW.getValue())
                    .setParameter(2, CrawlerStatus.NEW.getValue())
                    .setParameter(3, CrawlerStatus.NEW.getValue())
                    .setParameter(4, batchSize)
                    .getResultList();

            if (movies.isEmpty()) {
                // 如果没有找到ID间隔的电影，就直接按ID顺序查找
                movies = Movie.find("status = ?1 ORDER BY originalId ASC", CrawlerStatus.NEW.getValue())
                        .page(0, batchSize)
                        .list();
            }

            logger.infof("Found %d movies to process with status NEW", movies.size());
            return movies;

        } catch (Exception e) {
            logger.errorf("Error finding movies with ID gaps: %s", e.getMessage());
            logger.error("Stack trace:", e);
            return List.of();
        }
    }

    /**
     * 将电影状态更新为PROCESSING
     *
     * @param movies 要更新的电影列表
     */
    @Transactional
    protected void updateMoviesStatusToProcessing(List<Movie> movies) {
        if (movies == null || movies.isEmpty()) {
            return;
        }

        try {
            List<Integer> movieIds = movies.stream().map(Movie::getOriginalId).toList();
            Movie.update("status = ?1, updatedAt = ?2 where originalId in (?3)",
                MovieStatus.PROCESSING.getValue(), Instant.now(), movieIds);
            logger.infof("Marked %d movies as PROCESSING", movieIds.size());
        } catch (Exception e) {
            logger.errorf("Error updating movies status to PROCESSING: %s", e.getMessage());
        }
    }
    /**
     * 从电影列表中提取电影代码
     *
     * @param movies 电影列表
     * @return 电影代码列表
     */
    protected List<String> extractMovieCodes(List<Movie> movies) {
        return movies.stream()
            .map(movie -> {
                if (movie.link != null && !movie.link.isEmpty()) {
                    // 从链接中提取最后一部分作为代码
                    // 例如：https://123av.com/zh/dm2/v/rbd-301-uncensored-leaked
                    // 提取为：rbd-301-uncensored-leaked
                    String[] parts = movie.link.split("/");
                    if (parts.length > 0) {
                        // 获取最后一个部分作为代码
                        String extractedCode = parts[parts.length - 1];
                        logger.debugf("Extracted code '%s' from link: %s", extractedCode, movie.link);
                        return extractedCode;
                    }
                }
                // 如果链接为空或无法提取，则返回存储的代码
                return movie.code;
            })
            .filter(code -> code != null && !code.isEmpty())
            .toList();
    }

    /**
     * 处理单个电影 - 将HTTP请求与数据库事务分离
     */
    @Transactional
    public void processOneMovieByCode(Movie movie) {
        if (StringUtil.isNullOrEmpty(movie.getCode())) {
            logger.warnf("Movie code is null");
            return;
        }
        String code = movie.getCode();
        logger.infof("Processing movie code:{}", code);

        try {
            List<MovieInfo> extractedInfos = movieInfoExtractionService.extractAndSaveAllLanguages(movie);
            logger.infof("Extracted %d language versions for movie: %s", extractedInfos.size(), code);

            // 步骤2: 更新Movie表中的状态为已处理
            updateMovieStatusToProcessed(movie.getOriginalId());

            logger.infof("Successfully processed movie: %s", code);
        } catch (Exception e) {
            logger.errorf("Failed to process movie %s: %s", code, e.getMessage());
            updateMovieStatusToFailed(movie.getOriginalId());
        }
    }

    /**
     * 在新事务中更新电影状态为已处理
     */
    @Transactional
    void updateMovieStatusToProcessed(int originalId) {
        try {
            Movie.update("status = ?1 where originalId = ?2", MovieStatus.SUCCEED.getValue(), originalId);
            logger.infof("Updated movie with originalId %s status to ONLINE", originalId);
        } catch (Exception ex) {
            logger.errorf("Failed to update movie status to ONLINE: %s", ex.getMessage());
        }
    }

    /**
     * 在新事务中更新电影状态为失败
     */
    @Transactional
    void updateMovieStatusToFailed(int originalId) {
        try {
            Movie.update("status = ?1 where originalId = ?2", MovieStatus.FAILED.getValue(), originalId);
            logger.infof("Updated movie with originalId %s status to FAILED", originalId);
        } catch (Exception ex) {
            logger.errorf("Failed to update movie status to FAILED: %s", ex.getMessage());
        }
    }

    /**
     * Simple response class for API responses
     */
    public static class TaskResponse {
        public String message;

        public TaskResponse(String message) {
            this.message = message;
        }
    }
}
