package org.acme.entity;

import io.quarkus.hibernate.orm.panache.PanacheEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import java.time.Instant;

@Entity
@Table(name = "movie_info_translation")
@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
public class MovieInfoTranslation extends PanacheEntity {

    /**
     * 根据内容代码和语言代码查询翻译
     * 
     * @param code 内容代码
     * @param language 语言代码
     * @return 匹配的翻译记录，如果不存在则返回null
     */
    public static MovieInfoTranslation findByCodeAndLanguage(String code, String language) {
        return find("code = ?1 and language = ?2", code, language).firstResult();
    }

    public String code;

    public String language;

    @Column(columnDefinition = "text")
    public String title;

    @Column(columnDefinition = "text")
    public String description;

    @CreationTimestamp
    @Column(name = "created_at")
    public Instant createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    public Instant updatedAt;

}
