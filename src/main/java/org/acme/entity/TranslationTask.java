package org.acme.entity;

import io.quarkus.hibernate.orm.panache.PanacheEntity;
import jakarta.persistence.*;
import org.acme.enums.AzureAILanguageCode;

import java.time.LocalDateTime;

/**
 * 翻译任务实体
 * 记录每个需要翻译的内容项目和目标语言的状态
 */
@Entity
@Table(name = "translation_tasks", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"content_id", "content_type", "target_language"}))
public class TranslationTask extends PanacheEntity {

    /**
     * 翻译任务类型常量
     */
    public static final class ContentType {
        public static final String TITLE = "TITLE";
        public static final String DESCRIPTION = "DESCRIPTION";
        
        private ContentType() {} // 防止实例化
    }

    /**
     * 任务状态常量
     */
    public static final class TaskStatus {
        public static final String PENDING = "PENDING";
        public static final String PROCESSING = "PROCESSING";
        public static final String COMPLETED = "COMPLETED";
        public static final String FAILED = "FAILED";
        public static final String TIMEOUT = "TIMEOUT";
        
        private TaskStatus() {} // 防止实例化
    }

    /**
     * 内容ID（对应MovieInfo的code）
     */
    @Column(name = "content_id", nullable = false)
    public String contentId;

    /**
     * 内容类型（标题或描述）
     */
    @Column(name = "content_type", nullable = false)
    public String contentType;

    /**
     * 源语言
     */
    @Column(name = "source_language", nullable = false)
    public String sourceLanguage;

    /**
     * 目标语言
     */
    @Column(name = "target_language", nullable = false)
    public String targetLanguage;

    /**
     * 任务状态
     */
    @Column(name = "status", nullable = false)
    public String status = TaskStatus.PENDING;

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    public LocalDateTime createdAt = LocalDateTime.now();

    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    public LocalDateTime updatedAt = LocalDateTime.now();

    /**
     * 优先级（数值越高优先级越高）
     */
    @Column(name = "priority")
    public int priority = 0;

    /**
     * 重试次数
     */
    @Column(name = "retry_count")
    public int retryCount = 0;

    /**
     * 错误消息
     */
    @Column(name = "error_message", length = 1000)
    public String errorMessage;

    /**
     * 从MovieInfo和目标语言创建翻译任务
     */
    public static TranslationTask createFromMovieInfo(MovieInfo movie, AzureAILanguageCode targetLang, String contentType) {
        TranslationTask task = new TranslationTask();
        task.contentId = movie.code;
        task.contentType = contentType;
        task.sourceLanguage = AzureAILanguageCode.JAPANESE.getLanguageCode(); // 默认源语言为日语
        task.targetLanguage = targetLang.getLanguageCode();
        return task;
    }

    /**
     * 更新任务状态和时间
     */
    public void updateStatus(String newStatus) {
        this.status = newStatus;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 记录失败信息
     */
    public void markAsFailed(String errorMessage) {
        this.status = TaskStatus.FAILED;
        this.errorMessage = errorMessage;
        this.updatedAt = LocalDateTime.now();
        this.retryCount++;
    }

    /**
     * 标记为超时
     */
    public void markAsTimeout() {
        this.status = TaskStatus.TIMEOUT;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 重置任务为等待状态（用于重试）
     */
    public void resetForRetry() {
        this.status = TaskStatus.PENDING;
        this.updatedAt = LocalDateTime.now();
        this.retryCount++;
    }
}
