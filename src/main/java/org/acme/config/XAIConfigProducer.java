package org.acme.config;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Produces;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.logging.Logger;

/**
 * Configuration producer for XAI settings.
 * Reads XAI configuration from application.properties.
 */
@ApplicationScoped
public class XAIConfigProducer {
    private static final Logger LOG = Logger.getLogger(XAIConfigProducer.class);

    @ConfigProperty(name = "xai.api-key", defaultValue = "default-key")
    String apiKey;

    @ConfigProperty(name = "xai.endpoint", defaultValue = "https://api.xai.com/v1")
    String endpoint;

    @ConfigProperty(name = "xai.model-id", defaultValue = "default-model")
    String modelId;

    @Produces
    @ApplicationScoped
    public XAIConfig produceXAIConfig() {
        LOG.info("Initializing XAI configuration");
        
        XAIConfig config = new XAIConfig();
        
        // Override the default client with properties from application.properties
        config.updateDefaultClient(apiKey, endpoint, modelId);
        
        return config;
    }
}
