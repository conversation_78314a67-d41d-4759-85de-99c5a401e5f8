package org.acme.config;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import org.acme.service.browser.BrowserLoginService;
import org.jboss.logging.Logger;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Configuration class for managing authentication cookies.
 * This class centralizes cookie management for all services that need authentication.
 * Uses BrowserLoginService to dynamically obtain and refresh authentication cookies.
 */
@ApplicationScoped
public class AuthCookieConfig {
    
    private static final Logger logger = Logger.getLogger(AuthCookieConfig.class);
    
    /**
     * The auth cookie string used for API authentication.
     * This is dynamically fetched using BrowserLoginService and refreshed periodically.
     */
    private String authCookie;
    private Instant lastAttemptTime;
    private static final long REFRESH_COOLDOWN_SECONDS = 120; // Minimum seconds between refresh attempts
    private final AtomicBoolean refreshInProgress = new AtomicBoolean(false);
    private final AtomicReference<RefreshResult> lastRefreshResult = new AtomicReference<>(null);
    
    @Inject
    BrowserLoginService browserLoginService;
    
    @PostConstruct
    void init() {
        logger.info("Initializing AuthCookieConfig with dynamic login");
        refreshCookieFromBrowser();
    }
    
    /**
     * Refresh the cookie from the browser login service
     */
    private void refreshCookieFromBrowser() {
        try {
            lastAttemptTime = Instant.now();          // 写入尝试时间
            String newCookie = browserLoginService.getAuthCookies();
            if (newCookie != null && !newCookie.isEmpty()) {
                setAuthCookie(newCookie);
                logger.info("Successfully obtained auth cookie from browser login service");
                // 可选：记录成功时间
                // lastRefreshTime = Instant.now();
            } else {
                logger.warn("Failed to get auth cookie from browser login service");
            }
        } catch (Exception e) {
            logger.error("Error refreshing auth cookie from browser: " + e.getMessage(), e);
        }
    }
    
    /**
     * Get the authentication cookie string.
     * 
     * @return The cookie string for authentication
     */
    public String getAuthCookie() {
        // If cookie is null or empty, try to refresh it
        if (authCookie == null || authCookie.isEmpty()) {
            refreshCookieFromBrowser();
        }
        return authCookie;
    }
    
    /**
     * Set the authentication cookie string.
     * This can be used to update the cookie at runtime if it expires.
     * 
     * @param newCookie The new cookie string to use
     */
    public void setAuthCookie(String newCookie) {
        if (newCookie != null && !newCookie.isEmpty()) {
            if (!newCookie.equals(this.authCookie)) {
                logger.info("Updating authentication cookie");
                this.authCookie = newCookie;
            }
        }
    }
    
    /**
     * Force refresh of the authentication cookie using the browser login service.
     * Includes a cooldown mechanism to prevent excessive refresh attempts.
     * 
     * @return RefreshResult indicating the result of the refresh attempt
     */
    public RefreshResult refreshCookieWithResult() {
        Instant now = Instant.now();
        
        // If a refresh is already in progress, don't start another one
        if (refreshInProgress.get()) {
            logger.info("Cookie refresh already in progress, skipping new request");
            lastRefreshResult.set(RefreshResult.IN_PROGRESS);
            return RefreshResult.IN_PROGRESS;
        }
        
        // Check if we've attempted a refresh recently
        if (lastAttemptTime != null && 
            ChronoUnit.SECONDS.between(lastAttemptTime, now) < REFRESH_COOLDOWN_SECONDS) {
            logger.info("Cookie refresh on cooldown. Last attempt was " + 
                       ChronoUnit.SECONDS.between(lastAttemptTime, now) + 
                       " seconds ago. Waiting " + REFRESH_COOLDOWN_SECONDS + " seconds between attempts.");
            lastRefreshResult.set(RefreshResult.ON_COOLDOWN);
            return RefreshResult.ON_COOLDOWN;
        }
        
        try {
            lastAttemptTime = now;
            logger.info("Forcing cookie refresh");
            
            // Set flag to indicate refresh is in progress
            refreshInProgress.set(true);
            
            String newCookie = browserLoginService.refreshAuthCookies();
            boolean success = newCookie != null && !newCookie.isEmpty();
            
            if (success) {
                logger.info("Cookie refresh successful");
                lastRefreshResult.set(RefreshResult.REFRESHED);
                return RefreshResult.REFRESHED;
            } else {
                logger.warn("Cookie refresh returned null or empty cookie");
                lastRefreshResult.set(RefreshResult.FAILED);
                return RefreshResult.FAILED;
            }
        } catch (Exception e) {
            logger.error("Error refreshing cookie: " + e.getMessage(), e);
            lastRefreshResult.set(RefreshResult.FAILED);
            return RefreshResult.FAILED;
        } finally {
            // Always clear the in-progress flag
            refreshInProgress.set(false);
        }
    }
    
    /**
     * Indicates whether the last refresh attempt actually fetched a new cookie
     * @return true if the last refresh attempt resulted in a new cookie, false otherwise
     */
    public boolean wasActuallyRefreshed() {
        return lastRefreshResult.get() == RefreshResult.REFRESHED;
    }
    
    /**
     * Force refresh of the authentication cookie using the browser login service.
     * Includes a cooldown mechanism to prevent excessive refresh attempts.
     * 
     * @return true if the refresh was successful or if a refresh is already in progress/on cooldown, false otherwise
     * @deprecated Use refreshCookieWithResult() for more detailed status information
     */
    public boolean refreshCookie() {
        RefreshResult result = refreshCookieWithResult();
        // For backward compatibility: 
        // - Return true for REFRESHED, IN_PROGRESS, ON_COOLDOWN
        // - Return false for FAILED
        return result == RefreshResult.REFRESHED || 
               result == RefreshResult.IN_PROGRESS || 
               result == RefreshResult.ON_COOLDOWN;
    }
}
