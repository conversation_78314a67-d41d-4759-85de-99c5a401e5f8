package org.acme.config;

import org.jboss.logging.Logger;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Configuration class for XAI API clients.
 * Similar structure to AzureOpenAIConfig but adapted for XAI service.
 */
public class XAIConfig {
    private static final Logger LOG = Logger.getLogger(XAIConfig.class);

    private List<ClientConfig> clients = new ArrayList<>();

    public XAIConfig() {
        // Default configuration
        addDefaultClient();

        // Add additional configurations if needed
        addAdditionalClients();

        LOG.infof("Initialized %d XAI clients", clients.size());
    }

    private void addDefaultClient() {
        // Add the default client
        ClientConfig defaultClient = new ClientConfig(
            "xai-default",
            "your-xai-api-key", // Will be replaced by properties
            "https://api.xai.com/v1", // Will be replaced by properties
            "default-model", // Will be replaced by properties
            4000,
            1000,
            240,
            "default",
            1
        );
        clients.add(defaultClient);
    }
    
    /**
     * Update the default client with properties from application.properties
     * @param apiKey API key from properties
     * @param endpoint Endpoint from properties
     * @param modelId Model ID from properties
     */
    public void updateDefaultClient(String apiKey, String endpoint, String modelId) {
        if (clients.isEmpty()) {
            LOG.warn("No clients found to update");
            return;
        }
        
        // Update the first client (default client)
        ClientConfig defaultClient = clients.get(0);
        
        // Create a new client with updated properties
        ClientConfig updatedClient = new ClientConfig(
            defaultClient.getId(),
            apiKey,
            endpoint,
            modelId,
            defaultClient.getInputMaxTokens(),
            defaultClient.getOutputMaxTokens(),
            defaultClient.getTokenKiloPerMinute(),
            defaultClient.getGroup(),
            defaultClient.getPriority()
        );
        
        // Replace the default client
        clients.set(0, updatedClient);
        
        LOG.infof("Updated default XAI client with properties from application.properties: endpoint=%s, modelId=%s", 
                 endpoint, modelId);
    }

    private void addAdditionalClients() {
        // Add additional clients if needed
        // Example:
        /*
        clients.add(new ClientConfig(
            "xai-alternative",
            "alternative-api-key",
            "https://api.xai.com/v1",
            "alternative-model",
            4000,
            1000,
            240,
            "alternative",
            2
        ));
        */
    }

    /**
     * Get a client configuration by ID
     * @param id Client ID
     * @return Optional containing the client if found
     */
    public Optional<ClientConfig> getClientById(String id) {
        return clients.stream()
                .filter(client -> client.getId().equals(id))
                .findFirst();
    }

    /**
     * Get a client configuration by group, selecting the one with highest priority
     * @param group Group name
     * @return Optional containing the highest priority client in the group
     */
    public Optional<ClientConfig> getClientByGroup(String group) {
        return clients.stream()
                .filter(client -> client.getGroup().equals(group))
                .min(Comparator.comparingInt(ClientConfig::getPriority));
    }

    /**
     * Get all available clients
     * @return List of all client configurations
     */
    public List<ClientConfig> getAllClients() {
        return new ArrayList<>(clients);
    }

    /**
     * Get all available client groups
     * @return List of unique group names
     */
    public List<String> getAvailableGroups() {
        return clients.stream()
                .map(ClientConfig::getGroup)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * Get the best available client based on priority
     * @return The client with the highest priority (lowest number)
     */
    public ClientConfig getBestClient() {
        return clients.stream()
                .min(Comparator.comparingInt(ClientConfig::getPriority))
                .orElse(clients.get(0)); // Fallback to first client if no priority order
    }

    /**
     * Configuration for a single XAI client
     */
    public static class ClientConfig {
        private String id;
        private String apiKey;
        private String endpoint;
        private String modelId;
        private int inputMaxTokens;
        private int outputMaxTokens;
        private int tokenKiloPerMinute;
        private String group;
        private int priority;

        // API version is common across all clients
        private static final String API_VERSION = "v1";

        public ClientConfig(String id, String apiKey, String endpoint, String modelId,
                           int inputMaxTokens, int outputMaxTokens, int tokenKiloPerMinute,
                           String group, int priority) {
            this.id = id;
            this.apiKey = apiKey;
            this.endpoint = endpoint;
            this.modelId = modelId;
            this.inputMaxTokens = inputMaxTokens;
            this.outputMaxTokens = outputMaxTokens;
            this.tokenKiloPerMinute = tokenKiloPerMinute;
            this.group = group;
            this.priority = priority;
        }

        public String getId() {
            return id;
        }

        public String getApiKey() {
            return apiKey;
        }

        public String getEndpoint() {
            return endpoint;
        }

        public String getModelId() {
            return modelId;
        }

        public int getInputMaxTokens() {
            return inputMaxTokens;
        }

        public int getOutputMaxTokens() {
            return outputMaxTokens;
        }

        public int getTokenKiloPerMinute() {
            return tokenKiloPerMinute;
        }

        public String getGroup() {
            return group;
        }

        public int getPriority() {
            return priority;
        }

        public static String getApiVersion() {
            return API_VERSION;
        }

        @Override
        public String toString() {
            return "ClientConfig{" +
                    "id='" + id + '\'' +
                    ", endpoint='" + endpoint + '\'' +
                    ", modelId='" + modelId + '\'' +
                    ", group='" + group + '\'' +
                    ", priority=" + priority +
                    '}';
        }
    }
}
