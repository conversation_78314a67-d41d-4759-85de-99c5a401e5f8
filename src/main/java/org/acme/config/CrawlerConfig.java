package org.acme.config;

import jakarta.enterprise.context.ApplicationScoped;
import org.eclipse.microprofile.config.inject.ConfigProperty;

@ApplicationScoped
public class CrawlerConfig {

    @ConfigProperty(name = "crawler.useHeadlessBrowser", defaultValue = "false")
    boolean useHeadlessBrowser;
    
    @ConfigProperty(name = "crawler.headless.useragent", defaultValue = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    String headlessUserAgent;
    
    @ConfigProperty(name = "crawler.headless.timeout", defaultValue = "30000")
    int headlessTimeout;
    
    @ConfigProperty(name = "crawler.headless.slowMo", defaultValue = "50")
    int headlessSlowMo;

    public Boolean useHeadlessBrowser() {
        return useHeadlessBrowser;
    }

    public String getHeadlessUserAgent() {
        return headlessUserAgent;
    }

    public int getHeadlessTimeout() {
        return headlessTimeout;
    }
    
    public int getHeadlessSlowMo() {
        return headlessSlowMo;
    }
}
