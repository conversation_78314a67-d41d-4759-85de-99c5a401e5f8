package org.acme.config;

/**
 * Enum representing possible authentication cookie refresh outcomes.
 * Provides detailed status information about refresh attempts.
 */
public enum RefreshResult {
    /**
     * <PERSON><PERSON> was successfully refreshed with a new value
     */
    REFRESHED,
    
    /**
     * Refresh was attempted but failed
     */
    FAILED,
    
    /**
     * No refresh attempt made because a refresh is already in progress
     */
    IN_PROGRESS,
    
    /**
     * No refresh attempt made because the cooldown period hasn't elapsed
     */
    ON_COOLDOWN
}
