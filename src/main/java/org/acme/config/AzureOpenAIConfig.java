package org.acme.config;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.jboss.logging.Logger;

import jakarta.enterprise.context.ApplicationScoped;

/**
 * Configuration class for Azure OpenAI API clients.
 * Supports multiple client configurations with different priorities and groups.
 */
@ApplicationScoped
public class AzureOpenAIConfig {
    private static final Logger LOG = Logger.getLogger(AzureOpenAIConfig.class);

    private List<ClientConfig> clients = new ArrayList<>();

    public AzureOpenAIConfig() {
        // Default configuration
        addDefaultClient();

        // Add new configurations from your YAML
        addNewClients();

        LOG.infof("Initialized %d Azure OpenAI clients", clients.size());
    }

    private void addDefaultClient() {
        // Add the original default client
        ClientConfig defaultClient = new ClientConfig(
            "yut-gpt4o-eu",
            "67972d39fcfa49098bf45ecd4e1daaa2",
            "https://yut-oai-eu.openai.azure.com/",
            "yut-gpt4o-eu",
            4000,
            1000,
            240,
            "gpt4o",
            1
        );
        clients.add(defaultClient);
    }

    private void addNewClients() {
        // Add the new clients from your YAML
        clients.add(new ClientConfig(
            "yut-gpt35-0125-scu-1111",
            "2503f285c68442da98b1d83bca85af2d",
            "https://yut-oai-scu.openai.azure.com/",
            "yut-gpt35-0125-scu",
            1900,
            1000,
            240,
            "gpt35_0125",
            1
        ));

        clients.add(new ClientConfig(
            "yut-gpt35-0125-scu",
            "2503f285c68442da98b1d83bca85af2d",
            "https://yut-oai-scu.openai.azure.com/",
            "yut-gpt35-0125-scu",
            1900,
            1000,
            240,
            "gpt35_0613",
            1
        ));

        clients.add(new ClientConfig(
            "yut22-gpt35-0125-ce",
            "b44c7a611c6d4249885c108a77b70b00",
            "https://yut22-oai-ce.openai.azure.com/",
            "yut22-gpt35-0125-ce",
            1900,
            1000,
            299,
            "gpt35_1106",
            1
        ));

        clients.add(new ClientConfig(
            "yut001-gpt35-0125-scu",
            "59e5f07e79a0479d923516b415915592",
            "https://yut001-oai-scu.openai.azure.com/",
            "yut001-gpt35-0125-scu",
            1900,
            1000,
            240,
            "gpt35_1106",
            10
        ));
    }

    /**
     * Get a client configuration by ID
     * @param id Client ID
     * @return Optional containing the client if found
     */
    public Optional<ClientConfig> getClientById(String id) {
        return clients.stream()
                .filter(client -> client.getId().equals(id))
                .findFirst();
    }

    /**
     * Get a client configuration by group, selecting the one with highest priority
     * @param group Group name
     * @return Optional containing the highest priority client in the group
     */
    public Optional<ClientConfig> getClientByGroup(String group) {
        return clients.stream()
                .filter(client -> client.getGroup().equals(group))
                .min(Comparator.comparingInt(ClientConfig::getPriority));
    }

    /**
     * Get all available clients
     * @return List of all client configurations
     */
    public List<ClientConfig> getAllClients() {
        return new ArrayList<>(clients);
    }

    /**
     * Get all available client groups
     * @return List of unique group names
     */
    public List<String> getAvailableGroups() {
        return clients.stream()
                .map(ClientConfig::getGroup)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * Get the best available client based on priority
     * @return The client with the highest priority (lowest number)
     */
    public ClientConfig getBestClient() {
        return clients.stream()
                .min(Comparator.comparingInt(ClientConfig::getPriority))
                .orElse(clients.get(0)); // Fallback to first client if no priority order
    }

    /**
     * Configuration for a single Azure OpenAI client
     */
    public static class ClientConfig {
        private String id;
        private String apiKey;
        private String endpoint;
        private String deploymentId;
        private int inputMaxTokens;
        private int outputMaxTokens;
        private int tokenKiloPerMinute;
        private String group;
        private int priority;

        // API version is common across all clients
        private static final String API_VERSION = "2024-02-01";

        public ClientConfig(String id, String apiKey, String endpoint, String deploymentId,
                           int inputMaxTokens, int outputMaxTokens, int tokenKiloPerMinute,
                           String group, int priority) {
            this.id = id;
            this.apiKey = apiKey;
            this.endpoint = endpoint;
            this.deploymentId = deploymentId;
            this.inputMaxTokens = inputMaxTokens;
            this.outputMaxTokens = outputMaxTokens;
            this.tokenKiloPerMinute = tokenKiloPerMinute;
            this.group = group;
            this.priority = priority;
        }

        // Getters
        public String getId() { return id; }
        public String getApiKey() { return apiKey; }
        public String getEndpoint() { return endpoint; }
        public String getDeploymentId() { return deploymentId; }
        public int getInputMaxTokens() { return inputMaxTokens; }
        public int getOutputMaxTokens() { return outputMaxTokens; }
        public int getTokenKiloPerMinute() { return tokenKiloPerMinute; }
        public String getGroup() { return group; }
        public int getPriority() { return priority; }
        public String getApiVersion() { return API_VERSION; }

        @Override
        public String toString() {
            return String.format("Client[id=%s, group=%s, priority=%d, endpoint=%s]",
                id, group, priority, endpoint);
        }
    }
}
