package org.acme.config;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import org.acme.service.proxy.ProxyFetcherService;
import org.jboss.logging.Logger;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.enterprise.event.Observes;
import io.quarkus.runtime.StartupEvent;

/**
 * Configuration class for HTTP proxy pool.
 * Manages a pool of proxy servers that can be used for outbound connections.
 */
@ApplicationScoped
public class ProxyConfig {
    private static final Logger LOG = Logger.getLogger(ProxyConfig.class);

    private final List<ProxyEntry> proxyPool = Collections.synchronizedList(new ArrayList<>());
    private final AtomicInteger requestCounter = new AtomicInteger(0);
    private final Lock proxyRefreshLock = new ReentrantLock();
    
    // Enum for proxy source selection
    public enum ProxySource {
        PROXY_POOL,     // Use the dynamic proxy pool
        LOCAL_VPN       // Use the local VPN proxy
    }
    
    // Local VPN proxy configuration
    private static final String LOCAL_VPN_HOST = "127.0.0.1";
    private static final int LOCAL_VPN_PORT = 10802;
    private static final String LOCAL_VPN_GROUP = "local-vpn";
    
    // Current proxy source setting
    private ProxySource currentProxySource = ProxySource.PROXY_POOL;

    // Configuration for proxy pool management
    private int minProxies = 3; // Minimum number of proxies to maintain
    private int maxProxies = 5; // Maximum number of proxies in the pool
    private long proxyRefreshIntervalMinutes = 30; // How often to refresh proxies
    private boolean autoRefreshEnabled = true; // Whether to auto-refresh the proxy pool

    // Last time the proxy pool was refreshed
    private long lastRefreshTime = 0;

    @Inject
    ProxyFetcherService proxyFetcherService;

    public ProxyConfig() {
        // Constructor is empty, initialization happens in onStart
    }

    /**
     * Initialize the proxy pool when the application starts.
     * This is called automatically by Quarkus.
     */
    void onStart(@Observes StartupEvent ev) {
        // Initialize with default proxies if any
        initializeProxyPool();
        LOG.infof("Initialized proxy pool with %d proxies", proxyPool.size());

        // Start a background thread to periodically refresh the proxy pool
        if (autoRefreshEnabled) {
            startProxyRefreshThread();
        }
    }

    /**
     * Initialize the proxy pool with default entries and fetch international proxies.
     * In a production environment, this might load from configuration files or a database.
     */
    private void initializeProxyPool() {
        // Add default fallback proxies if needed
        // These are used if API fetching fails
        // addProxy("proxy1.example.com", 8080, "user1", "pass1", "fallback", 10);

        // Fetch initial set of proxies from the API
        refreshProxyPool();
    }

    /**
     * Starts a background thread that periodically refreshes the proxy pool.
     */
    private void startProxyRefreshThread() {
        Thread refreshThread = new Thread(() -> {
            LOG.info("Starting proxy pool refresh thread");

            while (!Thread.currentThread().isInterrupted()) {
                try {
                    // Sleep for the configured interval
                    Thread.sleep(TimeUnit.MINUTES.toMillis(proxyRefreshIntervalMinutes));

                    // Refresh the proxy pool
                    refreshProxyPool();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    LOG.info("Proxy pool refresh thread interrupted");
                    break;
                } catch (Exception e) {
                    LOG.errorf("Error in proxy pool refresh thread: %s", e.getMessage());
                    // Continue the loop even if there's an error
                }
            }
        }, "proxy-pool-refresh");

        // Set as daemon thread so it doesn't prevent application shutdown
        refreshThread.setDaemon(true);
        refreshThread.start();
    }

    /**
     * Refreshes the proxy pool by fetching new proxies if needed.
     * This method is synchronized to prevent multiple concurrent refreshes.
     *
     * @return Number of new proxies added
     */
    public int refreshProxyPool() {
        // Use a lock to prevent multiple concurrent refreshes
        if (!proxyRefreshLock.tryLock()) {
            LOG.info("Proxy pool refresh already in progress, skipping");
            return 0;
        }

        try {
            // Check if we need to refresh based on time elapsed
            long now = System.currentTimeMillis();
            if (now - lastRefreshTime < TimeUnit.MINUTES.toMillis(proxyRefreshIntervalMinutes / 2)) {
                LOG.info("Proxy pool was refreshed recently, skipping");
                return 0;
            }

            // Check if we need to refresh based on pool size
            int currentSize = proxyPool.size();
            if (currentSize >= minProxies) {
                LOG.infof("Proxy pool already has %d proxies (min: %d), no need to refresh",
                    currentSize, minProxies);
                return 0;
            }

            // Calculate how many proxies to fetch
            int fetchCount = Math.min(maxProxies - currentSize, minProxies - currentSize);

            LOG.infof("Refreshing proxy pool: current size=%d, fetching %d more proxies",
                currentSize, fetchCount);

            // Fetch new proxies using the ProxyFetcherService
            int addedCount = 0;
            if (proxyFetcherService != null) {
                addedCount = proxyFetcherService.fetchMultipleProxies(fetchCount);
                LOG.infof("Fetched %d/%d proxies from API", addedCount, fetchCount);
            } else {
                LOG.warn("ProxyFetcherService not available, cannot fetch new proxies");
            }

            // Update the last refresh time
            lastRefreshTime = now;

            return addedCount;
        } finally {
            proxyRefreshLock.unlock();
        }
    }

    /**
     * Add a proxy to the pool
     *
     * @param host Proxy host
     * @param port Proxy port
     * @param username Username for authentication (optional)
     * @param password Password for authentication (optional)
     * @param group Group name for categorization
     * @param priority Priority (lower number means higher priority)
     */
    public void addProxy(String host, int port, String username, String password, String group, int priority) {
        ProxyEntry proxy = new ProxyEntry(host, port, username, password, group, priority);
        proxyPool.add(proxy);
        LOG.infof("Added proxy %s to pool", proxy);
    }

    /**
     * Get the next proxy using round-robin selection
     * Automatically triggers a refresh if the pool is empty or below minimum threshold
     * 
     * @return The next proxy in the pool, or empty if pool is empty
     */
    public synchronized Optional<ProxyEntry> getNextProxy() {
        // Check if we should use the local VPN proxy
        if (currentProxySource == ProxySource.LOCAL_VPN) {
            LOG.debug("Using local VPN proxy");
            return Optional.of(createLocalVpnProxy());
        }
        
        // Using proxy pool - standard behavior
        // If the pool is empty or below minimum threshold, try to refresh it
        if (proxyPool.size() < minProxies) {
            // Try to refresh the pool
            refreshProxyPool();
        }

        if (proxyPool.isEmpty()) {
            LOG.warn("Proxy pool is empty, cannot provide a proxy");
            return Optional.empty();
        }

        // Use round-robin selection
        int idx = requestCounter.getAndIncrement() % proxyPool.size();
        return Optional.of(proxyPool.get(idx));
    }

    /**
     * Get a proxy by its group name
     *
     * @param group Group name
     * @return Optional containing a proxy from the specified group
     */
    public Optional<ProxyEntry> getProxyByGroup(String group) {
        return proxyPool.stream()
                .filter(proxy -> proxy.getGroup().equals(group))
                .findFirst();
    }

    /**
     * Get all proxies in the pool
     *
     * @return List of all proxy entries
     */
    public List<ProxyEntry> getAllProxies() {
        return Collections.unmodifiableList(proxyPool);
    }

    /**
     * Clear all proxies from the pool
     *
     * @param excludeGroups Optional list of groups to exclude from clearing
     */
    public void clearProxies(String... excludeGroups) {
        if (excludeGroups == null || excludeGroups.length == 0) {
            // Clear all proxies
            proxyPool.clear();
            LOG.info("Cleared all proxies from pool");
        } else {
            // Create a set of groups to exclude
            java.util.Set<String> excludeSet = java.util.Arrays.stream(excludeGroups)
                .collect(java.util.stream.Collectors.toSet());

            // Remove proxies not in the excluded groups
            int initialSize = proxyPool.size();
            proxyPool.removeIf(proxy -> !excludeSet.contains(proxy.getGroup()));
            int removedCount = initialSize - proxyPool.size();

            LOG.infof("Cleared %d proxies from pool (excluded groups: %s)",
                removedCount, String.join(", ", excludeSet));
        }
    }

    /**
     * Check if the proxy pool is empty
     *
     * @return true if the pool is empty, false otherwise
     */
    public boolean isEmpty() {
        return proxyPool.isEmpty();
    }

    /**
     * Get the number of proxies in the pool
     *
     * @return The size of the proxy pool
     */
    public int size() {
        return proxyPool.size();
    }

    /**
     * Configure the proxy pool settings
     *
     * @param minProxies Minimum number of proxies to maintain
     * @param maxProxies Maximum number of proxies in the pool
     * @param refreshIntervalMinutes How often to refresh proxies (in minutes)
     * @param autoRefresh Whether to auto-refresh the proxy pool
     */
    public void configureProxyPool(int minProxies, int maxProxies, long refreshIntervalMinutes, boolean autoRefresh) {
        this.minProxies = minProxies;
        this.maxProxies = maxProxies;
        this.proxyRefreshIntervalMinutes = refreshIntervalMinutes;
        this.autoRefreshEnabled = autoRefresh;

        LOG.infof("Configured proxy pool: min=%d, max=%d, refreshInterval=%d min, autoRefresh=%s",
            minProxies, maxProxies, refreshIntervalMinutes, autoRefresh);
    }

    /**
     * Creates a ProxyEntry for the local VPN proxy
     * 
     * @return ProxyEntry for local VPN
     */
    private ProxyEntry createLocalVpnProxy() {
        return new ProxyEntry(LOCAL_VPN_HOST, LOCAL_VPN_PORT, null, null, LOCAL_VPN_GROUP, 0);
    }
    
    /**
     * Set which proxy source to use
     * 
     * @param source The proxy source to use (PROXY_POOL or LOCAL_VPN)
     */
    public void setProxySource(ProxySource source) {
        this.currentProxySource = source;
        LOG.infof("Switched proxy source to: %s", source);
    }
    
    /**
     * Get the current proxy source
     * 
     * @return The current proxy source
     */
    public ProxySource getProxySource() {
        return this.currentProxySource;
    }
    
    /**
     * Get the current proxy pool configuration
     *
     * @return Map containing the current configuration
     */
    public Map<String, Object> getProxyPoolConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("minProxies", minProxies);
        config.put("maxProxies", maxProxies);
        config.put("proxyRefreshIntervalMinutes", proxyRefreshIntervalMinutes);
        config.put("autoRefreshEnabled", autoRefreshEnabled);
        config.put("currentPoolSize", proxyPool.size());
        config.put("lastRefreshTime", lastRefreshTime);
        config.put("currentProxySource", currentProxySource);
        config.put("localVpnProxy", LOCAL_VPN_HOST + ":" + LOCAL_VPN_PORT);

        // Calculate time until next refresh
        long timeUntilNextRefresh = 0;
        if (autoRefreshEnabled && lastRefreshTime > 0) {
            long nextRefreshTime = lastRefreshTime + TimeUnit.MINUTES.toMillis(proxyRefreshIntervalMinutes);
            timeUntilNextRefresh = Math.max(0, nextRefreshTime - System.currentTimeMillis());
        }
        config.put("timeUntilNextRefreshMs", timeUntilNextRefresh);
        config.put("timeUntilNextRefreshMinutes", TimeUnit.MILLISECONDS.toMinutes(timeUntilNextRefresh));

        return config;
    }

    /**
     * Represents a single proxy server entry in the pool
     */
    public static class ProxyEntry {
        private final String host;
        private final int port;
        private final String username;
        private final String password;
        private final String group;
        private final int priority;

        public ProxyEntry(String host, int port, String username, String password, String group, int priority) {
            this.host = host;
            this.port = port;
            this.username = username;
            this.password = password;
            this.group = group;
            this.priority = priority;
        }

        public String getHost() {
            return host;
        }

        public int getPort() {
            return port;
        }

        public String getUsername() {
            return username;
        }

        public String getPassword() {
            return password;
        }

        public String getGroup() {
            return group;
        }

        public int getPriority() {
            return priority;
        }

        public boolean hasAuthentication() {
            return username != null && !username.isEmpty() && password != null;
        }

        @Override
        public String toString() {
            return String.format("Proxy[host=%s, port=%d, group=%s, priority=%d, auth=%s]",
                    host, port, group, priority, hasAuthentication() ? "yes" : "no");
        }
    }
}
