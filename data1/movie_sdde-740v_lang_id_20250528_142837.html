<!DOCTYPE html>
<html lang="id">
    <head>
                                                        <link rel="alternate" hreflang="zh-Hant" href="https://missav.ai/dm39/sdde-740" />
                                    <link rel="alternate" hreflang="zh-<PERSON>" href="https://missav.ai/dm39/cn/sdde-740" />
                                    <link rel="alternate" hreflang="en" href="https://missav.ai/dm39/en/sdde-740" />
                                    <link rel="alternate" hreflang="ja" href="https://missav.ai/dm39/ja/sdde-740" />
                                    <link rel="alternate" hreflang="ko" href="https://missav.ai/dm39/ko/sdde-740" />
                                    <link rel="alternate" hreflang="ms" href="https://missav.ai/dm39/ms/sdde-740" />
                                    <link rel="alternate" hreflang="th" href="https://missav.ai/dm39/th/sdde-740" />
                                    <link rel="alternate" hreflang="de" href="https://missav.ai/dm39/de/sdde-740" />
                                    <link rel="alternate" hreflang="fr" href="https://missav.ai/dm39/fr/sdde-740" />
                                    <link rel="alternate" hreflang="vi" href="https://missav.ai/dm39/vi/sdde-740" />
                                    <link rel="alternate" hreflang="id" href="https://missav.ai/dm39/id/sdde-740" />
                                    <link rel="alternate" hreflang="fil" href="https://missav.ai/dm39/fil/sdde-740" />
                                    <link rel="alternate" hreflang="pt" href="https://missav.ai/dm39/pt/sdde-740" />
                                        <link rel="alternate" hreflang="x-default" href="https://missav.ai/dm39/en/sdde-740" />
                <meta charset="utf-8" />
        <meta http-equiv="x-ua-compatible" content="ie=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
        <meta name="description" content="[Edisi bonus SP termasuk sudut khusus] Musim pelatihan karyawan baru penerbangan vagina telah tiba pada tahun 2024! Seperti biasa, silakan nikmati situs pe" />
        <meta name="keywords" content="春原未来, 花芽ありす, 及川うみ, 五十嵐清華, 夏目れみ, 響乃うた, 七瀬シノン, 朝野りる, 天美めあ, 水瀬りた, 安藤はる, 道久てん, 制服, スチュワーデス, 巨尻, 騎乗位, ハーレム, ハイビジョン, 4K, 「制服・下着・全裸」でおもてなし またがりオマ○コ航空, SODクリエイト, 坂井シベリア, SENZ" />
        <meta name="author" content="">
        <meta name="referrer" content="unsafe-url" />
        <meta property="og:url" content="https://missav.ai/dm39/id/sdde-740" />
        <meta property="og:site_name" content="MissAV" />
        <meta property="og:title" content="SDDE-740 [Versi bonus SP] Perhotelan dengan &quot;seragam, pakaian dalam, dan ketelanjangan&quot; mengangkangi penerbangan vagina 2024 edisi pelatihan skala besar CA baru dengan total 11 orang + 1 instruktur khusus 6 bagian pelajaran kabin vagina grup 43 menit sudut bonus ditambahkan! Total durasi: 190 menit, 6+1 bagian - Mirai Sunohara" />
        <meta property="og:description" content="[Edisi bonus SP termasuk sudut khusus] Musim pelatihan karyawan baru penerbangan vagina telah tiba pada tahun 2024! Seperti biasa, silakan nikmati situs pelatihan penerbangan straddle terbesar yang diselenggarakan oleh total 11 CA dan instruktur baru! #kostumbanyakorang #pramugari #asli #kostumasli #celanaketat #sapatelanjang #airways #airlines" />
        <meta property="og:type" content="video.other" />
        <meta property="og:image" content="https://fourhoi.com/sdde-740/cover-n.jpg" />
        <meta name="twitter:image" content="https://fourhoi.com/sdde-740/cover-n.jpg" />
        <meta name="twitter:image:alt" content="SDDE-740 [Versi bonus SP] Perhotelan dengan &quot;seragam, pakaian dalam, dan ketelanjangan&quot; mengangkangi penerbangan vagina 2024 edisi pelatihan skala besar CA baru dengan total 11 orang + 1 instruktur khusus 6 bagian pelajaran kabin vagina grup 43 menit sudut bonus ditambahkan! Total durasi: 190 menit, 6+1 bagian - Mirai Sunohara" />
        <meta name="twitter:title" content="SDDE-740 [Versi bonus SP] Perhotelan dengan &quot;seragam, pakaian dalam, dan ketelanjangan&quot; mengangkangi penerbangan vagina 2024 edisi pelatihan skala besar CA baru dengan total 11 orang + 1 instruktur khusus 6 bagian pelajaran kabin vagina grup 43 menit sudut bonus ditambahkan! Total durasi: 190 menit, 6+1 bagian - Mirai Sunohara" />
        <meta name="twitter:description" content="[Edisi bonus SP termasuk sudut khusus] Musim pelatihan karyawan baru penerbangan vagina telah tiba pada tahun 2024! Seperti biasa, silakan nikmati situs pelatihan penerbangan straddle terbesar yang diselenggarakan oleh total 11 CA dan instruktur baru! #kostumbanyakorang #pramugari #asli #kostumasli #celanaketat #sapatelanjang #airways #airlines" />
        <meta name="twitter:card" content="summary_large_image" />
                    <meta name="twitter:site" content="@missav_daily" />
            <meta name="twitter:creator" content="@missav_daily" />
                <title>SDDE-740 [Versi bonus SP] Perhotelan dengan &quot;seragam, p</title>
        <link rel="icon" type="image/x-icon" href="https://missav.ai/img/favicon.ico" />
        <link rel="icon" type="image/png" href="https://missav.ai/img/favicon.png" />
        <link rel="preload" href="https://missav.ai/fonts/inter-v3-latin-500.woff2" as="font" type="font/woff2" crossorigin />
        <link rel="preload" href="https://missav.ai/fonts/halant-v8-latin-500.woff2" as="font" type="font/woff2" crossorigin />
        <style>
            @font-face {
                font-family: 'Inter';
                font-style: normal;
                font-weight: 500;
                src: url('https://missav.ai/fonts/inter-v3-latin-500.eot');
                src: local(''),
                url('https://missav.ai/fonts/inter-v3-latin-500.eot?#iefix') format('embedded-opentype'),
                url('https://missav.ai/fonts/inter-v3-latin-500.woff2') format('woff2'),
                url('https://missav.ai/fonts/inter-v3-latin-500.woff') format('woff'),
                url('https://missav.ai/fonts/inter-v3-latin-500.ttf') format('truetype'),
                url('https://missav.ai/fonts/inter-v3-latin-500.svg#Inter') format('svg');
                font-display: swap;
            }

            @font-face {
                font-family: 'Halant';
                font-style: normal;
                font-weight: 500;
                src: url('https://missav.ai/fonts/halant-v8-latin-500.eot');
                src: local(''),
                url('https://missav.ai/fonts/halant-v8-latin-500.eot?#iefix') format('embedded-opentype'),
                url('https://missav.ai/fonts/halant-v8-latin-500.woff2') format('woff2'),
                url('https://missav.ai/fonts/halant-v8-latin-500.woff') format('woff'),
                url('https://missav.ai/fonts/halant-v8-latin-500.ttf') format('truetype'),
                url('https://missav.ai/fonts/halant-v8-latin-500.svg#Halant') format('svg');
                font-display: swap;
            }
        </style>
        <script>
            window.dataLayer = window.dataLayer || [];
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','GTM-WLS867RZ');
        </script>
            <link rel="canonical" href="https://missav.ai/dm39/id/sdde-740" />
    <link rel="preload" as="image" href="https://fourhoi.com/sdde-740/cover-n.jpg" />
    <meta property="og:video:release_date" content="2024-11-19" />
            <meta property="og:video:duration" content="11508" />
                <meta property="og:video:actor" content="Mirai Sunohara" />
            <meta property="og:video:actor" content="kuncup bunga alice" />
            <meta property="og:video:actor" content="Oikawa Umi" />
            <meta property="og:video:actor" content="Seika Igarashi" />
            <meta property="og:video:actor" content="Remi Natsume" />
            <meta property="og:video:actor" content="Hibino Uta" />
            <meta property="og:video:actor" content="Nanase Sinon" />
            <meta property="og:video:actor" content="Riru Asano" />
            <meta property="og:video:actor" content="Amami Mea" />
            <meta property="og:video:actor" content="Rita Minase" />
            <meta property="og:video:actor" content="Haru Ando" />
            <meta property="og:video:actor" content="Michikuten" />
                    <meta property="og:video:director" content="坂井シベリア" />
            <link rel="preload" as="script" href="https://cdnjs.cloudflare.com/ajax/libs/plyr/3.6.8/plyr.min.js" />
    <link rel="preload" as="script" href="https://missav.ai/js/plyr-plugin-thumbnail.js" />
    <link rel="preload" as="script" href="https://cdnjs.cloudflare.com/ajax/libs/hls.js/1.4.3/hls.min.js" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/plyr/3.6.8/plyr.css" />
        <link rel="preload" as="style" href="https://missav.ai/build/assets/app.b9f2710a.css" /><link rel="modulepreload" href="https://missav.ai/build/assets/app.1aad5686.js" /><link rel="stylesheet" href="https://missav.ai/build/assets/app.b9f2710a.css" data-navigate-track="reload" /><script type="module" src="https://missav.ai/build/assets/app.1aad5686.js" data-navigate-track="reload"></script>    </head>
    <body class="relative">
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WLS867RZ" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
        <div
            x-data="{
                showDropdown: null,
                showCollapse: null,
                showLocaleSwitcher: false,
                showSearch: window.location.pathname.startsWith('/search/'),
                searchHistory: [],
                showPreview: null,
                holdPreviews: [],
                showModal: {
                    login: false,
                },
                currentPage: 'login',
                locale: 'id',
                isMain: true,
                isChinese: false,
                isEnglish: false,
                fallbackLocale: 'zh',
                user: null,
                loadedRecaptcha: false,
                loginCallback: null,
                blockedKeywords: {&quot;abuse&quot;:&quot;play&quot;,&quot;asphyxia&quot;:&quot;play&quot;,&quot;behead&quot;:&quot;play&quot;,&quot;bleed&quot;:&quot;play&quot;,&quot;blood&quot;:&quot;play&quot;,&quot;child&quot;:&quot;play&quot;,&quot;choke&quot;:&quot;play&quot;,&quot;choking&quot;:&quot;play&quot;,&quot;decapitation&quot;:&quot;play&quot;,&quot;drugged&quot;:&quot;played&quot;,&quot;forced&quot;:&quot;played&quot;,&quot;kid&quot;:&quot;play&quot;,&quot;kill&quot;:&quot;play&quot;,&quot;loli&quot;:&quot;play&quot;,&quot;murder&quot;:&quot;play&quot;,&quot;rape&quot;:&quot;play&quot;,&quot;raped&quot;:&quot;played&quot;,&quot;raping&quot;:&quot;playing&quot;,&quot;shota&quot;:&quot;play&quot;,&quot;snuff&quot;:&quot;play&quot;,&quot;strangle&quot;:&quot;play&quot;,&quot;torture&quot;:&quot;play&quot;,&quot;abduct&quot;:&quot;play&quot;,&quot;incest&quot;:&quot;play&quot;,&quot;underaged&quot;:&quot;play&quot;},
                dmcaDummy: 'dm39',
                currentSearchPlaceholderIndex: 0,
                searchPlaceholderTexts: [
                    'Contoh: Siswi Pirang Payudara Besar',
                    'Gunakan &quot;+&quot; untuk menggabungkan beberapa kata kunci',
                ],
                currentSearchPlaceholderText() {
                    return this.searchPlaceholderTexts[this.currentSearchPlaceholderIndex]
                },
                slot() {
                    return parseInt(window.localStorage.getItem('slot'))
                },
                isDesktop() {
                    return document.documentElement.clientWidth >= 1024
                },
                isThreeColumns() {
                    return document.documentElement.clientWidth >= 768 && document.documentElement.clientWidth < 1280
                },
                cdnUrl(path) {
                    return `https://fourhoi.com${path}`
                },
                itemUrl(item) {
                    let dmPrefix = item.dm ? `/dm${item.dm}` : '';
                    let url = this.locale === 'zh' ? `${dmPrefix}/${item.dvd_id}` : `${dmPrefix}/${this.locale}/${item.dvd_id}`

                    if (item.recommend_id) {
                        url += `#${item.recommend_id}`

                        if (item.scenario) {
                            url += `_${item.scenario}`
                        }
                    } else if (this.isMain && item.scenario) {
                        url += `#${item.scenario}`
                    }

                    return url
                },
                generateFullItemTitle(item) {
                    const titleField = this.translatedField('title', this.locale)

                    item['full_title'] = item['dvd_id']
                        ? `${item['dvd_id'].toUpperCase().replace('-UNCENSORED-LEAK', '').replace('-CHINESE-SUBTITLE', '').replace('-ENGLISH-SUBTITLE', '')} ${item[titleField]}`
                        : '&nbsp;'

                    return item
                },
                translatedField(field, locale) {
                    return locale === 'ja' ? field : `${field}_${locale}`
                },
                translatedValue(object, field, locale) {
                    return this.purify(object[this.translatedField(field, locale)])
                },
                toggleSearch() {
                    let type

                    this.showSearch = ! this.showSearch

                    if (this.showSearch) {
                        this.showDropdown = null

                        this.$nextTick(() => {
                            this.$refs.search.select()
                        })
                    }
                },
                search(keyword) {
                    keyword = keyword.trim()

                    if (! keyword) {
                        return
                    }

                    keyword = encodeURIComponent(keyword.replace('\\', ''))

                    if (window.location.href.includes('/legacy')) {
                        window.location.href = this.locale === this.fallbackLocale ? `/legacy?keyword=${keyword}` : `/${this.locale}/legacy?keyword=${keyword}`
                        return
                    }

                    let searchHistory = this.getSearchHistory()

                    if (searchHistory.includes(keyword)) {
                        searchHistory.splice(searchHistory.indexOf(keyword), 1)
                    }

                    searchHistory.unshift(keyword);

                    window.Cookies.set('search_history', JSON.stringify(searchHistory), { expires: 365 })

                    window.location.href = this.locale === this.fallbackLocale ? `/search/${keyword}` : `/${this.locale}/search/${keyword}`
                },
                getSearchHistory() {
                    const searchHistoryText = window.Cookies.get('search_history')

                    if (! searchHistoryText) {
                        return []
                    }

                    return JSON.parse(searchHistoryText).slice(0, 5)
                },
                clearSearchHistory() {
                    window.Cookies.remove('search_history')

                    this.searchHistory = []
                },
                clearWatchHistory() {
                    this.showDropdown = null

                    if (! confirm(`Yakin ingin menghapus semua histori tontonan?`)) {
                        return
                    }

                    window.axios.delete('https://missav.ai/api/history').then(response => {
                        alert(`Histori tontonan telah dihapus.`)

                        window.location.href = 'https://missav.ai/id/history'
                    })
                },
                localizedUrl(locale) {
                    let path = window.location.pathname

                    if (! path.endsWith('/')) {
                        path = `${path}/`
                    }

                    path = path + window.location.search

                    if (locale === this.fallbackLocale) {
                        return this.removeTrailingSlash(path
                            .replace('/cn/', '/').replace('/en/', '/').replace('/ja/', '/').replace('/ko/', '/').replace('/ms/', '/').replace('/th/', '/').replace('/de/', '/').replace('/fr/', '/').replace('/vi/', '/').replace('/id/', '/').replace('/fil/', '/').replace('/pt/', '/')
                        )
                    }

                    if (path.includes('/cn/') || path.includes('/en/') || path.includes('/ja/') || path.includes('/ko/') || path.includes('/ms/') || path.includes('/th/') || path.includes('/de/') || path.includes('/fr/') || path.includes('/vi/') || path.includes('/id/') || path.includes('/fil/') || path.includes('/pt/')) {
                        return this.removeTrailingSlash(path
                            .replace('/cn/', `/${locale}/`).replace('/en/', `/${locale}/`).replace('/ja/', `/${locale}/`).replace('/ko/', `/${locale}/`).replace('/ms/', `/${locale}/`).replace('/th/', `/${locale}/`).replace('/de/', `/${locale}/`).replace('/fr/', `/${locale}/`).replace('/vi/', `/${locale}/`).replace('/id/', `/${locale}/`).replace('/fil/', `/${locale}/`).replace('/pt/', `/${locale}/`)
                        )
                    }

                    return this.removeTrailingSlash(
                        this.dmcaDummy ? path.replace(this.dmcaDummy, `${this.dmcaDummy}/${locale}`) : `/${locale}${path}`
                    )
                },
                removeTrailingSlash(url) {
                    if (url.substr(url.length - 1) === '/') {
                        return url.slice(0, -1)
                    }

                    return url.replace('/?', '?')
                },
                redirectToBaseLocalizedUrl() {
                    window.Cookies.set('localized', 1, { expires: 30 })

                    setTimeout(() => {
                        window.location.href = '/?localized=1'
                    }, 100)
                },
                loadRecaptcha() {
                    if (! this.loadedRecaptcha) {
                        this.loadedRecaptcha = true

                        let script = document.createElement('script')
                        script.type = 'text/javascript';
                        script.src = 'https://www.google.com/recaptcha/api.js?render=6Leez8oZAAAAABJqF0uuw35s7N50I1pZkIJBO1QT'

                        document.getElementsByTagName('head')[0].appendChild(script)
                    }
                },
                requireRecaptcha(callback) {
                    grecaptcha.ready(() => {
                        grecaptcha.execute('6Leez8oZAAAAABJqF0uuw35s7N50I1pZkIJBO1QT', { action: 'submit' }).then(recaptchaToken => {
                            callback(recaptchaToken)
                        })
                    })
                },
                showLoginModal(page) {
                    this.loadRecaptcha()

                    this.showModal.login = true

                    if (page) {
                        this.currentPage = page
                    }
                },
                requireLogin(callback) {
                    if (! this.user) {
                        this.loginCallback = callback
                        this.showLoginModal()

                        return
                    }

                    callback()
                },
                handleErrorResponse(page, error) {
                    this.errors[page] = error.response.data.errors
                },
                setPreview(id) {
                    if (window.innerWidth < 1024) {
                        return
                    }

                    if (this.showPreview) {
                        const previousPreview = document.getElementById(`preview-${this.showPreview}`)

                        if (previousPreview) {
                            previousPreview.pause()
                        }
                    }

                    if (! id) {
                        this.showPreview = id
                        return
                    }

                    this.playPreview(id)
                },
                clickPreview(id) {
                    if (this.isDesktop()) {
                        return
                    }

                    if (! this.holdPreviews.includes(id)) {
                        event.preventDefault()

                        this.holdPreviews.push(id)

                        this.playPreview(id)
                    }
                },
                playPreview(id) {
                    const preview = document.getElementById(`preview-${id}`)

                    if (! preview.getAttribute('src')) {
                        preview.addEventListener('loadedmetadata', event => {
                            event.target.play()

                            this.showPreview = id
                        })

                        preview.setAttribute('src', preview.getAttribute('data-src'))
                    } else {
                        preview.play()

                        this.showPreview = id
                    }
                },
                initLozad() {
                    window.lozad('.lozad', {
                        loaded: function(element) {
                            element.classList.remove('lozad')
                        },
                    }).observe()
                },
                purify(text) {
                    for (key in this.blockedKeywords) {
                        text = text.replaceAll(new RegExp(key, 'gi'), this.blockedKeywords[key])
                    }

                    return text
                },
            }"
            x-init="$nextTick(() => {
                for (element of document.getElementsByClassName('font-serif')) {
                    element.style.visibility = 'visible'
                }

                searchHistory = getSearchHistory()

                window.user_uuid = Cookies.get('user_uuid')

                if (! window.user_uuid) {
                    if (window.crypto && window.crypto.randomUUID) {
                        window.user_uuid = window.crypto.randomUUID()
                    } else {
                        const generateUUID = () => {
                            let d = new Date().getTime()
                            let d2 = ((typeof performance !== 'undefined') && performance.now && (performance.now()*1000)) || 0

                            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
                                let r = Math.random() * 16

                                if (d > 0) {
                                    r = (d + r) % 16 | 0
                                    d = Math.floor(d / 16)
                                } else {
                                    r = (d2 + r) % 16 | 0
                                    d2 = Math.floor(d2 / 16)
                                }

                                return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16)
                            });
                        }

                        window.user_uuid = generateUUID()
                    }

                    Cookies.set('user_uuid', window.user_uuid, { expires: 365 })
                }

                if (! window.localStorage.getItem('slot')) {
                    window.localStorage.setItem('slot', Math.floor(Math.random() * 12) + 1);
                }

                const iframes = document.querySelectorAll('iframe[data-src]')
                let iframe

                for (iframe of iframes) {
                    iframe.setAttribute('src', iframe.getAttribute('data-src'))
                }

                setInterval(() => {
                    currentSearchPlaceholderIndex = currentSearchPlaceholderIndex + 1 < searchPlaceholderTexts.length
                        ? currentSearchPlaceholderIndex + 1
                        : 0
                }, 3000)
            })"
        >
            <div
    x-cloak
    x-show="showModal.login"
    class="fixed z-max top-0 left-0 w-screen h-screen overflow-y-auto"
    aria-labelledby="modal-title"
    role="dialog"
    aria-modal="true"
>
    <div
        x-data="{
            email: '',
            username: '',
            old_password: '',
            password: '',
            password_confirmation: '',
            remember: true,
            loading: {
                loading: false,
                register: false,
                forget: false,
                changePassword: false,
            },
            errors: {
                login: {},
                register: {},
                forget: {},
                changePassword: {},
            },
            success: {
                forget: false,
                changePassword: false,
            },
            login() {
                window.axios.post('https://missav.ai/id/api/login', {
                    email: this.email,
                    password: this.password,
                    remember: this.remember,
                }).then(response => {
                    this.handleUserResponse(response)
                }).catch(error => {
                    this.handleErrorResponse('login', error)
                }).then(() => {
                    this.loading.login = false
                })

                this.loading.login = true
                this.errors.login = {}
            },
            register() {
                this.requireRecaptcha(recaptchaToken => {
                    window.axios.post('https://missav.ai/id/api/register', {
                        email: this.email,
                        username: this.username,
                        password: this.password,
                        password_confirmation: this.password_confirmation,
                        recaptcha_token: recaptchaToken,
                    }).then(response => {
                        this.handleUserResponse(response)
                    }).catch(error => {
                        this.handleErrorResponse('register', error)
                    }).then(() => {
                        this.loading.register = false
                    })
                })

                this.loading.register = true
                this.errors.register = {}
            },
            forget() {
                this.requireRecaptcha(recaptchaToken => {
                    window.axios.post('https://missav.ai/id/api/forget', {
                        email: this.email,
                        recaptcha_token: recaptchaToken,
                    }).then(response => {
                        this.success.forget = true
                    }).catch(error => {
                        this.handleErrorResponse('forget', error)
                    }).then(() => {
                        this.loading.forget = false
                    })
                })

                this.loading.forget = true
                this.errors.forget = {}
            },
            changePassword() {
                this.requireRecaptcha(recaptchaToken => {
                    window.axios.post('https://missav.ai/api/password/update', {
                        old_password: this.old_password,
                        password: this.password,
                        password_confirmation: this.password_confirmation,
                        recaptcha_token: recaptchaToken,
                    }).then(response => {
                        this.success.changePassword = true
                    }).catch(error => {
                        this.handleErrorResponse('changePassword', error)
                    }).then(() => {
                        this.loading.changePassword = false
                    })
                })

                this.loading.changePassword = true
                this.errors.changePassword = {}
            },
            handleUserResponse(response) {
                this.user = response.data.user
                this.showModal.login = false

                if (this.loginCallback) {
                    this.loginCallback()
                }
            },
        }"
        class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
    >
        <div
            @click.prevent="showModal.login = false"
            class="fixed top-0 left-0 w-screen h-screen bg-black bg-opacity-75 transition-opacity"
            aria-hidden="true"
        ></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="w-screen inline-block align-bottom bg-nord1 rounded-lg p-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-sm sm:w-full sm:p-6">
            <form
                x-show="currentPage === 'login'"
                @submit.prevent="login"
                class="space-y-4 sm:space-y-6"
            >
                <div>
                    <h2 class="text-center text-2xl text-nord4">
                        masuk ke akun Anda
                    </h2>
                    <p class="mt-2 text-center text-sm leading-5 text-nord5 max-w">
                        ATAU
                        <a @click.prevent="currentPage = 'register'" href="#" class="font-medium text-nord13 hover:text-nord8 focus:outline-none focus:underline">
                            Daftarkan akun baru
                        </a>
                    </p>
                </div>
                <div x-cloak x-show="Object.keys(errors.login).length" class="rounded-md bg-red-50 p-4 text-sm text-red-700">
    <ul role="list" class="list-disc pl-5 space-y-1">
        <template x-for="error in errors.login">
            <li x-text="error[0]"></li>
        </template>
    </ul>
</div>
                <div class="text-group">
                    <div
            :class="{ 'bg-red-100': errors.login.email }"
        class="rounded-b-none"
>
    <label
                    :class="{ 'text-red-800': errors.login.email }"
                for="login_email"
    >Surel</label>
    <input
        x-model="email"
                    :class="{ 'bg-red-100': errors.login.email }"
                type="text"
        id="login_email"
                    required
                            autocomplete="section-login username"
            >
</div>
                    <div
            :class="{ 'bg-red-100': errors.login.password }"
        class="rounded-t-none"
>
    <label
                    :class="{ 'text-red-800': errors.login.password }"
                for="login_password"
    >Kata sandi</label>
    <input
        x-model="password"
                    :class="{ 'bg-red-100': errors.login.password }"
                type="password"
        id="login_password"
                    required
                            autocomplete="section-login current-password"
            >
</div>
                </div>
                <div class="relative flex items-start justify-between">
                    <div class="flex">
                        <div class="flex items-center h-5">
                            <input x-model="remember" id="login_remember" aria-describedby="remember-me" type="checkbox" class="focus:ring-primary h-4 w-4 text-primary border-gray-300 rounded">
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="login_remember" class="font-medium text-nord4">Ingat saya</label>
                        </div>
                    </div>
                    <div class="text-sm">
                        <a @click.prevent="currentPage = 'forget'" href="#" class="font-medium text-nord13 hover:text-nord8 focus:outline-none focus:underline">
                            Lupa Password?
                        </a>
                    </div>
                </div>
                <div class="block w-full rounded-md shadow-sm">
                    <button :disable="loading.login" type="submit" class="button-primary button-block">
                        <svg x-cloak x-show="loading.login" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
</svg>
                        Gabung
                    </button>
                </div>
            </form>
            <form
                x-show="currentPage === 'register'"
                @submit.prevent="register"
                class="space-y-4 sm:space-y-6"
            >
                <div>
                    <h2 class="text-center text-2xl text-nord4">
                        Daftarkan akun baru
                    </h2>
                    <p class="mt-2 text-center text-sm leading-5 text-nord5 max-w">
                        ATAU
                        <a @click.prevent="currentPage = 'login'" href="#" class="font-medium text-nord13 hover:text-nord8 focus:outline-none focus:underline">
                            masuk ke akun Anda
                        </a>
                    </p>
                </div>
                <div x-cloak x-show="Object.keys(errors.register).length" class="rounded-md bg-red-50 p-4 text-sm text-red-700">
    <ul role="list" class="list-disc pl-5 space-y-1">
        <template x-for="error in errors.register">
            <li x-text="error[0]"></li>
        </template>
    </ul>
</div>
                <div class="text-group">
                    <div
            :class="{ 'bg-red-100': errors.register.email }"
        class="rounded-b-none"
>
    <label
                    :class="{ 'text-red-800': errors.register.email }"
                for="register_email"
    >Surel</label>
    <input
        x-model="email"
                    :class="{ 'bg-red-100': errors.register.email }"
                type="email"
        id="register_email"
                    required
                            autocomplete="section-register username"
            >
</div>
                    <div
            :class="{ 'bg-red-100': errors.register.username }"
        class="rounded-t-none"
>
    <label
                    :class="{ 'text-red-800': errors.register.username }"
                for="register_username"
    >Nama belakang</label>
    <input
        x-model="username"
                    :class="{ 'bg-red-100': errors.register.username }"
                type="text"
        id="register_username"
                    required
                            autocomplete="section-register username"
            >
</div>
                </div>
                <div class="text-group">
                    <div
            :class="{ 'bg-red-100': errors.register.password }"
        class="rounded-b-none"
>
    <label
                    :class="{ 'text-red-800': errors.register.password }"
                for="register_password"
    >Kata sandi</label>
    <input
        x-model="password"
                    :class="{ 'bg-red-100': errors.register.password }"
                type="password"
        id="register_password"
                    required
                            autocomplete="section-register new-password"
            >
</div>
                    <div
            :class="{ 'bg-red-100': errors.register.password_confirmation }"
        class="rounded-t-none"
>
    <label
                    :class="{ 'text-red-800': errors.register.password_confirmation }"
                for="register_password_confirmation"
    >Konfirmasi sandi</label>
    <input
        x-model="password_confirmation"
                    :class="{ 'bg-red-100': errors.register.password_confirmation }"
                type="password"
        id="register_password_confirmation"
                    required
                            autocomplete="section-register new-password"
            >
</div>
                </div>
                <div class="block w-full rounded-md shadow-sm">
                    <button :disabled="loading.register" type="submit" class="button-primary button-block">
                        <svg x-cloak x-show="loading.register" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
</svg>
                        Daftar
                    </button>
                </div>
            </form>
            <form
                x-show="currentPage === 'forget'"
                @submit.prevent="forget"
                class="space-y-4 sm:space-y-6"
            >
                <div>
                    <h2 class="text-center text-2xl text-nord4">
                        Lupa Password?
                    </h2>
                    <p class="mt-2 text-center text-sm leading-5 text-nord5 max-w">
                        ATAU
                        <a @click.prevent="currentPage = 'login'" href="#" class="font-medium text-nord13 hover:text-nord8 focus:outline-none focus:underline">
                            masuk ke akun Anda
                        </a>
                    </p>
                </div>
                <div x-cloak x-show="Object.keys(errors.forget).length" class="rounded-md bg-red-50 p-4 text-sm text-red-700">
    <ul role="list" class="list-disc pl-5 space-y-1">
        <template x-for="error in errors.forget">
            <li x-text="error[0]"></li>
        </template>
    </ul>
</div>
                <div x-cloak class="rounded-md bg-emerald-50 p-4" x-show="success.forget">
    <div class="flex">
        <div class="shrink-0">
            <svg class="h-5 w-5 text-emerald-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
        </div>
        <div class="ml-3">
            <p class="text-sm font-medium text-emerald-800">
                Kami sudah mengirim surel yang berisi tautan untuk mereset kata sandi Anda!
            </p>
        </div>
    </div>
</div>
                <div x-show="! success.forget" class="text-group">
                    <div
            :class="{ 'bg-red-100': errors.forget.email }"
        
>
    <label
                    :class="{ 'text-red-800': errors.forget.email }"
                for="forget_email"
    >Surel</label>
    <input
        x-model="email"
                    :class="{ 'bg-red-100': errors.forget.email }"
                type="email"
        id="forget_email"
                    required
                            autocomplete="section-forget username"
            >
</div>
                </div>
                <div x-show="! success.forget" class="block w-full rounded-md shadow-sm">
                    <button :disabled="loading.forget" type="submit" class="button-primary button-block">
                        <svg x-cloak x-show="loading.forget" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
</svg>
                        Atur ulang kata sandi
                    </button>
                </div>
            </form>
            <form
                x-show="currentPage === 'change_password'"
                @submit.prevent="changePassword"
                class="space-y-4 sm:space-y-6"
            >
                <div>
                    <h2 class="text-center text-2xl text-nord4">
                        Ganti kata sandi
                    </h2>
                </div>
                <div x-cloak x-show="Object.keys(errors.changePassword).length" class="rounded-md bg-red-50 p-4 text-sm text-red-700">
    <ul role="list" class="list-disc pl-5 space-y-1">
        <template x-for="error in errors.changePassword">
            <li x-text="error[0]"></li>
        </template>
    </ul>
</div>
                <div x-cloak class="rounded-md bg-emerald-50 p-4" x-show="success.changePassword">
    <div class="flex">
        <div class="shrink-0">
            <svg class="h-5 w-5 text-emerald-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
        </div>
        <div class="ml-3">
            <p class="text-sm font-medium text-emerald-800">
                Kata sandi diubah.
            </p>
        </div>
    </div>
</div>
                <div x-show="! success.changePassword" class="text-group">
                    <div
            :class="{ 'bg-red-100': errors.changePassword.old_password }"
        class="rounded-b-none"
>
    <label
                    :class="{ 'text-red-800': errors.changePassword.old_password }"
                for="change_password_old_password"
    >Password lama</label>
    <input
        x-model="old_password"
                    :class="{ 'bg-red-100': errors.changePassword.old_password }"
                type="password"
        id="change_password_old_password"
                    required
                            autocomplete="section-change-password current-password"
            >
</div>
                    <div
            :class="{ 'bg-red-100': errors.changePassword.password }"
        class="rounded-b-none"
>
    <label
                    :class="{ 'text-red-800': errors.changePassword.password }"
                for="change_password_password"
    >Kata sandi baru</label>
    <input
        x-model="password"
                    :class="{ 'bg-red-100': errors.changePassword.password }"
                type="password"
        id="change_password_password"
                    required
                            autocomplete="section-change-password new-password"
            >
</div>
                    <div
            :class="{ 'bg-red-100': errors.changePassword.password_confirmation }"
        class="rounded-t-none"
>
    <label
                    :class="{ 'text-red-800': errors.changePassword.password_confirmation }"
                for="change_password_password_confirmation"
    >Konfirmasi sandi</label>
    <input
        x-model="password_confirmation"
                    :class="{ 'bg-red-100': errors.changePassword.password_confirmation }"
                type="password"
        id="change_password_password_confirmation"
                    required
                            autocomplete="section-change-password new-password"
            >
</div>
                </div>
                <div x-show="! success.changePassword" class="block w-full rounded-md shadow-sm">
                    <button :disabled="loading.changePassword" type="submit" class="button-primary button-block">
                        <svg x-cloak x-show="loading.changePassword" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
</svg>
                        Ganti kata sandi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
            <div class="relative">
    <div class="fixed z-max w-full bg-gradient-to-b from-darkest">
        <div class="sm:container flex justify-between items-center mx-auto px-4">
            <div class="lg:w-0 lg:flex-1">
                <a class="text-4xl leading-normal" href="https://missav.ai/id">
                    <span style="visibility: hidden;" class="font-serif"><span class="text-zinc-50">MISS</span><span class="text-primary">AV</span></span>
                </a>
            </div>
            <div class="relative xl:hidden flex items-center space-x-4">
                <a
    @click.prevent="toggleSearch"
    href="#"
    class="rounded-md text-nord6 hover:text-primary focus:outline-none"
    alt="Mencari"
>
    <span class="sr-only">Mencari</span>
    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
    </svg>
</a>
                <div class="relative z-max">
    <a
        @click.prevent="showLocaleSwitcher = ! showLocaleSwitcher"
        href="#"
    >
        <img width="28" height="28" src="https://missav.ai/img/flags/indonesia.png" alt="Bahasa Indonesia">
    </a>
    <div
        x-cloak
        x-show="showLocaleSwitcher"
        @click.outside="showLocaleSwitcher = false"
        class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg max-h-[calc(100vh-55px)] overflow-y-scroll"
    >
        <div class="rounded-md text-nord4 bg-gray-900 shadow-xs">
                            <div class="py-1">
                    <a
                                                    @click.prevent="redirectToBaseLocalizedUrl"
                            href="/"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/hong-kong.png" alt="繁體中文">
                        繁體中文
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('cn')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/china.png" alt="简体中文">
                        简体中文
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('en')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/united-kingdom.png" alt="English">
                        English
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('ja')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/japan.png" alt="日本語">
                        日本語
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('ko')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/south-korea.png" alt="한국의">
                        한국의
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('ms')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/malaysia.png" alt="Melayu">
                        Melayu
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('th')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/thailand.png" alt="ไทย">
                        ไทย
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('de')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/germany.png" alt="Deutsch">
                        Deutsch
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('fr')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/france.png" alt="Français">
                        Français
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('vi')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/vietnam.png" alt="Tiếng Việt">
                        Tiếng Việt
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('id')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/indonesia.png" alt="Bahasa Indonesia">
                        Bahasa Indonesia
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('fil')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/philippines.png" alt="Filipino">
                        Filipino
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('pt')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/brazil.png" alt="Português">
                        Português
                    </a>
                </div>
                    </div>
    </div>
</div>
                <div class="relative ml-4">
                    <a
                        @click.prevent="showDropdown = showDropdown === 'mobile' ? null : 'mobile'"
                        href="#"
                        class="rounded-md text-nord6 hover:text-primary focus:outline-none"
                    >
                        <span class="sr-only">Menu</span>
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </a>
                    <div
                        x-cloak
                        x-show="showDropdown === 'mobile'"
                        @click.outside="showDropdown = null"
                        class="z-max origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg max-h-[calc(100vh-55px)] overflow-y-scroll"
                    >
                        <div class="rounded-md text-nord0 bg-nord5 shadow-xs">
                            <div class="py-1">
                                                                    <a
                                        href="https://missav.ai/id/vip"
                                        class="block px-4 py-2 text-sm leading-5 text-gray-700 hover:bg-nord4"
                                    >
                                        Tingkatkan VIP
                                    </a>
                                                                                                    <a
                                        href="https://missav.ai/id/english-subtitle"
                                        class="block px-4 py-2 text-sm leading-5 text-gray-700 hover:bg-nord4"
                                    >
                                        Subtitle bahasa inggris
                                    </a>
                                                                <a
    @click.prevent="showCollapse = showCollapse === 'jav' ? null : 'jav'"
    href="#"
    class="flex items-center justify-between px-4 py-2 text-sm leading-5 text-nord0 hover:bg-nord4"
>
    <span>Tonton JAV</span>
    <svg x-show="showCollapse !== 'jav'" class="ml-2 h-5 w-5 text-nord0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
    </svg>
    <svg x-show="showCollapse === 'jav'" class="ml-2 h-5 w-5 text-nord0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
    </svg>
</a>
<span
    x-cloak
    x-show="showCollapse === 'jav'"
>
            <a
            href="https://missav.ai/dm514/id/new"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Recent update
        </a>
            <a
            href="https://missav.ai/dm588/id/release"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Keluaran terbaru
        </a>
            <a
            href="https://missav.ai/dm621/id/uncensored-leak"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Kebocoran tanpa sensor
        </a>
            <a
            href="https://missav.ai/id/actresses"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Daftar aktris
        </a>
            <a
            href="https://missav.ai/id/actresses/ranking"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Peringkat aktris MAY 2025
        </a>
            <a
            href="https://missav.ai/id/genres"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Genre
        </a>
            <a
            href="https://missav.ai/id/makers"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Pembuat
        </a>
            <a
            href="https://missav.ai/id/genres/VR"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            VR
        </a>
            <a
            href="https://missav.ai/dm291/id/today-hot"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Paling banyak dilihat hari ini
        </a>
            <a
            href="https://missav.ai/dm169/id/weekly-hot"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Paling banyak dilihat per minggu
        </a>
            <a
            href="https://missav.ai/dm257/id/monthly-hot"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Paling banyak dilihat berdasarkan bulan
        </a>
    </span>
                                <a
    @click.prevent="showCollapse = showCollapse === 'amateur' ? null : 'amateur'"
    href="#"
    class="flex items-center justify-between px-4 py-2 text-sm leading-5 text-nord0 hover:bg-nord4"
>
    <span>Amatir</span>
    <svg x-show="showCollapse !== 'amateur'" class="ml-2 h-5 w-5 text-nord0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
    </svg>
    <svg x-show="showCollapse === 'amateur'" class="ml-2 h-5 w-5 text-nord0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
    </svg>
</a>
<span
    x-cloak
    x-show="showCollapse === 'amateur'"
>
            <a
            href="https://missav.ai/dm23/id/siro"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            SIRO
        </a>
            <a
            href="https://missav.ai/dm20/id/luxu"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            LUXU
        </a>
            <a
            href="https://missav.ai/dm17/id/gana"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            GANA
        </a>
            <a
            href="https://missav.ai/dm862/id/maan"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            MAAN
        </a>
            <a
            href="https://missav.ai/dm23/id/scute"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            S-CUTE
        </a>
            <a
            href="https://missav.ai/dm19/id/ara"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            ARA
        </a>
    </span>
                                <a
    @click.prevent="showCollapse = showCollapse === 'uncensored' ? null : 'uncensored'"
    href="#"
    class="flex items-center justify-between px-4 py-2 text-sm leading-5 text-nord0 hover:bg-nord4"
>
    <span>Tanpa sensor</span>
    <svg x-show="showCollapse !== 'uncensored'" class="ml-2 h-5 w-5 text-nord0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
    </svg>
    <svg x-show="showCollapse === 'uncensored'" class="ml-2 h-5 w-5 text-nord0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
    </svg>
</a>
<span
    x-cloak
    x-show="showCollapse === 'uncensored'"
>
            <a
            href="https://missav.ai/dm621/id/uncensored-leak"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Kebocoran tanpa sensor
        </a>
            <a
            href="https://missav.ai/dm99/id/fc2"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            FC2
        </a>
            <a
            href="https://missav.ai/dm319995/id/heyzo"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            HEYZO
        </a>
            <a
            href="https://missav.ai/dm29/id/tokyohot"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Tokyo panas
        </a>
            <a
            href="https://missav.ai/dm695579/id/1pondo"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            1pondo
        </a>
            <a
            href="https://missav.ai/dm1271239/id/caribbeancom"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Caribbeancom
        </a>
            <a
            href="https://missav.ai/dm14081/id/caribbeancompr"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Caribbeancompr
        </a>
            <a
            href="https://missav.ai/dm1117248/id/10musume"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            10musume
        </a>
            <a
            href="https://missav.ai/dm370414/id/pacopacomama"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            pacopacomama
        </a>
            <a
            href="https://missav.ai/dm135/id/gachinco"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Gachinco
        </a>
            <a
            href="https://missav.ai/dm29/id/xxxav"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            XXX-AV
        </a>
            <a
            href="https://missav.ai/dm24/id/marriedslash"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Slash Menikah
        </a>
            <a
            href="https://missav.ai/dm19/id/naughty4610"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Nakal 4610
        </a>
            <a
            href="https://missav.ai/dm22/id/naughty0930"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Nakal 0930
        </a>
    </span>
                                <a
    @click.prevent="showCollapse = showCollapse === 'madou' ? null : 'madou'"
    href="#"
    class="flex items-center justify-between px-4 py-2 text-sm leading-5 text-nord0 hover:bg-nord4"
>
    <span>Asia AV</span>
    <svg x-show="showCollapse !== 'madou'" class="ml-2 h-5 w-5 text-nord0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
    </svg>
    <svg x-show="showCollapse === 'madou'" class="ml-2 h-5 w-5 text-nord0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
    </svg>
</a>
<span
    x-cloak
    x-show="showCollapse === 'madou'"
>
            <a
            href="https://missav.ai/dm34/id/madou"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Madou
        </a>
            <a
            href="https://missav.ai/dm17/id/twav"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            TWAV
        </a>
            <a
            href="https://missav.ai/dm15/id/furuke"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Furuke
        </a>
            <a
            href="https://missav.ai/id/klive"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Siaran Langsung Korea
        </a>
            <a
            href="https://missav.ai/id/clive"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Siaran Langsung Cina
        </a>
    </span>
                                <a
    @click.prevent="showCollapse = showCollapse === 'saved' ? null : 'saved'"
    href="#"
    class="flex items-center justify-between px-4 py-2 text-sm leading-5 text-nord0 hover:bg-nord4"
>
    <span>Koleksi saya</span>
    <svg x-show="showCollapse !== 'saved'" class="ml-2 h-5 w-5 text-nord0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
    </svg>
    <svg x-show="showCollapse === 'saved'" class="ml-2 h-5 w-5 text-nord0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
    </svg>
</a>
<span
    x-cloak
    x-show="showCollapse === 'saved'"
>
            <a
            href="https://missav.ai/id/vip"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Tingkatkan VIP
        </a>
            <a
            href="https://missav.ai/id/saved"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Koleksi video saya
        </a>
            <a
            href="https://missav.ai/id/playlists"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Daftar putar saya
        </a>
            <a
            href="https://missav.ai/id/saved/actresses"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Koleksi aktris saya
        </a>
            <a
            href="https://missav.ai/id/history"
                                    class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            Tonton sejarah
        </a>
    </span>
                                                                                                    <a
    @click.prevent="showCollapse = showCollapse === 'partners' ? null : 'partners'"
    href="#"
    class="flex items-center justify-between px-4 py-2 text-sm leading-5 text-nord0 hover:bg-nord4"
>
    <span>Lebih banyak situs</span>
    <svg x-show="showCollapse !== 'partners'" class="ml-2 h-5 w-5 text-nord0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
    </svg>
    <svg x-show="showCollapse === 'partners'" class="ml-2 h-5 w-5 text-nord0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
    </svg>
</a>
<span
    x-cloak
    x-show="showCollapse === 'partners'"
>
            <a
            href="https://bit.ly/3DW32St"
                            rel="sponsored nofollow noopener noreferrer"
                                        target="_blank"
                        class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            成人污漫禁漫大全
        </a>
            <a
            href="https://bit.ly/3GMy00T"
                            rel="sponsored nofollow noopener noreferrer"
                                        target="_blank"
                        class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            哔咔漫画破解版
        </a>
            <a
            href="https://bit.ly/44JTIMZ"
                            rel="sponsored nofollow noopener noreferrer"
                                        target="_blank"
                        class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            全球最大色情平台
        </a>
            <a
            href="https://bit.ly/4dBFnVh"
                            rel="sponsored nofollow noopener noreferrer"
                                        target="_blank"
                        class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            91大神原创社区
        </a>
            <a
            href="https://bit.ly/4fvb16M"
                            rel="sponsored nofollow noopener noreferrer"
                                        target="_blank"
                        class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            最全重口稀缺资源
        </a>
            <a
            href="https://bit.ly/3H3haet"
                            rel="sponsored nofollow noopener noreferrer"
                                        target="_blank"
                        class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            P站中文免费版
        </a>
            <a
            href="https://bit.ly/3Z6rUPc"
                            rel="sponsored nofollow noopener noreferrer"
                                        target="_blank"
                        class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            糖心vlog破解版
        </a>
            <a
            href="https://bit.ly/43wW7Zf"
                            rel="sponsored nofollow noopener noreferrer"
                                        target="_blank"
                        class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            全球顶级国禁视频
        </a>
            <a
            href="https://bit.ly/43key39"
                            rel="sponsored nofollow noopener noreferrer"
                                        target="_blank"
                        class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            AI脱衣换衣
        </a>
            <a
            href="https://bit.ly/4fyWiau"
                            rel="sponsored nofollow noopener noreferrer"
                                        target="_blank"
                        class="block px-4 py-2 text-sm leading-5 text-nord5 bg-nord3 hover:bg-nord2"
        >
            TikTok成人版
        </a>
    </span>
                                                                                                                                                                                                                                        <a
                                    href="https://myavlive.com/?userId=050103608cf9b4d04684e5804b8637ff881d466e3ceaf77c1cc78be33cb1f3fe"
                                    rel="nofollow"
                                    target="_blank"
                                    class="block px-4 py-2 text-sm leading-5 text-gray-700 hover:bg-nord4"
                                >
                                    Seks Kamera Langsung
                                </a>
                                                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <nav class="hidden xl:flex space-x-4 items-center">
                                <a
                    href="https://myavlive.com/?userId=050103608cf9b4d04684e5804b8637ff881d466e3ceaf77c1cc78be33cb1f3fe"
                    rel="nofollow"
                    target="_blank"
                    class="text-base leading-6 font-medium text-nord6 hover:text-primary focus:outline-none"
                >
                    Seks Kamera Langsung
                </a>
                                                                <div class="relative">
    <a
        @click.prevent="showDropdown = 'jav'"
        href="#"
        class="text-nord6 group inline-flex items-center text-base leading-6 font-medium hover:text-primary focus:outline-none"
    >
        <span>Tonton JAV</span>
        <svg x-show="showDropdown !== 'jav'" class="ml-1 h-5 w-5 text-nord6 group-hover:text-primary" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
        <svg x-cloak x-show="showDropdown === 'jav'" class="ml-1 h-5 w-5 text-nord6 group-hover:text-primary" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
        </svg>
    </a>
    <div
        x-cloak
        x-show="showDropdown === 'jav'"
        @click.outside="showDropdown = null"
        class="right-0 z-max origin-top-right absolute mt-2 w-56 rounded-md shadow-lg"
    >
        <div class="rounded-md text-nord0 bg-nord5 shadow-xs">
            
            <div class="py-1">
                                    <a
                        href="https://missav.ai/dm514/id/new"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Recent update
                    </a>
                                    <a
                        href="https://missav.ai/dm588/id/release"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Keluaran terbaru
                    </a>
                                    <a
                        href="https://missav.ai/dm621/id/uncensored-leak"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Kebocoran tanpa sensor
                    </a>
                                    <a
                        href="https://missav.ai/id/actresses"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Daftar aktris
                    </a>
                                    <a
                        href="https://missav.ai/id/actresses/ranking"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Peringkat aktris MAY 2025
                    </a>
                                    <a
                        href="https://missav.ai/id/genres"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Genre
                    </a>
                                    <a
                        href="https://missav.ai/id/makers"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Pembuat
                    </a>
                                    <a
                        href="https://missav.ai/id/genres/VR"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        VR
                    </a>
                                    <a
                        href="https://missav.ai/dm291/id/today-hot"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Paling banyak dilihat hari ini
                    </a>
                                    <a
                        href="https://missav.ai/dm169/id/weekly-hot"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Paling banyak dilihat per minggu
                    </a>
                                    <a
                        href="https://missav.ai/dm257/id/monthly-hot"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Paling banyak dilihat berdasarkan bulan
                    </a>
                                    <a
                        href="https://missav.ai/id/english-subtitle"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Subtitle bahasa inggris
                    </a>
                            </div>
        </div>
    </div>
</div>
                <div class="relative">
    <a
        @click.prevent="showDropdown = 'amateur'"
        href="#"
        class="text-nord6 group inline-flex items-center text-base leading-6 font-medium hover:text-primary focus:outline-none"
    >
        <span>Amatir</span>
        <svg x-show="showDropdown !== 'amateur'" class="ml-1 h-5 w-5 text-nord6 group-hover:text-primary" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
        <svg x-cloak x-show="showDropdown === 'amateur'" class="ml-1 h-5 w-5 text-nord6 group-hover:text-primary" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
        </svg>
    </a>
    <div
        x-cloak
        x-show="showDropdown === 'amateur'"
        @click.outside="showDropdown = null"
        class="right-0 z-max origin-top-right absolute mt-2 w-56 rounded-md shadow-lg"
    >
        <div class="rounded-md text-nord0 bg-nord5 shadow-xs">
            
            <div class="py-1">
                                    <a
                        href="https://missav.ai/dm23/id/siro"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        SIRO
                    </a>
                                    <a
                        href="https://missav.ai/dm20/id/luxu"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        LUXU
                    </a>
                                    <a
                        href="https://missav.ai/dm17/id/gana"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        GANA
                    </a>
                                    <a
                        href="https://missav.ai/dm862/id/maan"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        MAAN
                    </a>
                                    <a
                        href="https://missav.ai/dm23/id/scute"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        S-CUTE
                    </a>
                                    <a
                        href="https://missav.ai/dm19/id/ara"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        ARA
                    </a>
                            </div>
        </div>
    </div>
</div>
                <div class="relative">
    <a
        @click.prevent="showDropdown = 'uncensored'"
        href="#"
        class="text-nord6 group inline-flex items-center text-base leading-6 font-medium hover:text-primary focus:outline-none"
    >
        <span>Tanpa sensor</span>
        <svg x-show="showDropdown !== 'uncensored'" class="ml-1 h-5 w-5 text-nord6 group-hover:text-primary" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
        <svg x-cloak x-show="showDropdown === 'uncensored'" class="ml-1 h-5 w-5 text-nord6 group-hover:text-primary" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
        </svg>
    </a>
    <div
        x-cloak
        x-show="showDropdown === 'uncensored'"
        @click.outside="showDropdown = null"
        class="right-0 z-max origin-top-right absolute mt-2 w-56 rounded-md shadow-lg"
    >
        <div class="rounded-md text-nord0 bg-nord5 shadow-xs">
            
            <div class="py-1">
                                    <a
                        href="https://missav.ai/dm621/id/uncensored-leak"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Kebocoran tanpa sensor
                    </a>
                                    <a
                        href="https://missav.ai/dm99/id/fc2"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        FC2
                    </a>
                                    <a
                        href="https://missav.ai/dm319995/id/heyzo"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        HEYZO
                    </a>
                                    <a
                        href="https://missav.ai/dm29/id/tokyohot"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Tokyo panas
                    </a>
                                    <a
                        href="https://missav.ai/dm695579/id/1pondo"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        1pondo
                    </a>
                                    <a
                        href="https://missav.ai/dm1271239/id/caribbeancom"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Caribbeancom
                    </a>
                                    <a
                        href="https://missav.ai/dm14081/id/caribbeancompr"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Caribbeancompr
                    </a>
                                    <a
                        href="https://missav.ai/dm1117248/id/10musume"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        10musume
                    </a>
                                    <a
                        href="https://missav.ai/dm370414/id/pacopacomama"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        pacopacomama
                    </a>
                                    <a
                        href="https://missav.ai/dm135/id/gachinco"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Gachinco
                    </a>
                                    <a
                        href="https://missav.ai/dm29/id/xxxav"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        XXX-AV
                    </a>
                                    <a
                        href="https://missav.ai/dm24/id/marriedslash"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Slash Menikah
                    </a>
                                    <a
                        href="https://missav.ai/dm19/id/naughty4610"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Nakal 4610
                    </a>
                                    <a
                        href="https://missav.ai/dm22/id/naughty0930"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Nakal 0930
                    </a>
                            </div>
        </div>
    </div>
</div>
                <div class="relative">
    <a
        @click.prevent="showDropdown = 'madou'"
        href="#"
        class="text-nord6 group inline-flex items-center text-base leading-6 font-medium hover:text-primary focus:outline-none"
    >
        <span>Asia AV</span>
        <svg x-show="showDropdown !== 'madou'" class="ml-1 h-5 w-5 text-nord6 group-hover:text-primary" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
        <svg x-cloak x-show="showDropdown === 'madou'" class="ml-1 h-5 w-5 text-nord6 group-hover:text-primary" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
        </svg>
    </a>
    <div
        x-cloak
        x-show="showDropdown === 'madou'"
        @click.outside="showDropdown = null"
        class="right-0 z-max origin-top-right absolute mt-2 w-56 rounded-md shadow-lg"
    >
        <div class="rounded-md text-nord0 bg-nord5 shadow-xs">
            
            <div class="py-1">
                                    <a
                        href="https://missav.ai/dm34/id/madou"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Madou
                    </a>
                                    <a
                        href="https://missav.ai/dm17/id/twav"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        TWAV
                    </a>
                                    <a
                        href="https://missav.ai/dm15/id/furuke"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Furuke
                    </a>
                                    <a
                        href="https://missav.ai/id/klive"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Siaran Langsung Korea
                    </a>
                                    <a
                        href="https://missav.ai/id/clive"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Siaran Langsung Cina
                    </a>
                            </div>
        </div>
    </div>
</div>
                <div class="relative">
    <a
        @click.prevent="showDropdown = 'saved'"
        href="#"
        class="text-nord6 group inline-flex items-center text-base leading-6 font-medium hover:text-primary focus:outline-none"
    >
        <span>Koleksi saya</span>
        <svg x-show="showDropdown !== 'saved'" class="ml-1 h-5 w-5 text-nord6 group-hover:text-primary" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
        <svg x-cloak x-show="showDropdown === 'saved'" class="ml-1 h-5 w-5 text-nord6 group-hover:text-primary" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
        </svg>
    </a>
    <div
        x-cloak
        x-show="showDropdown === 'saved'"
        @click.outside="showDropdown = null"
        class="right-0 z-max origin-top-right absolute mt-2 w-56 rounded-md shadow-lg"
    >
        <div class="rounded-md text-nord0 bg-nord5 shadow-xs">
            
            <div class="py-1">
                                    <a
                        href="https://missav.ai/id/vip"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Tingkatkan VIP
                    </a>
                                    <a
                        href="https://missav.ai/id/saved"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Koleksi video saya
                    </a>
                                    <a
                        href="https://missav.ai/id/playlists"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Daftar putar saya
                    </a>
                                    <a
                        href="https://missav.ai/id/saved/actresses"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Koleksi aktris saya
                    </a>
                                    <a
                        href="https://missav.ai/id/history"
                                                                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        Tonton sejarah
                    </a>
                            </div>
        </div>
    </div>
</div>
                                    <div class="relative">
    <a
        @click.prevent="showDropdown = 'partners'"
        href="#"
        class="text-nord6 group inline-flex items-center text-base leading-6 font-medium hover:text-primary focus:outline-none"
    >
        <span>Lebih banyak situs</span>
        <svg x-show="showDropdown !== 'partners'" class="ml-1 h-5 w-5 text-nord6 group-hover:text-primary" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
        <svg x-cloak x-show="showDropdown === 'partners'" class="ml-1 h-5 w-5 text-nord6 group-hover:text-primary" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
        </svg>
    </a>
    <div
        x-cloak
        x-show="showDropdown === 'partners'"
        @click.outside="showDropdown = null"
        class="right-0 z-max origin-top-right absolute mt-2 w-56 rounded-md shadow-lg"
    >
        <div class="rounded-md text-nord0 bg-nord5 shadow-xs">
            
            <div class="py-1">
                                    <a
                        href="https://bit.ly/3DW32St"
                                                                            rel="sponsored nofollow noopener noreferrer"
                                                                            target="_blank"
                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        成人污漫禁漫大全
                    </a>
                                    <a
                        href="https://bit.ly/3Z6rUPc"
                                                                            rel="sponsored nofollow noopener noreferrer"
                                                                            target="_blank"
                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        糖心vlog破解版
                    </a>
                                    <a
                        href="https://bit.ly/4fyWiau"
                                                                            rel="sponsored nofollow noopener noreferrer"
                                                                            target="_blank"
                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        TikTok成人版
                    </a>
                                    <a
                        href="https://bit.ly/3GMy00T"
                                                                            rel="sponsored nofollow noopener noreferrer"
                                                                            target="_blank"
                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        哔咔漫画破解版
                    </a>
                                    <a
                        href="https://bit.ly/44JTIMZ"
                                                                            rel="sponsored nofollow noopener noreferrer"
                                                                            target="_blank"
                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        全球最大色情平台
                    </a>
                                    <a
                        href="https://bit.ly/4dBFnVh"
                                                                            rel="sponsored nofollow noopener noreferrer"
                                                                            target="_blank"
                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        91大神原创社区
                    </a>
                                    <a
                        href="https://bit.ly/4fvb16M"
                                                                            rel="sponsored nofollow noopener noreferrer"
                                                                            target="_blank"
                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        最全重口稀缺资源
                    </a>
                                    <a
                        href="https://bit.ly/43wW7Zf"
                                                                            rel="sponsored nofollow noopener noreferrer"
                                                                            target="_blank"
                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        全球顶级国禁视频
                    </a>
                                    <a
                        href="https://bit.ly/43key39"
                                                                            rel="sponsored nofollow noopener noreferrer"
                                                                            target="_blank"
                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        AI脱衣换衣
                    </a>
                                    <a
                        href="https://bit.ly/3H3haet"
                                                                            rel="sponsored nofollow noopener noreferrer"
                                                                            target="_blank"
                                                class="text-nord0 block px-4 py-2 text-sm leading-5 hover:bg-nord4"
                    >
                        P站中文免费版
                    </a>
                            </div>
        </div>
    </div>
</div>
                                <a
    @click.prevent="toggleSearch"
    href="#"
    class="rounded-md text-nord6 hover:text-primary focus:outline-none"
    alt="Mencari"
>
    <span class="sr-only">Mencari</span>
    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
    </svg>
</a>
                <div class="relative z-max">
    <a
        @click.prevent="showLocaleSwitcher = ! showLocaleSwitcher"
        href="#"
    >
        <img width="28" height="28" src="https://missav.ai/img/flags/indonesia.png" alt="Bahasa Indonesia">
    </a>
    <div
        x-cloak
        x-show="showLocaleSwitcher"
        @click.outside="showLocaleSwitcher = false"
        class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg max-h-[calc(100vh-55px)] overflow-y-scroll"
    >
        <div class="rounded-md text-nord4 bg-gray-900 shadow-xs">
                            <div class="py-1">
                    <a
                                                    @click.prevent="redirectToBaseLocalizedUrl"
                            href="/"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/hong-kong.png" alt="繁體中文">
                        繁體中文
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('cn')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/china.png" alt="简体中文">
                        简体中文
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('en')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/united-kingdom.png" alt="English">
                        English
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('ja')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/japan.png" alt="日本語">
                        日本語
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('ko')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/south-korea.png" alt="한국의">
                        한국의
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('ms')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/malaysia.png" alt="Melayu">
                        Melayu
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('th')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/thailand.png" alt="ไทย">
                        ไทย
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('de')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/germany.png" alt="Deutsch">
                        Deutsch
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('fr')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/france.png" alt="Français">
                        Français
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('vi')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/vietnam.png" alt="Tiếng Việt">
                        Tiếng Việt
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('id')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/indonesia.png" alt="Bahasa Indonesia">
                        Bahasa Indonesia
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('fil')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/philippines.png" alt="Filipino">
                        Filipino
                    </a>
                </div>
                            <div class="py-1">
                    <a
                                                    :href="localizedUrl('pt')"
                                                class="block w-full text-left px-4 py-1.5 xs:py-2 text-sm leading-5 text-nord6 hover:bg-gray-700"
                    >
                        <img width="24" height="24" class="inline mr-2" src="https://missav.ai/img/flags/brazil.png" alt="Português">
                        Português
                    </a>
                </div>
                    </div>
    </div>
</div>
            </nav>
        </div>
        <template x-if="showSearch">
            <form @submit.prevent="search($refs.search.value)">
                <div class="sm:container mx-auto px-4">
                    <div :class="{ 'pb-2': searchHistory.length, 'pb-4': ! searchHistory.length }" class="flex justify-between items-center">
                        <div class="flex rounded-md shadow-sm w-full mx-auto">
                            <div class="flex items-stretch grow">
                                <input
                                    x-ref="search"
                                    :placeholder="currentSearchPlaceholderText"
                                    type="text"
                                    value=""
                                    class="bg-nord1 appearance-none border-2 border-nord9 rounded-none rounded-l w-full py-2 px-4 text-nord9 leading-tight focus:outline-none focus:bg-nord0 focus:ring-0 focus:border-nord9"
                                    maxlength="50"
                                >
                            </div>
                            <button class="-ml-px relative inline-flex items-center px-4 py-2 border-2 border-nord9 text-sm whitespace-nowrap leading-5 font-medium rounded-r-md text-norddark bg-nord9 hover:bg-opacity-90 focus:outline-none focus:border-nord8 active:bg-opacity-80 transition ease-in-out duration-150">
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                <span class="ml-2">Mencari</span>
                            </button>
                        </div>
                    </div>
                    <div x-cloak x-show="searchHistory.length" class="pb-2">
                        <div x-cloak class="flex items-start justify-between">
    <div class="line-clamp-1">
        <template x-for="(keyword, index) in searchHistory">
            <span>
                <a
                    x-text="decodeURIComponent(keyword)"
                    @click.prevent="search(decodeURIComponent(keyword))"
                    href="#"
                    class="text-nord13"
                ></a><span x-show="index < searchHistory.length - 1" class="text-nord4">, </span>
            </span>
        </template>
    </div>
    <a @click.prevent="clearSearchHistory" href="#" alt="Jernih">
        <svg xmlns="http://www.w3.org/2000/svg" class="inline h-5 w-5 text-secondary hover:text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
    </a>
</div>
                    </div>
                </div>
            </form>
        </template>
    </div>
</div>
            <!--sse-->
            <div
                :class="{
                    'content-without-search': ! showSearch,
                    'content-with-search': showSearch,
                }"
                class="
                    sm:container mx-auto px-4 
                    
                    content-without-search
                    pb-12
                "
            >
                                <script type="text/javascript">
        window.placeHolderRelatedItems = [{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{}]
    </script>
    <div
        x-data="{
            saved: false,
            loading: false,
            showPanel: null,
            shareTitle: 'SDDE-740+%5BVersi+bonus+SP%5D+Perhotelan+dengan+%22seragam%2C+pakaian+dalam%2C+dan+ketelanjangan%22+mengangkangi+penerbangan+vagina+2024+edisi+pelatihan+skala+besar+CA+baru+dengan+total+11+orang+%2B+1+instruktur+khusus+6+bagian+pelajaran+kabin+vagina+grup+43+menit+sudut+bonus+ditambahkan%21+Total+durasi%3A+190+menit%2C+6%2B1+bagian+-+Mirai+Sunohara',
            dvdId: 'sdde-740',
            relatedItemsQuantity: 29,
            currentTab: 'video_details',
            recommendItems: window.placeHolderRelatedItems,
            toggleSave() {
                this.requireLogin(() => {
                    if (this.loading) {
                        return
                    }

                    this.loading = true

                    const method = this.saved ? 'delete' : 'post'

                    window.axios[method]('https://missav.ai/api/items/w9wjkljj/save').then(() => {
                        this.loading = false
                    })

                    this.saved = ! this.saved
                })
            },
            togglePanel(type) {
                this.showPanel = this.showPanel !== type ? type : null
            },
            togglePlaylist() {
                this.requireLogin(() => {
                    this.togglePanel('playlist')
                })
            },
        }"
        x-init="$nextTick(() => {
            axios.get('https://missav.ai/api/items/w9wjkljj/view').then(response => {
                user = response.data.user
                saved = response.data.saved
            })

            const recombeeRecommendItemsToItem = (scenario, count) => {
                return new recombee.RecommendItemsToItem(dvdId, window.user_uuid, count, {
                    scenario,
                    returnProperties: true,
                    includedProperties: [
                        locale === 'ja' ? 'title' : `title_${locale}`,
                        'duration',
                        'has_chinese_subtitle',
                        'has_english_subtitle',
                        'is_uncensored_leak',
                        'dm',
                    ],
                })
            }

            const recombeeRequests = isDesktop() ? [
                recombeeRecommendItemsToItem('desktop-watch-next-side', 13),
                recombeeRecommendItemsToItem('desktop-watch-next-bottom', isThreeColumns() ? 12 : 16),
            ] : [
                recombeeRecommendItemsToItem('mobile-watch-next', isThreeColumns() ? 12 : 16),
            ];

            const recombeeRequestScenarios = isDesktop() ? [
                'desktop-watch-next-side',
                'desktop-watch-next-bottom',
            ] : [
                'mobile-watch-next',
            ]

            window.recombeeClient.send(
                new recombee.Batch(recombeeRequests, { distinctRecomms: true }),
            ).then(responses => {
                recommendItems = responses.reduce((recomms, response, index) =>
                    recomms.concat(response.json.recomms.map(recomm => {
                        let item = recomm.values

                        item.dvd_id = recomm.id
                        item.recommend_id = response.json.recommId
                        item.scenario = recombeeRequestScenarios[index]

                        return item
                    })), []
                )

                $nextTick(() => {
                    initLozad()
                })
            })
        })"
        class="flex"
    >
        <div class="hidden lg:flex h-full ml-6 order-last" style="max-width: 300px; min-width: 300px;">
            <div>
                <div class="space-y-6 mb-6">
                            <div
    class="
                    hidden lg:block
            "
>
    <div class="mx-auto" style="width: 300px; height: 250px;">
    <iframe width="300px" height="250px" style="display:block" marginWidth="0" marginHeight="0" frameBorder="no" src="https://go.rmhfrtnd.com/smartpop/4e490087485504e3973c7dd50ce6472bb710085963e281aff05461b839677dfb?userId=050103608cf9b4d04684e5804b8637ff881d466e3ceaf77c1cc78be33cb1f3fe"></iframe>
</div>
</div>
                                <div
    class="
                    hidden lg:block
            "
>
    <div class="mx-auto" style="width: 300px; height: 250px;">
    <iframe referrerpolicy="no-referrer" data-link="https://r.trackwilltrk.com/s1/bc21a4fe-5e9b-4936-a844-b3e5f04c4cdc?externalId={impressionId}&cv1={impressionId}&cv2={userId}&cv3={device}&cv4={creativeId}&cv5={campaignId}&cv6={language}&cv7=%SLAVA_KPSS%&cv8={browser}&cv9={siteId}&cv10={creativeName}" src="javascript:window.location.replace(this.frameElement.dataset.link)" width="300" height="250" scrolling="no" marginwidth="0" marginheight="0" frameborder="0"></iframe>
</div>
</div>
                        </div>
                    <template
    x-for="item in recommendItems.slice(0, 13).map(item => {
        item = generateFullItemTitle(item)

        item.scenario = item.scenario
            ? item.scenario
            : isDesktop() ? 'internal-desktop-watch-next-side' : 'internal-mobile-watch-next'

        return item
    })"
>
    <div class="flex mb-6">
        <div class="flex-none mr-4" style="width: 165px;">
            <div
                @mouseenter="item.dvd_id ? setPreview(`side-${item.dvd_id}`) : null"
                @mouseleave="item.dvd_id ? setPreview() : null"
                @click="item.dvd_id ? clickPreview(`side-${item.dvd_id}`) : null"
                class="thumbnail group"
            >
                <div class="relative aspect-w-16 aspect-h-9 rounded overflow-hidden shadow-lg">
    <a :href="item.dvd_id ? itemUrl(item) : 'javascript:;'" :alt="item.dvd_id">
        <video
            x-cloak
            :data-src="item.dvd_id ? cdnUrl(`/${item.dvd_id}/preview.mp4`) : 'javascript:;'"
            :id="`preview-side-${item.dvd_id}`"
            :class="{ hidden: showPreview !== `side-${item.dvd_id}` && ! holdPreviews.includes(`side-${item.dvd_id}`) }"
            class="preview hidden"
            loop
            muted
            playsinline
        ></video>
        <img
            x-cloak
            :data-src="item.dvd_id ? cdnUrl(`/${item.dvd_id}/cover-t.jpg`) : 'javascript:;'"
            :class="{ hidden: showPreview === `side-${item.dvd_id}` || holdPreviews.includes(`side-${item.dvd_id}`) }"
            :alt="item.title"
            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mN09omrBwADNQFuUCqPAwAAAABJRU5ErkJggg=="
            class="lozad w-full"
        >
    </a>
    <a x-show="item.dvd_id && isChinese && item.has_chinese_subtitle" :href="itemUrl(item)" :alt="item.dvd_id">
        <span class="absolute bottom-1 left-1 rounded-lg px-2 py-1 text-xs text-nord5 bg-red-800 bg-opacity-75">
            subjudul Cina
        </span>
    </a>
    <a x-show="item.dvd_id && item.has_english_subtitle" :href="itemUrl(item)" :alt="item.dvd_id">
        <span class="absolute bottom-1 left-1 rounded-lg px-2 py-1 text-xs text-nord5 bg-red-800 bg-opacity-75">
            Subtitle bahasa inggris
        </span>
    </a>
    <a x-show="item.dvd_id && ! (isChinese && item.has_chinese_subtitle) && ! (! isChinese && item.has_english_subtitle) && item.is_uncensored_leak" :href="itemUrl(item)" :alt="item.dvd_id">
        <span class="absolute bottom-1 left-1 rounded-lg px-2 py-1 text-xs text-nord5 bg-blue-800 bg-opacity-75">
            Tanpa sensor
        </span>
    </a>
    <a x-show="item.dvd_id" :href="itemUrl(item)" :alt="item.dvd_id">
        <span class="absolute bottom-1 right-1 rounded-lg px-2 py-1 text-xs text-nord5 bg-gray-800 bg-opacity-75">
            <span x-text="Math.floor(item.duration / 3600)"></span>:<span x-text="Math.floor(item.duration / 60 % 60).toString().padStart(2, '0')"></span>:<span x-text="(item.duration % 60).toString().padStart(2, '0')"></span>
        </span>
    </a>
</div>
            </div>
        </div>
        <div class="flex-1" style="width: 119px;">
            <div class="flex flex-wrap content-center h-full">
                <div class="max-h-14 overflow-y-hidden text-sm">
                    <a
                        x-text="item.full_title"
                        :href="itemUrl(item)"
                        :class="{
                            'text-secondary': showPreview !== item.dvd_id,
                            'text-primary': showPreview === item.dvd_id,
                        }"
                    ></a>
                </div>
            </div>
        </div>
    </div>
</template>
                            </div>
        </div>
        <div class="flex-1 order-first">
                                    <div
    x-init="$nextTick(() => {
        if (window.hash && window.hash.includes(':')) {
            initClip()
            extractClipTime(clipTime)

            if (clipStartSeconds > 0 && clipEndSeconds > 0) {
                isLooping = true
            }
        }

        $watch('clipStartTime', () => {
            extractClipTime(clipTime)
            updateHash()
        })

        $watch('clipEndTime', () => {
            extractClipTime(clipTime)
            updateHash()
        })

        document.addEventListener('playing', () => {
            if (isJumped) {
                return
            }

            window.player.currentTime = clipStartSeconds
            isJumped = true
        })

        document.addEventListener('timeupdate', () => {
            if (! clipEndSeconds) {
                return
            }

            if (isLooping && Math.round(window.player.currentTime) < clipStartSeconds) {
                window.player.currentTime = clipStartSeconds
            }

            if (isLooping && Math.round(window.player.currentTime) > clipEndSeconds + 1) {
                window.player.currentTime = clipStartSeconds
            }
        })

        if (window.isPublished && window.user_uuid) {
            window.recombeeClient.send(new recombee.AddDetailView(window.user_uuid, 'sdde-740', {
                recommId: window.currentRecommendId,
            }))
        }
    })"
    x-data="{
        baseUrl: 'https://missav.ai/dm39/id/sdde-740',
        directUrls: JSON.parse('[\u0022https:\\\/\\\/tsyndicate.com\\\/api\\\/v1\\\/direct\\\/c65c80b49bf04c139b29dba8137a6419\u0022,\u0022https:\\\/\\\/tsyndicate.com\\\/api\\\/v1\\\/direct\\\/c65c80b49bf04c139b29dba8137a6419?2\u0022,\u0022https:\\\/\\\/tsyndicate.com\\\/api\\\/v1\\\/direct\\\/c65c80b49bf04c139b29dba8137a6419?3\u0022,\u0022https:\\\/\\\/tsyndicate.com\\\/api\\\/v1\\\/direct\\\/c65c80b49bf04c139b29dba8137a6419?4\u0022,\u0022https:\\\/\\\/tsyndicate.com\\\/api\\\/v1\\\/direct\\\/c65c80b49bf04c139b29dba8137a6419?5\u0022,\u0022https:\\\/\\\/tsyndicate.com\\\/api\\\/v1\\\/direct\\\/c65c80b49bf04c139b29dba8137a6419?6\u0022,\u0022https:\\\/\\\/tsyndicate.com\\\/api\\\/v1\\\/direct\\\/c65c80b49bf04c139b29dba8137a6419?7\u0022,\u0022https:\\\/\\\/tsyndicate.com\\\/api\\\/v1\\\/direct\\\/c65c80b49bf04c139b29dba8137a6419?8\u0022,\u0022https:\\\/\\\/ad.twinrdengine.com\\\/adraw?zone=01DXF6DT004000000000001JGA\\u0026kw=COMMA_SEPARATED_KEYWORDS\u0022,\u0022https:\\\/\\\/diffusedpassionquaking.com\\\/1919723\\\/\u0022,\u0022https:\\\/\\\/diffusedpassionquaking.com\\\/1919723\\\/?2\u0022,\u0022https:\\\/\\\/diffusedpassionquaking.com\\\/1919723\\\/?3\u0022,\u0022https:\\\/\\\/go.myavlive.com\\\/easy?campaignId=ff0b763268d316cf081ec8d3bd43ab3e5d8dd09b8fb261d4a3f1a3449ba4186b\\u0026userId=050103608cf9b4d04684e5804b8637ff881d466e3ceaf77c1cc78be33cb1f3fe\u0022]'),
        directUrlsIphone: JSON.parse('[\u0022https:\\\/\\\/tsyndicate.com\\\/api\\\/v1\\\/direct\\\/c65c80b49bf04c139b29dba8137a6419\u0022,\u0022https:\\\/\\\/tsyndicate.com\\\/api\\\/v1\\\/direct\\\/c65c80b49bf04c139b29dba8137a6419?2\u0022,\u0022https:\\\/\\\/tsyndicate.com\\\/api\\\/v1\\\/direct\\\/c65c80b49bf04c139b29dba8137a6419?3\u0022,\u0022https:\\\/\\\/tsyndicate.com\\\/api\\\/v1\\\/direct\\\/c65c80b49bf04c139b29dba8137a6419?4\u0022,\u0022https:\\\/\\\/tsyndicate.com\\\/api\\\/v1\\\/direct\\\/c65c80b49bf04c139b29dba8137a6419?5\u0022,\u0022https:\\\/\\\/tsyndicate.com\\\/api\\\/v1\\\/direct\\\/c65c80b49bf04c139b29dba8137a6419?6\u0022,\u0022https:\\\/\\\/tsyndicate.com\\\/api\\\/v1\\\/direct\\\/c65c80b49bf04c139b29dba8137a6419?7\u0022,\u0022https:\\\/\\\/tsyndicate.com\\\/api\\\/v1\\\/direct\\\/c65c80b49bf04c139b29dba8137a6419?8\u0022,\u0022https:\\\/\\\/ad.twinrdengine.com\\\/adraw?zone=01DXF6DT004000000000001JGA\\u0026kw=COMMA_SEPARATED_KEYWORDS\u0022,\u0022https:\\\/\\\/diffusedpassionquaking.com\\\/1919723\\\/\u0022,\u0022https:\\\/\\\/diffusedpassionquaking.com\\\/1919723\\\/?2\u0022,\u0022https:\\\/\\\/diffusedpassionquaking.com\\\/1919723\\\/?3\u0022,\u0022https:\\\/\\\/go.myavlive.com\\\/easy?campaignId=ff0b763268d316cf081ec8d3bd43ab3e5d8dd09b8fb261d4a3f1a3449ba4186b\\u0026userId=050103608cf9b4d04684e5804b8637ff881d466e3ceaf77c1cc78be33cb1f3fe\u0022]'),
        popped: $persist({
            urls: [],
            timestamp: 0,
        }).using(sessionStorage),
        popOnce: false,
        isJumped: false,
        isLooping: false,
        clipTime: '',
        clipStartTime: '',
        clipEndTime: '',
        clipStartSeconds: 0,
        clipEndSeconds: 0,
        initClip() {
            const hash = decodeURI(window.location.hash.substr(1))

            this.clipTime = hash;

            if (this.clipTime.includes('-')) {
                const clipTimeSegments = this.clipTime.split('-')

                this.clipStartTime = clipTimeSegments[0]
                this.clipEndTime = clipTimeSegments[1]
            } else {
                this.clipStartTime = this.clipTime
                this.clipEndTime = ''
            }
        },
        extractClipTime(clipTime) {
            const extractTimePosition = position => {
                const clipTime = this[`clip${position}Time`]

                if (! clipTime) {
                    return 0
                }

                const timeSegments = clipTime.split(':').reverse().map(segment => parseInt(segment))

                let seconds = 0, i = 0

                for (i = 0; i < timeSegments.length; i++) {
                    if (i === 0) {
                        seconds += timeSegments[i]
                    } else if (i === 1) {
                        seconds += timeSegments[i] * 60
                    } else if (i === 2) {
                        seconds += timeSegments[i] * 3600
                    }
                }

                return seconds
            }

            this.clipStartSeconds = extractTimePosition('Start')
            this.clipEndSeconds = extractTimePosition('End')

            if (this.clipStartSeconds >= this.clipEndSeconds) {
                this.clipEndSeconds = 0
            }
        },
        jumpToClip() {
            this.isJumped = false

            if (this.clipEndSeconds) {
                this.looping = true
            }

            window.player.currentTime = this.clipStartSeconds
            window.player.play()
        },
        copyCurrentTime(field) {
            const currentTime = Math.floor(window.player.currentTime)

            const hour = Math.floor(currentTime / 3600).toString().padStart(2, '0')
            const minute = Math.floor(currentTime % 3600 / 60).toString().padStart(2, '0')
            const second = (currentTime % 60).toString().padStart(2, '0')

            this[field] = `${hour}:${minute}:${second}`
        },
        isValidClipTime(clipTime) {
            return clipTime && clipTime.match(/^(?:(?:([01]?\d|2[0-3]):)?([0-5]?\d):)?([0-5]?\d)$/)
        },
        updateHash() {
            let hash = this.baseUrl

            if (this.clipStartSeconds > 0 && this.isValidClipTime(this.clipStartTime)) {
                hash += `#${this.clipStartTime}`

                if (this.clipEndSeconds > 0 && this.isValidClipTime(this.clipEndTime)) {
                    hash += `-${this.clipEndTime}`
                }
            }

            history.replaceState(null, null, hash)

            window.dispatchEvent(new CustomEvent('hashUpdated'))
        },
        pop() {
            if (this.popOnce) {
                return
            }

            this.popOnce = true

            if (Date.now() - this.popped.timestamp > 86400 * 1000 && this.popped.urls.length > 0) {
                this.popped.urls = []
            }

            const directUrls = (window.navigator.userAgent.includes('iPhone') ? this.directUrlsIphone : this.directUrls)
                .map(value => ({ value, sort: Math.random() }))
                .sort((a, b) => a.sort - b.sort)
                .map(({ value }) => value)

            const nextDirectUrl = directUrls.find(directUrl => ! this.popped.urls.includes(directUrl))

            if (nextDirectUrl) {
                this.popped.urls.push(nextDirectUrl)
                this.popped.timestamp = Date.now()

                const popWindow = window.open(`/pop?url=${encodeURIComponent(nextDirectUrl)}`, '_blank')

                const timer = setInterval(() => {
                    if (! popWindow || popWindow.closed) {
                        clearInterval(timer)
                        window.player.play()
                    }
                }, 300)
            }
        },
    }"
>
    <div class="relative -mx-4 sm:m-0 -mt-6">
        <div
                            @click="pop()"
                @keyup.space.window="pop()"
                        class="aspect-w-16 aspect-h-9"
        >
            <video
                x-cloak
                controls
                playsinline
                data-poster="https://fourhoi.com/sdde-740/cover-n.jpg"
                preload="none"
                class="player"
                crossorigin="anonymous"
                style="--plyr-color-main: #fe628e; --plyr-captions-background: rgba(0, 0, 0, 0.5);"
            >
                                            </video>
        </div>
    </div>
    <div class="sm:hidden flex justify-between -mx-4 px-4 pt-3 pb-1 bg-black">
        <span class="isolate inline-flex rounded-md shadow-sm">
            <button @click.prevent="window.player.currentTime -= 600" type="button" class="relative inline-flex items-center rounded-l-md bg-transparent pl-2 pr-3 py-2 text-xs font-semibold text-white ring-1 ring-inset ring-white hover:bg-primary focus:z-10">
                <svg class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M15.79 14.77a.75.75 0 01-1.06.02l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 111.04 1.08L11.832 10l3.938 3.71a.75.75 0 01.02 1.06zm-6 0a.75.75 0 01-1.06.02l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 111.04 1.08L5.832 10l3.938 3.71a.75.75 0 01.02 1.06z" clip-rule="evenodd" />
                </svg>
                10m
            </button>
            <button @click.prevent="window.player.currentTime -= 60" type="button" class="relative -ml-px inline-flex items-center bg-transparent px-2 pr-3 py-2 text-xs font-semibold text-white ring-1 ring-inset ring-white hover:bg-primary focus:z-10">
                <svg class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
                </svg>
                1m
            </button>
            <button @click.prevent="window.player.currentTime -= 10" type="button" class="relative -ml-px inline-flex items-center rounded-r-md bg-transparent pl-2 pr-3 py-2 text-xs font-semibold text-white ring-1 ring-inset ring-white hover:bg-primary focus:z-10">
                <svg class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
                </svg>
                10s
            </button>
        </span>
        <span class="isolate inline-flex rounded-md shadow-sm">
            <button @click.prevent="window.player.currentTime += 10" type="button" class="relative inline-flex items-center rounded-l-md bg-transparent pl-3 pr-2 py-2 text-xs font-semibold text-white ring-1 ring-inset ring-white hover:bg-primary focus:z-10">
                10s
                <svg class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                </svg>
            </button>
            <button @click.prevent="window.player.currentTime += 60" type="button" class="relative -ml-px inline-flex items-center bg-transparent pl-3 pr-2 py-2 text-xs font-semibold text-white ring-1 ring-inset ring-white hover:bg-primary focus:z-10">
                1m
                <svg class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                </svg>
            </button>
            <button @click.prevent="window.player.currentTime += 600" type="button" class="relative -ml-px inline-flex items-center rounded-r-md bg-transparent pl-3 pr-2 py-2 text-xs font-semibold text-white ring-1 ring-inset ring-white hover:bg-primary focus:z-10">
                10m
                <svg class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M10.21 14.77a.75.75 0 01.02-1.06L14.168 10 10.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                    <path fill-rule="evenodd" d="M4.21 14.77a.75.75 0 01.02-1.06L8.168 10 4.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                </svg>
            </button>
        </span>
    </div>
    <div class="-mx-4 sm:m-0 px-4 py-2 sm:py-4 bg-black rounded-b-0 sm:rounded-b-lg">
        <div class="flex items-center flex-nowrap leading-5">
            <div class="grow">
                <label for="clip-start-time" class="hidden">Mulai dari</label>
                <input x-model="clipStartTime" type="text" placeholder="00:00:00" id="clip-start-time" class="text-right w-18 sm:w-20 p-0 border-0 border-b border-gray-300 text-sm sm:text-base text-gray-300 font-mono bg-transparent focus:border-transparent focus:ring-0">
                <a @click.prevent="copyCurrentTime('clipStartTime')" href="#">
                    <svg xmlns="http://www.w3.org/2000/svg" class="inline h-4 w-4 sm:h-5 sm:w-5 text-nord13" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                        <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                    </svg>
                </a>
                <svg xmlns="http://www.w3.org/2000/svg" class="inline mx-0 sm:mx-1 h-3 w-3 xs:h-5 xs:w-5 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    <path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
                <label for="clip-end-time" class="hidden">Putar ke</label>
                <input x-model="clipEndTime" type="text" placeholder="00:00:00" id="clip-end-time" class="text-right w-18 sm:w-20 p-0 border-0 border-b border-gray-300 text-sm sm:text-base text-gray-300 font-mono bg-transparent focus:border-transparent focus:ring-0">
                <a @click.prevent="copyCurrentTime('clipEndTime')" href="#">
                    <svg xmlns="http://www.w3.org/2000/svg" class="inline h-4 w-4 sm:h-5 sm:w-5 text-nord13" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                        <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                    </svg>
                </a>
            </div>
            <div class="sm:ml-6">
                <button
                    :class="{
                        'bg-primary': isLooping,
                        'hover:bg-opacity-75': isLooping,
                        'active:bg-nord3': isLooping,
                        'border-1': ! isLooping,
                        'border-white': ! isLooping,
                    }"
                    @click.prevent="isLooping = ! isLooping"
                    type="button"
                    class="inline-flex items-center whitespace-nowrap px-2.5 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white"
                >
                    Perulangan
                    <svg
                        :class="{
                            'text-emerald-400': isLooping,
                            'text-red-500': ! isLooping,
                        }"
                        class="ml-1.5 h-2 w-2"
                        fill="currentColor"
                        viewBox="0 0 8 8"
                    >
                        <circle cx="4" cy="4" r="3" />
                    </svg>
                </button>
            </div>
        </div>
    </div>
</div>
            <div class="mt-4">
                <h1 class="text-base lg:text-lg text-nord6">SDDE-740 [Versi bonus SP] Perhotelan dengan &quot;seragam, pakaian dalam, dan ketelanjangan&quot; mengangkangi penerbangan vagina 2024 edisi pelatihan skala besar CA baru dengan total 11 orang + 1 instruktur khusus 6 bagian pelajaran kabin vagina grup 43 menit sudut bonus ditambahkan! Total durasi: 190 menit, 6+1 bagian - Mirai Sunohara</h1>
                <div class="flex flex-wrap justify-center space-x-4 md:space-x-6 py-8 rounded-md shadow-sm">
                    <button
                        :class="{ 'text-primary': saved, 'text-nord4': ! saved, 'hover:text-nord6': ! saved }"
                        @click.prevent="toggleSave"
                        type="button"
                        class="inline-flex items-center whitespace-nowrap text-sm text-nord4 leading-4 font-medium focus:outline-none"
                    >
                        <svg x-cloak x-show="saved" class="mr-1 md:mr-2 h-3 w-3 xs:h-4 xs:w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                        </svg>
                        <svg x-show="! saved" class="mr-1 md:mr-2 h-3 w-3 xs:h-4 xs:w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12z" />
                        </svg>
                        Menyimpan
                    </button>
                    <button
                        :class="{ 'text-primary': showPanel === 'playlist', 'text-nord4': showPanel !== 'playlist', 'hover:text-nord6': showPanel !== 'playlist' }"
                        @click.prevent="togglePlaylist"
                        type="button"
                        class="inline-flex items-center whitespace-nowrap shadow-sm text-sm text-nord4 leading-4 font-medium focus:outline-none"
                    >
                        <svg class="mr-1 md:mr-2 h-3 w-3 xs:h-4 xs:w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm-.375 5.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                        </svg>
                        Daftar putar
                    </button>
                                        <button
                        :class="{ 'text-primary': showPanel === 'share', 'text-nord4': showPanel !== 'share', 'hover:text-nord6': showPanel !== 'share' }"
                        @click.prevent="togglePanel('share')"
                        type="button"
                        class="inline-flex items-center whitespace-nowrap shadow-sm text-sm text-nord4 leading-4 font-medium focus:outline-none"
                    >
                        <svg class="mr-1 md:mr-2 h-3 w-3 xs:h-4 xs:w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
                        </svg>
                        Membagikan
                    </button>
                                            <div x-data="{ showDropdown: false }" @click.outside="showDropdown = false" class="inline-flex items-center">
                            <button @click="showDropdown = ! showDropdown" type="button" class="relative inline-flex items-center shadown-sm text-sm leading-4 font-medium focus:z-10 focus:outline-none text-nord4 hover:text-nord6" id="download-option-menu-button" aria-expanded="true" aria-haspopup="true">
                                <span class="sr-only">Menu</span>
                                <svg class="h-4 w-4 xs:h-6 xs:w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM12.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM18.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0z" />
                                </svg>
                            </button>
                            <div class="relative">
                                <div x-cloak x-show="showDropdown" class="absolute top-2 right-0 z-10 mt-2 -mr-1 w-auto origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none" role="menu" aria-orientation="vertical" aria-labelledby="download-option-menu-button" tabindex="-1">
                                    <div class="py-1" role="none">
                                                                                    <a href="https://missav.ai/id/sdde-740-uncensored-leak" class="hover:bg-gray-100 hover:text-gray-900 text-gray-700 block px-4 py-2 text-sm whitespace-nowrap" role="menuitem" tabindex="-1" id="option-menu-item-1">
                                                Beralih ke tanpa sensor
                                            </a>
                                                                                                                                                            </div>
                                </div>
                            </div>
                        </div>
                                    </div>
            </div>
            <div
    x-cloak
    x-show="showPanel === 'share'"
    x-init="$nextTick(() => {
        window.addEventListener('hashUpdated', () => {
            shareUrl = window.location.href
        })
    })"
    x-data="{
        copied: false,
        shareUrl: window.location.href,
        copyShareUrl() {
            this.$refs.shareUrl.select()

            document.execCommand('copy')

            this.copied = true

            setTimeout(() => {
                this.copied = false
            }, 2000)
        },
    }"
    class="mb-5 p-3 pb-0 sm:p-6 sm:pb-3 bg-gray-800 rounded-lg space-y-3"
>
    <div class="flex rounded-md shadow-sm">
        <div class="relative flex items-stretch grow focus-within:z-10">
            <label for="share-url" class="hidden"></label>
            <input
                x-ref="shareUrl"
                :value="shareUrl"
                type="text"
                id="share-url"
                class="bg-gray-900 focus:ring-gray-500 focus:border-gray-500 block w-full rounded-none rounded-l-md text-nord4 text-sm border-gray-700"
                readonly
            >
        </div>
        <button @click.prevent="copyShareUrl" type="button" class="relative inline-flex items-center space-x-2 px-4 py-2 border border-gray-700 text-nord6 text-sm font-medium rounded-r-md bg-gray-700 hover:bg-gray-600 focus:outline-none focus:ring-1 focus:ring-gray-500 focus:border-gray-500">
            <div>
                <svg x-cloak x-show="copied" class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <svg x-show="! copied" class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M7 9a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9z" />
                    <path d="M5 3a2 2 0 00-2 2v6a2 2 0 002 2V5h8a2 2 0 00-2-2H5z" />
                </svg>
            </div>
            <span class="whitespace-nowrap">Menyalin</span>
        </button>
    </div>
    <div>
        <a
    :href="`https://wa.me/?text=${shareTitle}%0a%0a${shareUrl}`"
    target="_blank"
    style="background: #25D366;"
    class="inline-flex items-center px-3 py-2 border border-transparent shadow-sm leading-4 font-medium rounded-md text-white mr-1 sm:mr-3 mb-3 text-xs sm:text-sm"
>
    <svg aria-hidden="true" focusable="false" data-prefix="fab" data-icon="whatsapp" class="-ml-0.5 mr-2 h-3 w-3 sm:h-4 sm:w-4" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                    <path fill="currentColor" d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"></path>
                </svg>
    Whatsapp
</a>
        <a
    :href="`https://t.me/share/url?url=${shareUrl}&amp;text=${shareTitle}`"
    target="_blank"
    style="background: #0088CC;"
    class="inline-flex items-center px-3 py-2 border border-transparent shadow-sm leading-4 font-medium rounded-md text-white mr-1 sm:mr-3 mb-3 text-xs sm:text-sm"
>
    <svg aria-hidden="true" focusable="false" data-prefix="fab" data-icon="telegram-plane" class="-ml-0.5 mr-2 h-3 w-3 sm:h-4 sm:w-4" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                    <path fill="currentColor" d="M446.7 98.6l-67.6 318.8c-5.1 22.5-18.4 28.1-37.3 17.5l-103-75.9-49.7 47.8c-5.5 5.5-10.1 10.1-20.7 10.1l7.4-104.9 190.9-172.5c8.3-7.4-1.8-11.5-12.9-4.1L117.8 284 16.2 252.2c-22.1-6.9-22.5-22.1 4.6-32.7L418.2 66.4c18.4-6.9 34.5 4.1 28.5 32.2z"></path>
                </svg>
    Telegram
</a>
        <a
    :href="`https://twitter.com/intent/tweet?text=${shareTitle}%0a%0a${shareUrl}`"
    target="_blank"
    style="background: #1DA1F2;"
    class="inline-flex items-center px-3 py-2 border border-transparent shadow-sm leading-4 font-medium rounded-md text-white mr-1 sm:mr-3 mb-3 text-xs sm:text-sm"
>
    <svg aria-hidden="true" focusable="false" data-prefix="fab" data-icon="twitter" class="-ml-0.5 mr-2 h-3 w-3 sm:h-4 sm:w-4" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                    <path fill="currentColor" d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z"></path>
                </svg>
    Twitter
</a>
    </div>
</div>
            <div
    x-cloak
    x-show="showPanel === 'playlist'"
    x-init="$nextTick(() => {
        $watch('showPanel', value => {
            if (value === 'playlist' && ! loading.playlist) {
                loading.playlist = true

                window.axios.get('https://missav.ai/api/playlists/sdde-740').then(response => {
                    playlists = response.data.data

                    loading.playlist = false
                })
            }
        })
    })"
    x-data="{
        loading: {
            playlist: false,
        },
        playlists: [],
        togglePlaylist(key) {
            const data = {
                dvdId: 'sdde-740',
                key: key,
            }

            if (event.target.checked) {
                window.axios.post('https://missav.ai/api/playlists/add', data)
            } else {
                window.axios.post('https://missav.ai/api/playlists/remove', data)
            }
        },
    }"
    class="mb-5 p-3 pb-1 sm:p-6 sm:pb-4 bg-gray-800 rounded-lg"
>
    <div class="justify-center">
        <svg x-cloak x-show="loading.playlist" class="animate-spin mx-auto h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
</svg>
    </div>
    <fieldset x-cloak x-show="! loading.playlist">
        <template x-for="playlist in playlists">
            <div class="relative flex items-start mb-3">
                <div class="flex h-5 items-center">
                    <input
                        x-model="playlist.is_added"
                        :id="playlist.key"
                        @click="togglePlaylist(playlist.key)"
                        type="checkbox"
                        class="focus:ring-primary h-4 w-4 text-primary border-gray-300 rounded"
                    >
                    <div class="ml-3">
                        <label x-text="playlist.name" :for="playlist.key" class="text-nord4 cursor-pointer"></label>
                    </div>
                </div>
            </div>
        </template>
        <hr x-cloak x-show="playlists.length" class="mb-2 border-gray-700">
        <a href="https://missav.ai/id/playlists/create/sdde-740" class="inline-flex items-center text-nord4 hover:text-nord6 font-medium">
            <svg class="-ml-1 mr-2 h-6 w-6" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            Buat daftar putar
        </a>
    </fieldset>
</div>
                        <div class="sm:mx-0 mb-8 rounded-0 sm:rounded-lg">
                <div class="mb-4 border-b border-gray-700">
                    <nav class="-mb-px flex space-x-2 sm:space-x-3" aria-label="Tabs">
                        <a
            @click.prevent="() => {
            currentTab = 'video_details'
            
        }"
        href="#"
    :class="{
        'border-red-400': currentTab === 'video_details',
        'text-red-400': currentTab === 'video_details',
        'border-transparent': currentTab !== 'video_details',
        'text-gray-500': currentTab !== 'video_details',
        'hover:text-nord4': currentTab !== 'video_details',
        'hover:border-gray-400': currentTab !== 'video_details',
    }"
    class="group inline-flex items-center whitespace-nowrap pb-4 px-2 border-b-2 border-transparent font-medium text-sm text-gray-500 border-red-400 text-red-400"
    aria-current="page"
>
    <svg
        :class="{
            'text-red-400': currentTab === 'video_details',
            'text-gray-500': currentTab !== 'video_details',
            'group-hover:text-nord4': currentTab !== 'video_details',
        }"
        class="hidden sm:block -ml-0.5 mr-2 h-5 w-5 text-gray-500 text-red-400"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 20 20"
        fill="currentColor"
    >
        <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
    </svg>
    <span>Detail</span>
</a>
                                                                    </nav>
                </div>
                <div>
                    <div x-show="currentTab === 'video_details'">
                        <div>
            <div
            x-data="{
                showMore: false,
            }"
            class="mb-4"
        >
            <div
                :class="{
                    'line-clamp-none': showMore,
                    'line-clamp-2': ! showMore,
                }"
                class="mb-1 text-secondary break-all line-clamp-2"
            >
                [Edisi bonus SP termasuk sudut khusus] Musim pelatihan karyawan baru penerbangan vagina telah tiba pada tahun 2024! Seperti biasa, silakan nikmati situs pelatihan penerbangan straddle terbesar yang diselenggarakan oleh total 11 CA dan instruktur baru! #kostumbanyakorang #pramugari #asli #kostumasli #celanaketat #sapatelanjang #airways #airlines
            </div>
            <div>
                <a
                    :class="{ hidden: showMore }"
                    @click.prevent="showMore = true"
                    href="#"
                    class="text-nord13 font-medium flex items-center"
                >
                    <svg class="w-4 h-4 -ml-1 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    Menampilkan lebih banyak
                </a>
            </div>
        </div>
        <ul class="mb-4 list-none text-nord14 grid grid-cols-2 gap-2">
                            <li>
                <a href="https://bit.ly/4kDQS0T" class="text-nord14" target="_blank" rel="sponsored nofollow noopener noreferrer">
                    전국의아름다운아가씨예약되 되며
                </a>
            </li>
                    <li>
                <a href="https://bit.ly/43hOHKs" class="text-nord14" target="_blank" rel="sponsored nofollow noopener noreferrer">
                    91福利 免费看片
                </a>
            </li>
                    <li>
                <a href="https://bit.ly/4iUp079" class="text-nord14" target="_blank" rel="sponsored nofollow noopener noreferrer">
                    全国空降迷情春药
                </a>
            </li>
                <li>
            <a href="https://myavlive.com/?userId=050103608cf9b4d04684e5804b8637ff881d466e3ceaf77c1cc78be33cb1f3fe" rel="nofollow" class="text-nord14" target="_blank">
                Seks Kamera Langsung
            </a>
        </li>
    </ul>
    <div class="space-y-2">
                    <div class="text-secondary">
                <span>Tanggal rilis:</span>
                <time datetime="2024-11-19T10:00:29+08:00" class="font-medium">2024-11-19</time>
            </div>
                <div class="text-secondary">
            <span>Kode:</span>
            <span class="font-medium">SDDE-740</span>
        </div>
                    <div class="text-secondary">
                <span>Judul:</span>
                <span class="font-medium">【SP特典版】「制服・下着・全裸」でおもてなし またがりオマ○コ航空 圧巻総勢11名＋特別講師1名による2024年度新人CA大型研修編 6セクションの集団おま〇こキャビンレッスン 特典コーナー43分追加！ 総尺190分収録 6＋1セクション</span>
            </div>
                                                        <div class="text-secondary">
                    <span>Aktris:</span>
                    <a href="https://missav.ai/dm27/id/actresses/Mirai%20Sunohara" class="text-nord13 font-medium">Mirai Sunohara</a>, <a href="https://missav.ai/dm18/id/actresses/kuncup%20bunga%20alice" class="text-nord13 font-medium">kuncup bunga alice</a>, <a href="https://missav.ai/dm18/id/actresses/Oikawa%20Umi" class="text-nord13 font-medium">Oikawa Umi</a>, <a href="https://missav.ai/dm13/id/actresses/Seika%20Igarashi" class="text-nord13 font-medium">Seika Igarashi</a>, <a href="https://missav.ai/dm13/id/actresses/Remi%20Natsume" class="text-nord13 font-medium">Remi Natsume</a>, <a href="https://missav.ai/dm13/id/actresses/Hibino%20Uta" class="text-nord13 font-medium">Hibino Uta</a>, <a href="https://missav.ai/id/actresses/Nanase%20Sinon" class="text-nord13 font-medium">Nanase Sinon</a>, <a href="https://missav.ai/id/actresses/Riru%20Asano" class="text-nord13 font-medium">Riru Asano</a>, <a href="https://missav.ai/id/actresses/Amami%20Mea" class="text-nord13 font-medium">Amami Mea</a>, <a href="https://missav.ai/id/actresses/Rita%20Minase" class="text-nord13 font-medium">Rita Minase</a>, <a href="https://missav.ai/id/actresses/Haru%20Ando" class="text-nord13 font-medium">Haru Ando</a>, <a href="https://missav.ai/id/actresses/Michikuten" class="text-nord13 font-medium">Michikuten</a>
                </div>
                                                                                            <div class="text-secondary">
                    <span>Genre:</span>
                    <a href="https://missav.ai/dm902/id/genres/seragam" class="text-nord13 font-medium">seragam</a>, <a href="https://missav.ai/dm80/id/genres/pramugari" class="text-nord13 font-medium">pramugari</a>, <a href="https://missav.ai/dm110/id/genres/bokong%20besar" class="text-nord13 font-medium">bokong besar</a>, <a href="https://missav.ai/dm480/id/genres/Cowgirl" class="text-nord13 font-medium">Cowgirl</a>, <a href="https://missav.ai/dm282/id/genres/Harem" class="text-nord13 font-medium">Harem</a>, <a href="https://missav.ai/dm95/id/genres/hi-vision" class="text-nord13 font-medium">hi-vision</a>, <a href="https://missav.ai/dm50/id/genres/4K" class="text-nord13 font-medium">4K</a>
                </div>
                                                            <div class="text-secondary">
                    <span>Seri:</span>
                    <a href="https://missav.ai/id/series/%E3%80%8C%E5%88%B6%E6%9C%8D%E3%83%BB%E4%B8%8B%E7%9D%80%E3%83%BB%E5%85%A8%E8%A3%B8%E3%80%8D%E3%81%A7%E3%81%8A%E3%82%82%E3%81%A6%E3%81%AA%E3%81%97%20%E3%81%BE%E3%81%9F%E3%81%8C%E3%82%8A%E3%82%AA%E3%83%9E%E2%97%8B%E3%82%B3%E8%88%AA%E7%A9%BA" class="text-nord13 font-medium">「制服・下着・全裸」でおもてなし またがりオマ○コ航空</a>
                </div>
                                                            <div class="text-secondary">
                    <span>Pembuat:</span>
                    <a href="https://missav.ai/dm2/id/makers/SOD" class="text-nord13 font-medium">SOD</a>
                </div>
                                                            <div class="text-secondary">
                    <span>Direktur:</span>
                    <a href="https://missav.ai/id/directors/%E5%9D%82%E4%BA%95%E3%82%B7%E3%83%99%E3%83%AA%E3%82%A2" class="text-nord13 font-medium">坂井シベリア</a>
                </div>
                                                            <div class="text-secondary">
                    <span>Label:</span>
                    <a href="https://missav.ai/id/labels/SENZ" class="text-nord13 font-medium">SENZ</a>
                </div>
                                                        </div>
</div>
                    </div>
                                                        </div>
            </div>
                        <div class="relative overflow-hidden">
                                    <div
                        x-init="$nextTick(() => {
                            let containerWidth = $refs.stripchat.offsetWidth

                            let height, rows, columns

                            if (document.documentElement.clientWidth >= 1280) {
                                height = Math.round((((containerWidth - 60 - 40) / 4) / 16 * 9) + 40)
                                rows = 1
                                columns = 4
                            } else if (document.documentElement.clientWidth >= 768) {
                                height = Math.round((((containerWidth - 40 - 30) / 3) / 16 * 9) + 40)
                                rows = 1
                                columns = 3
                            } else {
                                height = Math.round((((containerWidth - 20 - 20) / 2) / 16 * 9) + 34)
                                rows = 1
                                columns = 2
                            }

                            $refs.stripchat.innerHTML = `<iframe width='100%' height='${height}' style='display:block' marginWidth='0' marginHeight='0' frameBorder='no' src='https://creative.myavlive.com/widgets/v4/Universal?thumbsMargin=20&gridRows=${rows}&gridColumns=${columns}&responsive=0&hideButton=1&hideTitle=1&userId=050103608cf9b4d04684e5804b8637ff881d466e3ceaf77c1cc78be33cb1f3fe&campaignId=under_player&tag=girls/chinese'></iframe>`
                        })"
                        x-ref="stripchat"
                        class="-m-5 mb-2"
                    ></div>
                                <div class="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-5">
                    <template
    x-for="(item, index) in recommendItems.slice(
        isDesktop() ? 13 : 0,
        isDesktop()
            ? 29
            : 16
    ).map(item => {
        item = generateFullItemTitle(item)

        item.scenario = item.scenario
            ? item.scenario
            : isDesktop() ? 'internal-desktop-watch-next-bottom' : 'internal-mobile-watch-next'

        return item
    })"
>
    <div
        @mouseenter="item.dvd_id ? setPreview(`bottom-${item.dvd_id}`) : null"
        @mouseleave="item.dvd_id ? setPreview() : null"
        @click="item.dvd_id ? clickPreview(`bottom-${item.dvd_id}`) : null"
        :class="{
            'md:hidden': index >= 12,
            'xl:block': index >= 12,
        }"
        class="thumbnail group"
    >
        <div class="relative aspect-w-16 aspect-h-9 rounded overflow-hidden shadow-lg">
    <a :href="item.dvd_id ? itemUrl(item) : 'javascript:;'" :alt="item.dvd_id">
        <video
            x-cloak
            :data-src="item.dvd_id ? cdnUrl(`/${item.dvd_id}/preview.mp4`) : 'javascript:;'"
            :id="`preview-bottom-${item.dvd_id}`"
            :class="{ hidden: showPreview !== `bottom-${item.dvd_id}` && ! holdPreviews.includes(`bottom-${item.dvd_id}`) }"
            class="preview hidden"
            loop
            muted
            playsinline
        ></video>
        <img
            x-cloak
            :data-src="item.dvd_id ? cdnUrl(`/${item.dvd_id}/cover-t.jpg`) : 'javascript:;'"
            :class="{ hidden: showPreview === `bottom-${item.dvd_id}` || holdPreviews.includes(`bottom-${item.dvd_id}`) }"
            :alt="item.title"
            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mN09omrBwADNQFuUCqPAwAAAABJRU5ErkJggg=="
            class="lozad w-full"
        >
    </a>
    <a x-show="item.dvd_id && isChinese && item.has_chinese_subtitle" :href="itemUrl(item)" :alt="item.dvd_id">
        <span class="absolute bottom-1 left-1 rounded-lg px-2 py-1 text-xs text-nord5 bg-red-800 bg-opacity-75">
            subjudul Cina
        </span>
    </a>
    <a x-show="item.dvd_id && item.has_english_subtitle" :href="itemUrl(item)" :alt="item.dvd_id">
        <span class="absolute bottom-1 left-1 rounded-lg px-2 py-1 text-xs text-nord5 bg-red-800 bg-opacity-75">
            Subtitle bahasa inggris
        </span>
    </a>
    <a x-show="item.dvd_id && ! (isChinese && item.has_chinese_subtitle) && ! (! isChinese && item.has_english_subtitle) && item.is_uncensored_leak" :href="itemUrl(item)" :alt="item.dvd_id">
        <span class="absolute bottom-1 left-1 rounded-lg px-2 py-1 text-xs text-nord5 bg-blue-800 bg-opacity-75">
            Tanpa sensor
        </span>
    </a>
    <a x-show="item.dvd_id" :href="itemUrl(item)" :alt="item.dvd_id">
        <span class="absolute bottom-1 right-1 rounded-lg px-2 py-1 text-xs text-nord5 bg-gray-800 bg-opacity-75">
            <span x-text="Math.floor(item.duration / 3600)"></span>:<span x-text="Math.floor(item.duration / 60 % 60).toString().padStart(2, '0')"></span>:<span x-text="(item.duration % 60).toString().padStart(2, '0')"></span>
        </span>
    </a>
</div>
        <div class="my-2 text-sm text-nord4 truncate">
            <a
                x-text="item.full_title"
                :href="itemUrl(item)"
                class="text-secondary group-hover:text-primary"
            ></a>
        </div>
    </div>
</template>
                </div>
            </div>
        </div>
    </div>
            </div>
            <!--/sse-->
            <div class="mb-5 lg:mb-10">
    <a @click.prevent="window.scrollTo(0, 0)" href="#">
        <span class="sr-only">Kembali ke atas</span>
        <svg class="w-8 h-8 mx-auto text-nord6 hover:text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 11l3-3m0 0l3 3m-3-3v8m0-13a9 9 0 110 18 9 9 0 010-18z"></path>
        </svg>
    </a>
</div>

    <div class="space-y-5 mb-5">
                            <div
    class="
            "
>
    <div class="mx-auto" style="width: 300px; height: 250px; overflow: hidden;">
    <div id="ts_ad_video_aes67"></div>
    <script src="//cdn.tsyndicate.com/sdk/v1/outstream.video.js"></script>
    <script>
        TSOutstreamVideo({
            spot: "8bf9578a20b84e78bedf4927ad1dabb8",
            containerId: "ts_ad_video_aes67",
            cookieExpires: "4",
        });
    </script>
</div>
</div>
                        </div>
    
<footer aria-labelledby="footerHeading" class="sm:container mx-auto px-4">
    <div class="max-w-7xl mx-auto py-12 lg:py-16">
        <h2 id="footerHeading" class="sr-only">Catatan kaki</h2>
        <div class="xl:grid xl:grid-cols-3 xl:gap-8">
            <div class="space-y-4 xl:col-span-1">
                <a class="text-4xl leading-normal" href="https://missav.ai/id">
                    <span style="visibility: hidden;" class="font-serif"><span class="text-zinc-50">MISS</span><span class="text-primary">AV</span></span>
                </a>
                <p class="text-gray-500 text-base">
                    Situs porno AV Jepang terbaik, gratis selamanya, kecepatan tinggi, tanpa jeda, lebih dari 100.000 video, pembaruan harian, tanpa iklan saat memutar video.
                </p>
                <div id="inpage" class="text-gray-900"></div>
            </div>
            <div class="mt-12 grid grid-cols-2 gap-8 xl:mt-0 xl:col-span-2">
                <div class="md:grid md:grid-cols-2 md:gap-8">
                    <div>
                        <h3 class="text-sm font-semibold text-gray-400 tracking-wider uppercase">
                            Video
                        </h3>
                        <ul class="mt-4 space-y-4">
                            <li>
                                <a href="https://missav.ai/id/new" class="text-base text-gray-500 hover:text-primary">
                                    Recent update
                                </a>
                            </li>
                            <li>
                                <a href="https://missav.ai/id/release" class="text-base text-gray-500 hover:text-primary">
                                    Keluaran terbaru
                                </a>
                            </li>
                            <li>
                                <a href="https://missav.ai/id/uncensored-leak" class="text-base text-gray-500 hover:text-primary">
                                    Kebocoran tanpa sensor
                                </a>
                            </li>
                                                            <li>
                                    <a href="https://missav.ai/id/english-subtitle" class="text-base text-gray-500 hover:text-primary">
                                        Subtitle bahasa inggris
                                    </a>
                                </li>
                                                    </ul>
                    </div>
                    <div class="mt-12 md:mt-0">
                        <h3 class="text-sm font-semibold text-gray-400 tracking-wider uppercase">
                            Mencari
                        </h3>
                        <ul class="mt-4 space-y-4">
                                                            <li>
                                    <a href="https://missav.ai/id/actresses" class="text-base text-gray-500 hover:text-primary">
                                        Aktris
                                    </a>
                                </li>
                                                            <li>
                                    <a href="https://missav.ai/id/genres" class="text-base text-gray-500 hover:text-primary">
                                        Genre
                                    </a>
                                </li>
                                                            <li>
                                    <a href="https://missav.ai/id/makers" class="text-base text-gray-500 hover:text-primary">
                                        Pembuat
                                    </a>
                                </li>
                                                    </ul>
                    </div>
                </div>
                <div class="md:grid md:grid-cols-2 md:gap-8">
                    <div>
                        <h3 class="text-sm font-semibold text-gray-400 tracking-wider uppercase">
                            Tautan
                        </h3>
                        <ul class="mt-4 space-y-4">
                                                                                        <li>
                                    <a href="https://missav.ai/id/contact" class="text-base text-gray-500 hover:text-primary">
                                        Kontak
                                    </a>
                                </li>
                                                            <li>
                                    <a href="https://missav.ai/id/ads" class="text-base text-gray-500 hover:text-primary">
                                        Permintaan iklan
                                    </a>
                                </li>
                                                            <li>
                                    <a href="https://missav.ai/id/terms" class="text-base text-gray-500 hover:text-primary">
                                        Ketentuan
                                    </a>
                                </li>
                                                            <li>
                                    <a href="https://missav.ai/id/upload" class="text-base text-gray-500 hover:text-primary">
                                        Mengunggah video
                                    </a>
                                </li>
                                                    </ul>
                    </div>
                    <div class="mt-12 md:mt-0">
                        <h3 class="text-sm font-semibold text-gray-400 tracking-wider uppercase">
                            Lihat juga
                        </h3>
                        <ul class="mt-4 space-y-4">
                                                                                                                                                <li>
                                <a
                                    href="https://myavlive.com/?userId=050103608cf9b4d04684e5804b8637ff881d466e3ceaf77c1cc78be33cb1f3fe"
                                    class="text-base text-gray-500 hover:text-primary"
                                    rel="nofollow"
                                    target="_blank"
                                >
                                    Seks Kamera Langsung
                                </a>
                            </li>
                                                            <li>
                                    <a href="https://missav.ai/id/site/123av" class="text-base text-gray-500 hover:text-primary">
                                        123Av
                                    </a>
                                </li>
                                                            <li>
                                    <a href="https://missav.ai/id/site/njav" class="text-base text-gray-500 hover:text-primary">
                                        Njav
                                    </a>
                                </li>
                                                            <li>
                                    <a href="https://missav.ai/id/site/supjav" class="text-base text-gray-500 hover:text-primary">
                                        Supjav
                                    </a>
                                </li>
                                                        <li class="">
                        <a href="https://jerkdolls.com/" rel="sponsored nofollow noopener noreferrer" target="_target" class="text-base text-gray-500 hover:text-primary">
                JerkDolls
            </a>
                            </li>
                                                            <li class="">
                        <a href="https://theporndude.com/" rel="sponsored nofollow noopener noreferrer" target="_target" class="text-base text-gray-500 hover:text-primary">
                ThePornDude
            </a>
                            </li>
                                                    </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="my-12 border-t border-gray-700 pt-8">
            <p class="flex justify-center items-center text-base text-gray-400 xl:text-center">
                <a href="https://missav.ai/dm">&copy; 2025</a>
                <a class="ml-1 align-middle text-lg" href="/"><span style="visibility: hidden;" class="font-serif"><span class="text-zinc-50">MISS</span><span class="text-primary">AV</span></span>
</a>
            </p>
        </div>
    </div>
</footer>
        </div>
                    <div id="html-ads"></div>
            <script type="text/javascript">
                
                let htmlAds = []
                let htmlAdIndexes = []

                                    htmlAds.push(() => {
    const script = document.createElement('script')

    script.type = 'text/javascript'
    script.src = '//hartattenuate.com/62/bd/ca/62bdca270715b3b43fbac98597c038f1.js'
    script.async = true

    document.head.appendChild(script)
})
                    htmlAdIndexes.push(0, 0, 0)
                                    htmlAds.push(() => {
    window.addEventListener('DOMContentLoaded', () => {
        const script = document.createElement('script')

        script.type = 'text/javascript'
        script.src = 'https://creative.myavlive.com/widgets/Spot/lib.js'
        script.id = 'SCSpotScript'
        script.async = true

        document.head.appendChild(script)

        const init = () => {
            if (window.StripchatSpot) {
                new StripchatSpot({
                    autoplay: 'all',
                    userId: '050103608cf9b4d04684e5804b8637ff881d466e3ceaf77c1cc78be33cb1f3fe',
                    campaignId: 'inpage',
                    tag: 'girls/japanese',
                    hideButton: 1,
                    autoclose: 0,
                    closeButtonDelay: 1,
                    quality: '240p',
                    width: 300,
                    height: window.innerWidth > 640 ? 150 : 100,
                }).mount(document.body)
            } else {
                setTimeout(() => {
                    init()
                }, 100)
            }
        }

        init()
    })
})
                    htmlAdIndexes.push(1, 1, 1, 1, 1, 1)
                
                function shuffle(array) {
                    let currentIndex = array.length
                    let randomIndex

                    while (currentIndex !== 0) {
                        randomIndex = Math.floor(Math.random() * currentIndex)
                        currentIndex--

                        [array[currentIndex], array[randomIndex]] = [array[randomIndex], array[currentIndex]]
                    }

                    return array
                }

                shuffle(htmlAdIndexes)
            </script>
                    <script src="https://cdnjs.cloudflare.com/ajax/libs/plyr/3.6.8/plyr.min.js" defer></script>
    <script src="https://missav.ai/js/plyr-plugin-thumbnail.js" defer></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/hls.js/1.4.3/hls.min.js" defer></script>
    <script type="text/javascript">
        window.isPublished = true

        window.hash = window.location.hash.slice(1)

        if (window.hash.includes('internal')) {
            window.scenario = window.hash
            window.currentRecommendId = null
        } else if (window.hash.includes('_') && (window.hash.split('_')[0].length === 32 || window.hash.split('_')[0].length === 36)) {
            window.scenario = window.hash.split('_')[1]
            window.currentRecommendId = window.hash.split('_')[0]
        } else if (window.hash && (window.hash.length === 32 || window.hash.length === 36)) {
            window.scenario = null
            window.currentRecommendId = window.hash
        } else {
            window.scenario = null
            window.currentRecommendId = null
        }

        if (window.hash && ! window.hash.includes(':')) {
            window.history.replaceState(null, null, ' ')
        }

        window.dataLayer.push({
            event: 'videoVisit',
            item: {
                dvd_id: 'sdde-740',
            },
        })

        if (window.scenario) {
            window.dataLayer.push({
                event: 'recommendationVisit',
                recommendation: {
                    scenario: window.scenario,
                },
            })
        }

        document.addEventListener('DOMContentLoaded', () => {
            let source
            let isPreviewing = false

                            eval(function(p,a,c,k,e,d){e=function(c){return c.toString(36)};if(!''.replace(/^/,String)){while(c--){d[c.toString(a)]=k[c]||c.toString(a)}k=[function(e){return d[e]}];e=function(){return'\\w+'};c=1};while(c--){if(k[c]){p=p.replace(new RegExp('\\b'+e(c)+'\\b','g'),k[c])}}return p}('f=\'8://7.6/5-4-3-2-1/e.0\';d=\'8://7.6/5-4-3-2-1/c/9.0\';b=\'8://7.6/5-4-3-2-1/a/9.0\';',16,16,'m3u8|ed1542ba8e1d|80e7|47e5|c873|f12501a4|com|surrit|https|video|1080p|source1280|720p|source842|playlist|source'.split('|'),0,{}))
            
            const video = document.querySelector('video.player')

            const initialPlayerEvent = () => {
                setTimeout(() => {
                    window.player.speed = 2

                    setTimeout(() => {
                        window.player.speed = 1
                    }, 50)
                }, 50)

                window.player.on('play', () => {
                    if (! hasPlayed) {
                        if (window.hls) {
                            window.hls.startLoad(-1)
                        }

                        hasPlayed = true

                        window.dataLayer.push({
                            event: 'videoPlay',
                            item: {
                                dvd_id: 'sdde-740',
                            },
                        })
                    }
                })

                window.player.on('enterfullscreen', () => {
                    screen.orientation.lock('landscape').catch(() => {})

                    setHlsDefaultLevel()
                })

                window.player.on('exitfullscreen', () => {
                    screen.orientation.lock('portrait').catch(() => {})

                    setHlsDefaultLevel()
                })

                let converted = false

                window.player.on('progress', (event) => {
                    if (! window.isPublished || converted || ! window.user_uuid) {
                        return
                    }

                    if (event.timeStamp > 100000) {
                        converted = true

                        window.recombeeClient.send(new recombee.AddPurchase(window.user_uuid, 'sdde-740', {
                            cascadeCreate: false,
                            recommId: window.currentRecommendId,
                        }))
                    }
                })

                if (! window.hls) {
                    let resetPlayerCallback = null

                    window.player.on('stalled', () => {
                        let source = window.player.source
                        let oldCurrentTime = 0
                        let newCurrentTime = 0

                        if (window.player.playing) {
                            oldCurrentTime = window.player.currentTime

                            if (resetPlayerCallback) {
                                clearTimeout(resetPlayerCallback)
                            }

                            resetPlayerCallback = setTimeout(oldCurrentTime => {
                                newCurrentTime = window.player.currentTime

                                if (oldCurrentTime === newCurrentTime) {
                                    let presevedTime = window.player.currentTime

                                    window.player.once('canplay', () => {
                                        window.player.currentTime = presevedTime
                                    })

                                    video.src = ''
                                    video.src = source

                                    window.player.play()
                                }
                            }, 500, oldCurrentTime)
                        }
                    })
                }

                document.querySelector('[data-plyr=mute]').addEventListener('click', () => {
                    if (! window.player.muted && window.player.volume === 0) {
                        window.player.volume = 100
                    }
                })
            }

            const setHlsDefaultLevel = () => {
                if (! window.hls) {
                    return
                }

                window.hls.currentLevel = window.hls.levels.findIndex((level, levelIndex) =>
                    level.width + 20 > window.innerWidth || levelIndex === window.hls.levels.length - 1
                )
            }

            let hasPlayed = false

            let playerSettings = {
                controls: [
                    'play-large',
                    'rewind',
                    'play',
                    'fast-forward',
                    'progress',
                    'current-time',
                    'duration',
                    'mute',
                    'captions',
                    'settings',
                    'pip',
                    'fullscreen',
                    'volume',
                ],
                fullscreen: {
                    enabled: true,
                    fallback: true,
                    iosNative: true,
                    container: null,
                },
                speed: {
                    selected: 1,
                    options: [0.25, 0.5, 1, 1.25, 1.5, 2],
                },
                i18n: {
                    speed: 'Kecepatan',
                    normal: 'Normal',
                    quality: 'Kualitas',
                    qualityLabel: {
                        0: 'Mobil',
                    },
                },
                thumbnail: {
                                            enabled: true,
                        pic_num: 5754,
                        width: 300,
                        height: 168,
                        col: 6,
                        row: 6,
                        offsetX: 0,
                        offsetY: 0,
                        urls: ["https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_0.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_1.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_2.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_3.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_4.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_5.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_6.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_7.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_8.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_9.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_10.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_11.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_12.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_13.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_14.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_15.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_16.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_17.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_18.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_19.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_20.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_21.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_22.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_23.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_24.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_25.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_26.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_27.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_28.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_29.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_30.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_31.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_32.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_33.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_34.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_35.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_36.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_37.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_38.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_39.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_40.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_41.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_42.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_43.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_44.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_45.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_46.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_47.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_48.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_49.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_50.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_51.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_52.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_53.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_54.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_55.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_56.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_57.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_58.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_59.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_60.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_61.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_62.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_63.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_64.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_65.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_66.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_67.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_68.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_69.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_70.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_71.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_72.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_73.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_74.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_75.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_76.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_77.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_78.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_79.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_80.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_81.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_82.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_83.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_84.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_85.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_86.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_87.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_88.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_89.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_90.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_91.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_92.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_93.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_94.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_95.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_96.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_97.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_98.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_99.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_100.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_101.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_102.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_103.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_104.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_105.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_106.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_107.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_108.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_109.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_110.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_111.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_112.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_113.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_114.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_115.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_116.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_117.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_118.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_119.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_120.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_121.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_122.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_123.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_124.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_125.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_126.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_127.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_128.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_129.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_130.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_131.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_132.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_133.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_134.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_135.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_136.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_137.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_138.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_139.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_140.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_141.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_142.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_143.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_144.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_145.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_146.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_147.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_148.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_149.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_150.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_151.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_152.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_153.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_154.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_155.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_156.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_157.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_158.jpg","https:\/\/nineyu.com\/f12501a4-c873-47e5-80e7-ed1542ba8e1d\/seek\/_159.jpg"],
                                    },
                keyboard: {
                    focused: true,
                    global: true,
                },
                            };

            if (isPreviewing) {
                window.player = new Plyr(video, playerSettings)

                initialPlayerEvent()
            } else if (! Hls.isSupported()) {
                window.player = new Plyr(video, playerSettings)

                video.src = source842

                initialPlayerEvent()
            } else if (
                window.navigator.userAgent.includes('iPad') ||
                (window.navigator.userAgent.includes('Macintosh') && navigator.maxTouchPoints && navigator.maxTouchPoints > 1)
            ) {
                window.player = new Plyr(video, playerSettings)

                video.src = source1280

                initialPlayerEvent()
            } else {
                let parsedManifest = false

                window.hls = new Hls({
                    autoStartLoad: true,
                    maxBufferSize: 1 * 1000 * 1000,
                })

                hls.on(Hls.Events.ERROR, (event, data) => {
                    if (! parsedManifest && data.networkDetails.status === 429) {
                        hls.loadSource(source)
                    }
                })

                hls.loadSource(source)

                hls.on(Hls.Events.MANIFEST_PARSED, () => {
                    parsedManifest = true

                    window.player = new Plyr(video, {
                        quality: {
                            forced: true,
                            default: 0,
                            options: [...window.hls.levels.map(level => level.height).reverse(), 0],
                            onChange: height => {
                                if (height === 0) {
                                    setHlsDefaultLevel()
                                } else {
                                    window.hls.levels.forEach((level, levelIndex) => {
                                        if (level.height === height) {
                                            window.hls.currentLevel = levelIndex
                                        }
                                    })
                                }
                            },
                        },
                        ...playerSettings,
                    })

                    initialPlayerEvent()
                })

                hls.attachMedia(video)
            }

            document.addEventListener('visibilitychange', () => {
                if (window.player && document.hidden) {
                    window.player.pause()
                }
            })

            document.addEventListener('blur', () => {
                if (window.player) {
                    window.player.pause()
                }
            })

            window.addEventListener('blur', () => {
                if (window.player) {
                    window.player.pause()
                }
            })

            window.addEventListener('resize', () => {
                setHlsDefaultLevel()
            })

            window.addEventListener('orientationchange', () => {
                setHlsDefaultLevel()
            })
        })
    </script>
                    <script type="text/javascript">
                if (htmlAds[htmlAdIndexes[0]]) {
                    htmlAds[htmlAdIndexes[0]]()
                }
            </script>
                            <script type="text/javascript">
                
                eval(function(p,a,c,k,e,d){e=function(c){return(c<a?'':e(parseInt(c/a)))+((c=c%a)>35?String.fromCharCode(c+29):c.toString(36))};if(!''.replace(/^/,String)){while(c--){d[e(c)]=k[c]||e(c)}k=[function(e){return d[e]}];e=function(){return'\\w+'};c=1};while(c--){if(k[c]){p=p.replace(new RegExp('\\b'+e(c)+'\\b','g'),k[c])}}return p}('Y(R(p,a,c,k,e,d){e=R(c){S(c<a?\'\':e(1d(c/a)))+((c=c%a)>1c?T.1b(c+1a):c.12(Z))};W(!\'\'.V(/^/,T)){U(c--){d[e(c)]=k[c]||e(c)}k=[R(e){S d[e]}];e=R(){S\'\\\\w+\'};c=1};U(c--){W(k[c]){p=p.V(10 11(\'\\\\b\'+e(c)+\'\\\\b\',\'g\'),k[c])}}S p}(\'Q(A(p,a,c,k,e,d){e=A(c){x c.D(P)};B(!\\\'\\\'.C(/^/,O)){F(c--){d[c.D(a)]=k[c]||c.D(a)}k=[A(e){x d[e]}];e=A(){x\\\'\\\\\\\\w+\\\'};c=1};F(c--){B(k[c]){p=p.C(N M(\\\'\\\\\\\\b\\\'+e(c)+\\\'\\\\\\\\b\\\',\\\'g\\\'),k[c])}}x p}(\\\'u(![\\\\\\\'m\\\\\\\'+\\\\\\\'.\\\\\\\'+\\\\\\\'t\\\\\\\'+\\\\\\\'h\\\\\\\'+\\\\\\\'i\\\\\\\'+\\\\\\\'s\\\\\\\'+\\\\\\\'.\\\\\\\'+\\\\\\\'a\\\\\\\'+\\\\\\\'v\\\\\\\',\\\\\\\'m\\\\\\\'+\\\\\\\'i\\\\\\\'+\\\\\\\'s\\\\\\\'+\\\\\\\'s\\\\\\\'+\\\\\\\'a\\\\\\\'+\\\\\\\'v\\\\\\\'+\\\\\\\'1\\\\\\\'+\\\\\\\'2\\\\\\\'+\\\\\\\'3\\\\\\\'+\\\\\\\'.\\\\\\\'+\\\\\\\'c\\\\\\\'+\\\\\\\'o\\\\\\\'+\\\\\\\'m\\\\\\\',\\\\\\\'m\\\\\\\'+\\\\\\\'i\\\\\\\'+\\\\\\\'s\\\\\\\'+\\\\\\\'s\\\\\\\'+\\\\\\\'a\\\\\\\'+\\\\\\\'v\\\\\\\'+\\\\\\\'8\\\\\\\'+\\\\\\\'8\\\\\\\'+\\\\\\\'8\\\\\\\'+\\\\\\\'.\\\\\\\'+\\\\\\\'c\\\\\\\'+\\\\\\\'o\\\\\\\'+\\\\\\\'m\\\\\\\',\\\\\\\'k\\\\\\\'+\\\\\\\'i\\\\\\\'+\\\\\\\'d\\\\\\\'+\\\\\\\'d\\\\\\\'+\\\\\\\'e\\\\\\\'+\\\\\\\'w\\\\\\\'+\\\\\\\'.\\\\\\\'+\\\\\\\'c\\\\\\\'+\\\\\\\'o\\\\\\\'+\\\\\\\'m\\\\\\\',\\\\\\\'1\\\\\\\'+\\\\\\\'2\\\\\\\'+\\\\\\\'3\\\\\\\'+\\\\\\\'a\\\\\\\'+\\\\\\\'v\\\\\\\'+\\\\\\\'.\\\\\\\'+\\\\\\\'o\\\\\\\'+\\\\\\\'r\\\\\\\'+\\\\\\\'g\\\\\\\',\\\\\\\'m\\\\\\\'+\\\\\\\'i\\\\\\\'+\\\\\\\'s\\\\\\\'+\\\\\\\'s\\\\\\\'+\\\\\\\'a\\\\\\\'+\\\\\\\'v\\\\\\\'+\\\\\\\'7\\\\\\\'+\\\\\\\'8\\\\\\\'+\\\\\\\'9\\\\\\\'+\\\\\\\'.\\\\\\\'+\\\\\\\'c\\\\\\\'+\\\\\\\'o\\\\\\\'+\\\\\\\'m\\\\\\\',\\\\\\\'m\\\\\\\'+\\\\\\\'q\\\\\\\'+\\\\\\\'a\\\\\\\'+\\\\\\\'v\\\\\\\'+\\\\\\\'.\\\\\\\'+\\\\\\\'c\\\\\\\'+\\\\\\\'o\\\\\\\'+\\\\\\\'m\\\\\\\',\\\\\\\'m\\\\\\\'+\\\\\\\'i\\\\\\\'+\\\\\\\'s\\\\\\\'+\\\\\\\'s\\\\\\\'+\\\\\\\'a\\\\\\\'+\\\\\\\'v\\\\\\\'+\\\\\\\'.\\\\\\\'+\\\\\\\'l\\\\\\\'+\\\\\\\'i\\\\\\\'+\\\\\\\'v\\\\\\\'+\\\\\\\'e\\\\\\\',\\\\\\\'t\\\\\\\'+\\\\\\\'h\\\\\\\'+\\\\\\\'i\\\\\\\'+\\\\\\\'s\\\\\\\'+\\\\\\\'a\\\\\\\'+\\\\\\\'v\\\\\\\'+\\\\\\\'2\\\\\\\'+\\\\\\\'.\\\\\\\'+\\\\\\\'c\\\\\\\'+\\\\\\\'o\\\\\\\'+\\\\\\\'m\\\\\\\',\\\\\\\'n\\\\\\\'+\\\\\\\'j\\\\\\\'+\\\\\\\'a\\\\\\\'+\\\\\\\'v\\\\\\\'+\\\\\\\'t\\\\\\\'+\\\\\\\'v\\\\\\\'+\\\\\\\'.\\\\\\\'+\\\\\\\'c\\\\\\\'+\\\\\\\'o\\\\\\\'+\\\\\\\'m\\\\\\\',\\\\\\\'m\\\\\\\'+\\\\\\\'i\\\\\\\'+\\\\\\\'s\\\\\\\'+\\\\\\\'s\\\\\\\'+\\\\\\\'a\\\\\\\'+\\\\\\\'v\\\\\\\'+\\\\\\\'0\\\\\\\'+\\\\\\\'1\\\\\\\'+\\\\\\\'.\\\\\\\'+\\\\\\\'c\\\\\\\'+\\\\\\\'o\\\\\\\'+\\\\\\\'m\\\\\\\',\\\\\\\'m\\\\\\\'+\\\\\\\'i\\\\\\\'+\\\\\\\'s\\\\\\\'+\\\\\\\'s\\\\\\\'+\\\\\\\'a\\\\\\\'+\\\\\\\'v\\\\\\\'+\\\\\\\'.\\\\\\\'+\\\\\\\'a\\\\\\\'+\\\\\\\'i\\\\\\\',\\\\\\\'m\\\\\\\'+\\\\\\\'i\\\\\\\'+\\\\\\\'s\\\\\\\'+\\\\\\\'s\\\\\\\'+\\\\\\\'a\\\\\\\'+\\\\\\\'v\\\\\\\'+\\\\\\\'.\\\\\\\'+\\\\\\\'w\\\\\\\'+\\\\\\\'s\\\\\\\'].p(5.4.6)){5.4.b=5.4.b.f(5.4.6,\\\\\\\'m\\\\\\\'+\\\\\\\'i\\\\\\\'+\\\\\\\'s\\\\\\\'+\\\\\\\'s\\\\\\\'+\\\\\\\'a\\\\\\\'+\\\\\\\'v\\\\\\\'+\\\\\\\'.\\\\\\\'+\\\\\\\'a\\\\\\\'+\\\\\\\'i\\\\\\\')}\\\',E,E,\\\'||||L|K|J|||||I||z||C||||||||||H|y||||B||\\\'.G(\\\'|\\\'),0,{}))\',13,13,\'|||||||||||||||||||||||||||||||||S|||R|W|V|12|19|U|X|18|17|16|15|14|11|10|T|Z|Y\'.X(\'|\'),0,{}))',62,76,'|||||||||||||||||||||||||||||||||||||||||||||||||||||function|return|String|while|replace|if|split|eval|36|new|RegExp|toString|53|location|window|host|href|includes|33|29|fromCharCode|35|parseInt'.split('|'),0,{}))

            </script>
            <script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'946bb168be05d753',t:'MTc0ODQxMzcxNy4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>
</html>
